const supabase = require('./supabaseClient');
const { isPast } = require('date-fns');
const { customRound } = require('./util');
const { mapSymbol } = require('./symbol-mapper');

// Cache configuration
const CACHE_TTL = 5 * 60 * 1000; // 5 minutes
const cache = new Map();

/**
 * Generic database operation wrapper with error handling
 * @param {Function} operation - Database operation to perform
 * @param {string} errorMessage - Custom error message
 * @returns {Promise<*>} - Operation result
 */
async function executeDbOperation(operation, errorMessage) {
  try {
    const { data, error } = await operation();
    if (error) throw new Error(error.message);
    return data;
  } catch (error) {
    console.error(`${errorMessage}:`, error.message);
    throw error;
  }
}

/**
 * Cache wrapper for database operations
 * @param {string} key - Cache key
 * @param {Function} operation - Database operation
 * @param {number} ttl - Time to live in ms
 * @returns {Promise<*>} - Operation result
 */
async function withCache(key, operation, ttl = CACHE_TTL) {
  const cached = cache.get(key);
  if (cached && cached.timestamp > Date.now() - ttl) {
    return cached.data;
  }
  const data = await operation();
  cache.set(key, { data, timestamp: Date.now() });
  return data;
}

/**
 * Query builder for common operations
 */
const QueryBuilder = {
  select: (table) => ({
    where: (conditions) => ({
      single: () => supabase.from(table).select('*').match(conditions).single(),
      many: () => supabase.from(table).select('*').match(conditions),
    }),
    all: () => supabase.from(table).select('*'),
  }),
  upsert: (table, data) => supabase.from(table).upsert([data]),
  delete: (table, conditions) => supabase.from(table).delete().match(conditions),
};

// User Operations
async function saveUserInformation(userAccountInfo) {
  return executeDbOperation(
    () => QueryBuilder.upsert('trade_user_account', userAccountInfo),
    'Error saving user account information'
  );
}

async function findUserBy(field, value) {
  return executeDbOperation(
    () => QueryBuilder.select('trade_user_account').where({ [field]: value }).single(),
    `Error looking up user by ${field}`
  );
}

const lookupUserById = (id) => findUserBy('id', id);
const lookupUserByEmail = (email) => findUserBy('email', email);
const findUserByUUID = (uuid) => findUserBy('uuid', uuid);

// Broker Account Operations
async function saveTradeBrokerAccount(account) {
  return executeDbOperation(
    () => QueryBuilder.upsert('trade_broker_account', account),
    'Error saving trade broker account'
  );
}

async function getTradeBrokerAccount(conditions) {
  return executeDbOperation(
    () => QueryBuilder.select('trade_broker_account').where(conditions).single(),
    'Error retrieving trade broker account'
  );
}

const getTradeBrokerAccountById = (id) => getTradeBrokerAccount({ id });
const getTradeBrokerAccountByLogin = (login) => getTradeBrokerAccount({ login });

async function getTradeBrokerAccountsByUserId(userAccountId) {
  const cacheKey = `broker_accounts_${userAccountId}`;
  return withCache(cacheKey, () =>
    executeDbOperation(
      () => QueryBuilder.select('trade_broker_account').where({ user_account_id: userAccountId }).many(),
      'Error retrieving broker accounts'
    )
  );
}

async function listAllBrokerAccounts(includeInvalid = true) {
  const cacheKey = `all_broker_accounts_${includeInvalid}`;
  return withCache(cacheKey, () =>
    executeDbOperation(
      () => {
        let query = QueryBuilder.select('trade_broker_account');
        if (!includeInvalid) {
          query = query.where({ is_invalid: false });
        }
        return query.many();
      },
      'Error retrieving broker accounts'
    )
  );
}

const listAllValidBrokerAccounts = () => listAllBrokerAccounts(false);

// Bot Operations
async function saveTradeBot(tradeBot) {
  return executeDbOperation(
    () => QueryBuilder.upsert('trade_bot', tradeBot),
    'Error saving trade bot'
  );
}

async function findBotBy(conditions) {
  return executeDbOperation(
    () => QueryBuilder.select('trade_bot').where(conditions).single(),
    'Error finding bot'
  );
}

const findBotById = (id) => findBotBy({ id });
const findSignalBot = (symbol) => findBotBy({ symbol, is_signal_bot: true });

async function findActiveBots(conditions = {}) {
  return executeDbOperation(
    () => QueryBuilder.select('trade_bot').where({ ...conditions, is_active: true }).many(),
    'Error finding active bots'
  );
}

const findAllActiveBots = () => findActiveBots();
const findAllActiveAutoBots = () => findActiveBots({ is_auto_bot: true });
const findAllActiveSignalBots = () => findActiveBots({ is_signal_bot: true });
const findAllActiveSignalBotsBySymbol = (symbol) => findActiveBots({ is_signal_bot: true, symbol });

// Indicator Operations
async function saveIndicatorData(indicator_data) {
  const normalized = {
    ...indicator_data,
    symbol: mapSymbol(indicator_data.symbol),
  };
  
  return executeDbOperation(
    () => QueryBuilder.upsert('indicator_values', normalized),
    'Error saving indicator data'
  );
}

async function findIndicatorValuesBySymbol(symbol) {
  const cacheKey = `indicator_values_${symbol}`;
  return withCache(cacheKey, () =>
    executeDbOperation(
      () => QueryBuilder.select('indicator_values').where({ symbol: mapSymbol(symbol) }).many(),
      'Error retrieving indicator values'
    )
  );
}

async function findIndicatorValuesByTimeframe(timeframe) {
  const cacheKey = `indicator_values_${timeframe}`;
  return withCache(cacheKey, () =>
    executeDbOperation(
      () => QueryBuilder.select('indicator_values').where({ timeframe }).many(),
      'Error retrieving indicator values'
    )
  );
}

async function findIndicatorValuesBySymbolAndTimeframe(symbol, timeframe) {
  const cacheKey = `indicator_values_${symbol}_${timeframe}`;
  return withCache(cacheKey, () =>
    executeDbOperation(
      () => QueryBuilder.select('indicator_values').where({ symbol: mapSymbol(symbol), timeframe }).many(),
      'Error retrieving indicator values'
    )
  );
}

async function findEqualSpaceEma() {
  return executeDbOperation(
    () => QueryBuilder.select('indicator_values').where({ ema_has_equal_space: true }).many(),
    'Error retrieving equal space EMA'
  );
}

async function findSentSignalBySymbolAndTimeframe(symbol, timeframe) {
  return executeDbOperation(
    () => QueryBuilder.select('sent_signal').where({ symbol: mapSymbol(symbol), timeframe }).single(),
    'Error retrieving sent signal'
  );
}

async function saveSentSignal(symbol, timeframe, expire_at) {
  return executeDbOperation(
    () => QueryBuilder.upsert('sent_signal', { symbol: mapSymbol(symbol), timeframe, expire_at }),
    'Error saving sent signal'
  );
}

async function sentSignalExist(symbol, timeframe) {
  const sentSignal = await findSentSignalBySymbolAndTimeframe(symbol, timeframe);
  return sentSignal !== null;
}

async function clearExpiredSentSignal() {
  return executeDbOperation(
    () => supabase.from('sent_signal').delete().lt('expire_at', new Date()),
    'Error clearing expired sent signals'
  );
}

async function findGridSizeBySymbol(symbol) {
  return executeDbOperation(
    () => QueryBuilder.select('trade_grid_size').where({ symbol: mapSymbol(symbol) }).many(),
    'Error retrieving grid size'
  );
}

async function saveGridSize(symbol, grid_size) {
  return executeDbOperation(
    () => QueryBuilder.upsert('trade_grid_size', { symbol: mapSymbol(symbol), grid_size }),
    'Error saving grid size'
  );
}

async function findHedgedOrderByTicket(ticket) {
  return executeDbOperation(
    () => QueryBuilder.select('hedged_orders').where({ ticket }).many(),
    'Error retrieving hedged order'
  );
}

async function findHedgedOrderByTicketAndSymbol(ticket, symbol) {
  return executeDbOperation(
    () => QueryBuilder.select('hedged_orders').where({ ticket, symbol: mapSymbol(symbol) }).many(),
    'Error retrieving hedged order'
  );
}

async function saveHedgedOrder(ticket, take_profit, stop_loss, symbol) {
  return executeDbOperation(
    () => QueryBuilder.upsert('hedged_orders', { ticket, take_profit, stop_loss, symbol: mapSymbol(symbol) }),
    'Error saving hedged order'
  );
}

async function findOBOSWaitlist(symbol, timeframe) {
  return executeDbOperation(
    () => QueryBuilder.select('ob_os_waitlist').where({ symbol: mapSymbol(symbol), timeframe }).many(),
    'Error retrieving OB/OS waitlist'
  );
}

async function registerOverBoughtOverSold(symbol, timeframe, is_overbought, is_oversold) {
  return executeDbOperation(
    () => QueryBuilder.upsert('ob_os_waitlist', { symbol: mapSymbol(symbol), timeframe, is_overbought, is_oversold }),
    'Error registering OB/OS'
  );
}

async function clearOverboughtOverSold(symbol, timeframe) {
  return executeDbOperation(
    () => QueryBuilder.delete('ob_os_waitlist').where({ symbol: mapSymbol(symbol), timeframe }),
    'Error clearing OB/OS'
  );
}

async function findTradeSignalSubscriptionBySymbolAndTimeframe(symbol, timeframe) {
  return executeDbOperation(
    () => QueryBuilder.select('trade_signal_subscription').where({ symbol: mapSymbol(symbol), timeframe }).many(),
    'Error retrieving trade signal subscription'
  );
}

async function findTradeSignalSubscriptionByChatIdSymbolAndTimeframe(chat_id, symbol, timeframe) {
  return executeDbOperation(
    () => QueryBuilder.select('trade_signal_subscription').where({ chat_id, symbol: mapSymbol(symbol), timeframe }).many(),
    'Error retrieving trade signal subscription'
  );
}

async function saveTradeSignalSubscription(symbol, timeframe, chat_id) {
  return executeDbOperation(
    () => QueryBuilder.upsert('trade_signal_subscription', { symbol: mapSymbol(symbol), timeframe, chat_id }),
    'Error saving trade signal subscription'
  );
}

async function deleteTradeSignalSubscription(id) {
  return executeDbOperation(
    () => QueryBuilder.delete('trade_signal_subscription').where({ id }),
    'Error deleting trade signal subscription'
  );
}

async function findCandlestickInformationBySymbolAndTimeframe(symbol, timeframe) {
  return executeDbOperation(
    () => QueryBuilder.select('candlestick_information').where({ symbol: mapSymbol(symbol), timeframe }).many(),
    'Error retrieving candlestick information'
  );
}

async function saveCandlestickInformation(candlestick_information) {
  return executeDbOperation(
    () => QueryBuilder.upsert('candlestick_information', candlestick_information),
    'Error saving candlestick information'
  );
}

async function findAllActiveGridAi() {
  return executeDbOperation(
    () => QueryBuilder.select('grid_ai').where({ is_active: true }).many(),
    'Error retrieving active grid AI'
  );
}

async function findGridAiById(id) {
  return executeDbOperation(
    () => QueryBuilder.select('grid_ai').where({ id }).single(),
    'Error retrieving grid AI'
  );
}

async function findGridAiBySymbolUserIdAndAccountIdAndSymbol(user_id, account_id, symbol) {
  return executeDbOperation(
    () => QueryBuilder.select('grid_ai').where({ user_id, account_id, symbol: mapSymbol(symbol) }).many(),
    'Error retrieving grid AI'
  );
}


async function findGridAiByAccountNumberAndSymbol(account_number, symbol) {
  return executeDbOperation(
    () => QueryBuilder.select('grid_ai').where({ account_number, symbol }).many(),
    'Error retrieving grid AI'
  );
}

async function findPositionAiByAccountNumberAndSymbol(account_number, symbol) {
  return executeDbOperation(
    () =>
      QueryBuilder.select('position_ai').where({ account_number, symbol }).many(),
    'Error retrieving grid AI'
  );
}


async function findTickSizeValue(symbol) {
  return executeDbOperation(
    () => QueryBuilder.select('trade_symbol_amount').where({ symbol: mapSymbol(symbol) }).many(),
    'Error retrieving tick size'
  );
}

async function findAccountNumberByAccountId(account_id) {
  return executeDbOperation(
    () => QueryBuilder.select('trade_broker_account').where({ id: account_id }).many(),
    'Error retrieving account number'
  );
}

async function saveGridAi(grid_ai) {
  return executeDbOperation(
    () => QueryBuilder.upsert('grid_ai', grid_ai),
    'Error saving grid AI'
  );
}


async function savePositionAi(position_ai) {
  return executeDbOperation(
    () => QueryBuilder.upsert('position_ai', position_ai),
    'Error saving Position AI'
  );
}



async function tokenExist(token) {
  return executeDbOperation(
    () => QueryBuilder.select('trade_broker_account').where({ mt5_token: token }).many(),
    'Error checking token existence'
  );
}

module.exports = {
  saveUserInformation,
  lookupUserById,
  lookupUserByEmail,
  findUserByUUID,
  saveTradeBrokerAccount,
  getTradeBrokerAccountById,
  getTradeBrokerAccountByLogin,
  getTradeBrokerAccountsByUserId,
  listAllBrokerAccounts,
  listAllValidBrokerAccounts,
  saveTradeBot,
  findBotById,
  findSignalBot,
  findAllActiveBots,
  findAllActiveAutoBots,
  findAllActiveSignalBots,
  findAllActiveSignalBotsBySymbol,
  saveIndicatorData,
  findIndicatorValuesBySymbol,
  findIndicatorValuesByTimeframe,
  findIndicatorValuesBySymbolAndTimeframe,
  findEqualSpaceEma,
  findSentSignalBySymbolAndTimeframe,
  saveSentSignal,
  sentSignalExist,
  clearExpiredSentSignal,
  findGridSizeBySymbol,
  saveGridSize,
  findHedgedOrderByTicket,
  findHedgedOrderByTicketAndSymbol,
  saveHedgedOrder,
  findOBOSWaitlist,
  registerOverBoughtOverSold,
  clearOverboughtOverSold,
  findTradeSignalSubscriptionBySymbolAndTimeframe,
  findTradeSignalSubscriptionByChatIdSymbolAndTimeframe,
  saveTradeSignalSubscription,
  deleteTradeSignalSubscription,
  findCandlestickInformationBySymbolAndTimeframe,
  saveCandlestickInformation,
  findAllActiveGridAi,
  findGridAiBySymbolUserIdAndAccountIdAndSymbol,
  findGridAiByAccountNumberAndSymbol,
  findPositionAiByAccountNumberAndSymbol,
  findTickSizeValue,
  saveGridAi,
  tokenExist,
  findAccountNumberByAccountId,
  findGridAiById,
  savePositionAi,
};
