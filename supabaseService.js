const supabase = require('./supabaseClient');
const { isPast } = require('date-fns');
const { customRound } = require('./util');
const { mapSymbol } = require('./symbol-mapper');

// Enhanced cache configuration
const CACHE_CONFIG = {
  DEFAULT_TTL: 5 * 60 * 1000, // 5 minutes
  LONG_TTL: 30 * 60 * 1000,   // 30 minutes for rarely changing data
  SHORT_TTL: 1 * 60 * 1000,   // 1 minute for frequently changing data
  MAX_SIZE: 1000,             // Maximum cache entries
  CLEANUP_INTERVAL: 10 * 60 * 1000 // Cleanup every 10 minutes
};

const cache = new Map();
let cacheCleanupTimer = null;

/**
 * Initialize cache cleanup timer
 */
function initializeCacheCleanup() {
  if (cacheCleanupTimer) return;

  cacheCleanupTimer = setInterval(() => {
    const now = Date.now();
    const keysToDelete = [];

    for (const [key, value] of cache.entries()) {
      if (now - value.timestamp > value.ttl) {
        keysToDelete.push(key);
      }
    }

    keysToDelete.forEach(key => cache.delete(key));

    // If cache is too large, remove oldest entries
    if (cache.size > CACHE_CONFIG.MAX_SIZE) {
      const entries = Array.from(cache.entries())
        .sort((a, b) => a[1].timestamp - b[1].timestamp);

      const toRemove = cache.size - CACHE_CONFIG.MAX_SIZE;
      for (let i = 0; i < toRemove; i++) {
        cache.delete(entries[i][0]);
      }
    }
  }, CACHE_CONFIG.CLEANUP_INTERVAL);
}

// Initialize cleanup on module load
initializeCacheCleanup();

/**
 * Enhanced database operation wrapper with error handling and performance tracking
 * @param {Function} operation - Database operation to perform
 * @param {string} errorMessage - Custom error message
 * @param {Object} options - Additional options
 * @returns {Promise<*>} - Operation result
 */
async function executeDbOperation(operation, errorMessage, options = {}) {
  const startTime = Date.now();
  const { enablePerfTracking = false, timeout = 30000 } = options;

  try {
    // Add timeout wrapper for long-running queries
    const timeoutPromise = new Promise((_, reject) => {
      setTimeout(() => reject(new Error('Database operation timeout')), timeout);
    });

    const operationPromise = operation();
    const { data, error } = await Promise.race([operationPromise, timeoutPromise]);

    if (error) throw new Error(error.message);

    // Performance tracking
    if (enablePerfTracking) {
      const duration = Date.now() - startTime;
      console.log(`DB Operation completed in ${duration}ms: ${errorMessage}`);
    }

    return data;
  } catch (error) {
    const duration = Date.now() - startTime;
    console.error(`${errorMessage} (${duration}ms):`, error.message);
    throw error;
  }
}

/**
 * Enhanced cache wrapper for database operations with better invalidation
 * @param {string} key - Cache key
 * @param {Function} operation - Database operation
 * @param {number} ttl - Time to live in ms
 * @param {Object} options - Cache options
 * @returns {Promise<*>} - Operation result
 */
async function withCache(key, operation, ttl = CACHE_CONFIG.DEFAULT_TTL, options = {}) {
  const { forceRefresh = false, tags = [] } = options;

  // Check cache first (unless force refresh)
  if (!forceRefresh) {
    const cached = cache.get(key);
    if (cached && Date.now() - cached.timestamp < cached.ttl) {
      return cached.data;
    }
  }

  // Execute operation and cache result
  const data = await operation();
  cache.set(key, {
    data,
    timestamp: Date.now(),
    ttl,
    tags: tags || []
  });

  return data;
}

/**
 * Invalidate cache entries by tag or pattern
 * @param {string|Array} tags - Tags to invalidate
 * @param {string} pattern - Key pattern to match
 */
function invalidateCache(tags = [], pattern = null) {
  const keysToDelete = [];

  for (const [key, value] of cache.entries()) {
    let shouldDelete = false;

    // Check tags
    if (tags.length > 0 && value.tags) {
      shouldDelete = tags.some(tag => value.tags.includes(tag));
    }

    // Check pattern
    if (pattern && key.includes(pattern)) {
      shouldDelete = true;
    }

    if (shouldDelete) {
      keysToDelete.push(key);
    }
  }

  keysToDelete.forEach(key => cache.delete(key));
  return keysToDelete.length;
}

/**
 * Enhanced query builder for optimized database operations
 */
const QueryBuilder = {
  select: (table, fields = '*') => ({
    where: (conditions) => ({
      single: (options = {}) => {
        let query = supabase.from(table).select(fields).match(conditions);
        if (options.orderBy) query = query.order(options.orderBy.field, { ascending: options.orderBy.ascending });
        return query.single();
      },
      many: (options = {}) => {
        let query = supabase.from(table).select(fields).match(conditions);
        if (options.orderBy) query = query.order(options.orderBy.field, { ascending: options.orderBy.ascending });
        if (options.limit) query = query.limit(options.limit);
        if (options.offset) query = query.range(options.offset, options.offset + (options.limit || 1000) - 1);
        return query;
      },
    }),
    all: (options = {}) => {
      let query = supabase.from(table).select(fields);
      if (options.orderBy) query = query.order(options.orderBy.field, { ascending: options.orderBy.ascending });
      if (options.limit) query = query.limit(options.limit);
      return query;
    },
  }),

  upsert: (table, data, options = {}) => {
    const dataArray = Array.isArray(data) ? data : [data];
    let query = supabase.from(table).upsert(dataArray);
    if (options.onConflict) query = query.onConflict(options.onConflict);
    return query;
  },

  insert: (table, data) => {
    const dataArray = Array.isArray(data) ? data : [data];
    return supabase.from(table).insert(dataArray);
  },

  update: (table, data, conditions) => {
    return supabase.from(table).update(data).match(conditions);
  },

  delete: (table, conditions) => supabase.from(table).delete().match(conditions),

  // Batch operations for better performance
  batchUpsert: (table, dataArray, batchSize = 100) => {
    const batches = [];
    for (let i = 0; i < dataArray.length; i += batchSize) {
      const batch = dataArray.slice(i, i + batchSize);
      batches.push(supabase.from(table).upsert(batch));
    }
    return batches;
  },

  // Count operation
  count: (table, conditions = {}) => {
    return supabase.from(table).select('*', { count: 'exact', head: true }).match(conditions);
  }
};

// User Operations
async function saveUserInformation(userAccountInfo) {
  return executeDbOperation(
    () => QueryBuilder.upsert('trade_user_account', userAccountInfo),
    'Error saving user account information'
  );
}

async function findUserBy(field, value) {
  return executeDbOperation(
    () => QueryBuilder.select('trade_user_account').where({ [field]: value }).single(),
    `Error looking up user by ${field}`
  );
}

const lookupUserById = (id) => findUserBy('id', id);
const lookupUserByEmail = (email) => findUserBy('email', email);
const findUserByUUID = (uuid) => findUserBy('uuid', uuid);

// Broker Account Operations
async function saveTradeBrokerAccount(account) {
  const result = await executeDbOperation(
    () => QueryBuilder.upsert('trade_broker_account', account),
    'Error saving trade broker account'
  );

  // Invalidate related cache entries
  invalidateCache(['broker_accounts']);
  if (account.user_account_id) {
    invalidateCache([`user_${account.user_account_id}`]);
  }

  return result;
}

async function getTradeBrokerAccount(conditions) {
  return executeDbOperation(
    () => QueryBuilder.select('trade_broker_account').where(conditions).single(),
    'Error retrieving trade broker account'
  );
}

const getTradeBrokerAccountById = (id) => getTradeBrokerAccount({ id });
const getTradeBrokerAccountByLogin = (login) => getTradeBrokerAccount({ login });

async function getTradeBrokerAccountsByUserId(userAccountId) {
  const cacheKey = `broker_accounts_${userAccountId}`;
  return withCache(
    cacheKey,
    () => executeDbOperation(
      () => QueryBuilder.select('trade_broker_account', 'id,login,server,user_account_id,is_invalid,created_at')
        .where({ user_account_id: userAccountId })
        .many({ orderBy: { field: 'created_at', ascending: false } }),
      'Error retrieving broker accounts'
    ),
    CACHE_CONFIG.DEFAULT_TTL,
    { tags: ['broker_accounts', `user_${userAccountId}`] }
  );
}

async function listAllBrokerAccounts(includeInvalid = true) {
  const cacheKey = `all_broker_accounts_${includeInvalid}`;
  return withCache(
    cacheKey,
    () => executeDbOperation(
      () => {
        const fields = 'id,login,server,user_account_id,is_invalid,created_at';
        if (includeInvalid) {
          return QueryBuilder.select('trade_broker_account', fields)
            .all({ orderBy: { field: 'created_at', ascending: false } });
        } else {
          return QueryBuilder.select('trade_broker_account', fields)
            .where({ is_invalid: false })
            .many({ orderBy: { field: 'created_at', ascending: false } });
        }
      },
      'Error retrieving broker accounts'
    ),
    CACHE_CONFIG.LONG_TTL, // Longer cache for all accounts
    { tags: ['broker_accounts'] }
  );
}

const listAllValidBrokerAccounts = () => listAllBrokerAccounts(false);

// Bot Operations
async function saveTradeBot(tradeBot) {
  return executeDbOperation(
    () => QueryBuilder.upsert('trade_bot', tradeBot),
    'Error saving trade bot'
  );
}

async function findBotBy(conditions) {
  return executeDbOperation(
    () => QueryBuilder.select('trade_bot').where(conditions).single(),
    'Error finding bot'
  );
}

const findBotById = (id) => findBotBy({ id });
const findSignalBot = (symbol) => findBotBy({ symbol, is_signal_bot: true });

async function findActiveBots(conditions = {}) {
  return executeDbOperation(
    () => QueryBuilder.select('trade_bot').where({ ...conditions, is_active: true }).many(),
    'Error finding active bots'
  );
}

const findAllActiveBots = () => findActiveBots();
const findAllActiveAutoBots = () => findActiveBots({ is_auto_bot: true });
const findAllActiveSignalBots = () => findActiveBots({ is_signal_bot: true });
const findAllActiveSignalBotsBySymbol = (symbol) => findActiveBots({ is_signal_bot: true, symbol });

// Indicator Operations
async function saveIndicatorData(indicator_data) {
  const normalized = {
    ...indicator_data,
    symbol: mapSymbol(indicator_data.symbol),
  };

  const result = await executeDbOperation(
    () => QueryBuilder.upsert('indicator_values', normalized),
    'Error saving indicator data'
  );

  // Invalidate related cache entries
  const mappedSymbol = normalized.symbol;
  invalidateCache(['indicators', `symbol_${mappedSymbol}`]);
  if (normalized.timeframe) {
    invalidateCache([`timeframe_${normalized.timeframe}`]);
  }

  return result;
}

async function findIndicatorValuesBySymbol(symbol) {
  const cacheKey = `indicator_values_${symbol}`;
  const mappedSymbol = mapSymbol(symbol);
  return withCache(
    cacheKey,
    () => executeDbOperation(
      () => QueryBuilder.select('indicator_values', 'id,symbol,timeframe,ema_has_equal_space,created_at')
        .where({ symbol: mappedSymbol })
        .many({
          orderBy: { field: 'created_at', ascending: false },
          limit: 100 // Limit to recent indicators
        }),
      'Error retrieving indicator values'
    ),
    CACHE_CONFIG.SHORT_TTL, // Shorter cache for frequently changing data
    { tags: ['indicators', `symbol_${mappedSymbol}`] }
  );
}

async function findIndicatorValuesByTimeframe(timeframe) {
  const cacheKey = `indicator_values_tf_${timeframe}`;
  return withCache(
    cacheKey,
    () => executeDbOperation(
      () => QueryBuilder.select('indicator_values', 'id,symbol,timeframe,ema_has_equal_space,created_at')
        .where({ timeframe })
        .many({
          orderBy: { field: 'created_at', ascending: false },
          limit: 200 // Limit results
        }),
      'Error retrieving indicator values'
    ),
    CACHE_CONFIG.SHORT_TTL,
    { tags: ['indicators', `timeframe_${timeframe}`] }
  );
}

async function findIndicatorValuesBySymbolAndTimeframe(symbol, timeframe) {
  const cacheKey = `indicator_values_${symbol}_${timeframe}`;
  const mappedSymbol = mapSymbol(symbol);
  return withCache(
    cacheKey,
    () => executeDbOperation(
      () => QueryBuilder.select('indicator_values')
        .where({ symbol: mappedSymbol, timeframe })
        .many({
          orderBy: { field: 'created_at', ascending: false },
          limit: 50
        }),
      'Error retrieving indicator values'
    ),
    CACHE_CONFIG.SHORT_TTL,
    { tags: ['indicators', `symbol_${mappedSymbol}`, `timeframe_${timeframe}`] }
  );
}

async function findEqualSpaceEma() {
  return executeDbOperation(
    () => QueryBuilder.select('indicator_values').where({ ema_has_equal_space: true }).many(),
    'Error retrieving equal space EMA'
  );
}

async function findSentSignalBySymbolAndTimeframe(symbol, timeframe) {
  return executeDbOperation(
    () => QueryBuilder.select('sent_signal').where({ symbol: mapSymbol(symbol), timeframe }).single(),
    'Error retrieving sent signal'
  );
}

async function saveSentSignal(symbol, timeframe, expire_at) {
  return executeDbOperation(
    () => QueryBuilder.upsert('sent_signal', { symbol: mapSymbol(symbol), timeframe, expire_at }),
    'Error saving sent signal'
  );
}

async function sentSignalExist(symbol, timeframe) {
  const sentSignal = await findSentSignalBySymbolAndTimeframe(symbol, timeframe);
  return sentSignal !== null;
}

async function clearExpiredSentSignal() {
  return executeDbOperation(
    () => supabase.from('sent_signal').delete().lt('expire_at', new Date()),
    'Error clearing expired sent signals'
  );
}

async function findGridSizeBySymbol(symbol) {
  return executeDbOperation(
    () => QueryBuilder.select('trade_grid_size').where({ symbol: mapSymbol(symbol) }).many(),
    'Error retrieving grid size'
  );
}

async function saveGridSize(symbol, grid_size) {
  return executeDbOperation(
    () => QueryBuilder.upsert('trade_grid_size', { symbol: mapSymbol(symbol), grid_size }),
    'Error saving grid size'
  );
}

async function findHedgedOrderByTicket(ticket) {
  return executeDbOperation(
    () => QueryBuilder.select('hedged_orders').where({ ticket }).many(),
    'Error retrieving hedged order'
  );
}

async function findHedgedOrderByTicketAndSymbol(ticket, symbol) {
  return executeDbOperation(
    () => QueryBuilder.select('hedged_orders').where({ ticket, symbol: mapSymbol(symbol) }).many(),
    'Error retrieving hedged order'
  );
}

async function saveHedgedOrder(ticket, take_profit, stop_loss, symbol) {
  return executeDbOperation(
    () => QueryBuilder.upsert('hedged_orders', { ticket, take_profit, stop_loss, symbol: mapSymbol(symbol) }),
    'Error saving hedged order'
  );
}

async function findOBOSWaitlist(symbol, timeframe) {
  return executeDbOperation(
    () => QueryBuilder.select('ob_os_waitlist').where({ symbol: mapSymbol(symbol), timeframe }).many(),
    'Error retrieving OB/OS waitlist'
  );
}

async function registerOverBoughtOverSold(symbol, timeframe, is_overbought, is_oversold) {
  return executeDbOperation(
    () => QueryBuilder.upsert('ob_os_waitlist', { symbol: mapSymbol(symbol), timeframe, is_overbought, is_oversold }),
    'Error registering OB/OS'
  );
}

async function clearOverboughtOverSold(symbol, timeframe) {
  return executeDbOperation(
    () => QueryBuilder.delete('ob_os_waitlist').where({ symbol: mapSymbol(symbol), timeframe }),
    'Error clearing OB/OS'
  );
}

async function findTradeSignalSubscriptionBySymbolAndTimeframe(symbol, timeframe) {
  return executeDbOperation(
    () => QueryBuilder.select('trade_signal_subscription').where({ symbol: mapSymbol(symbol), timeframe }).many(),
    'Error retrieving trade signal subscription'
  );
}

async function findTradeSignalSubscriptionByChatIdSymbolAndTimeframe(chat_id, symbol, timeframe) {
  return executeDbOperation(
    () => QueryBuilder.select('trade_signal_subscription').where({ chat_id, symbol: mapSymbol(symbol), timeframe }).many(),
    'Error retrieving trade signal subscription'
  );
}

async function saveTradeSignalSubscription(symbol, timeframe, chat_id) {
  return executeDbOperation(
    () => QueryBuilder.upsert('trade_signal_subscription', { symbol: mapSymbol(symbol), timeframe, chat_id }),
    'Error saving trade signal subscription'
  );
}

async function deleteTradeSignalSubscription(id) {
  return executeDbOperation(
    () => QueryBuilder.delete('trade_signal_subscription').where({ id }),
    'Error deleting trade signal subscription'
  );
}

async function findCandlestickInformationBySymbolAndTimeframe(symbol, timeframe) {
  return executeDbOperation(
    () => QueryBuilder.select('candlestick_information').where({ symbol: mapSymbol(symbol), timeframe }).many(),
    'Error retrieving candlestick information'
  );
}

async function saveCandlestickInformation(candlestick_information) {
  return executeDbOperation(
    () => QueryBuilder.upsert('candlestick_information', candlestick_information),
    'Error saving candlestick information'
  );
}

async function findAllActiveGridAi() {
  return executeDbOperation(
    () => QueryBuilder.select('grid_ai').where({ is_active: true }).many(),
    'Error retrieving active grid AI'
  );
}

async function findGridAiById(id) {
  return executeDbOperation(
    () => QueryBuilder.select('grid_ai').where({ id }).single(),
    'Error retrieving grid AI'
  );
}

async function findGridAiBySymbolUserIdAndAccountIdAndSymbol(user_id, account_id, symbol) {
  return executeDbOperation(
    () => QueryBuilder.select('grid_ai').where({ user_id, account_id, symbol: mapSymbol(symbol) }).many(),
    'Error retrieving grid AI'
  );
}


async function findGridAiByAccountNumberAndSymbol(account_number, symbol) {
  return executeDbOperation(
    () => QueryBuilder.select('grid_ai').where({ account_number, symbol }).many(),
    'Error retrieving grid AI'
  );
}

async function findPositionAiByAccountNumberAndSymbol(account_number, symbol) {
  return executeDbOperation(
    () =>
      QueryBuilder.select('position_ai').where({ account_number, symbol }).many(),
    'Error retrieving grid AI'
  );
}


async function findTickSizeValue(symbol) {
  const cacheKey = `tick_size_${symbol}`;
  const mappedSymbol = mapSymbol(symbol);
  return withCache(
    cacheKey,
    () => executeDbOperation(
      () => QueryBuilder.select('trade_symbol_amount', 'symbol,tick_value,tick_size')
        .where({ symbol: mappedSymbol })
        .many(),
      'Error retrieving tick size'
    ),
    CACHE_CONFIG.LONG_TTL, // Tick sizes rarely change
    { tags: ['tick_sizes', `symbol_${mappedSymbol}`] }
  );
}

async function findAccountNumberByAccountId(account_id) {
  return executeDbOperation(
    () => QueryBuilder.select('trade_broker_account').where({ id: account_id }).many(),
    'Error retrieving account number'
  );
}

async function saveGridAi(grid_ai) {
  const result = await executeDbOperation(
    () => QueryBuilder.upsert('grid_ai', grid_ai),
    'Error saving grid AI'
  );

  // Invalidate related cache entries
  invalidateCache(['grid_ai']);
  if (grid_ai.symbol) {
    invalidateCache([`grid_symbol_${grid_ai.symbol}`]);
  }
  if (grid_ai.account_number) {
    invalidateCache([`grid_account_${grid_ai.account_number}`]);
  }

  return result;
}

async function savePositionAi(position_ai) {
  const result = await executeDbOperation(
    () => QueryBuilder.upsert('position_ai', position_ai),
    'Error saving Position AI'
  );

  // Invalidate related cache entries
  invalidateCache(['position_ai']);
  if (position_ai.symbol) {
    invalidateCache([`position_symbol_${position_ai.symbol}`]);
  }
  if (position_ai.account_number) {
    invalidateCache([`position_account_${position_ai.account_number}`]);
  }

  return result;
}



async function tokenExist(token) {
  const cacheKey = `token_exists_${token}`;
  return withCache(
    cacheKey,
    () => executeDbOperation(
      () => QueryBuilder.select('trade_broker_account', 'id,mt5_token')
        .where({ mt5_token: token })
        .many(),
      'Error checking token existence'
    ),
    CACHE_CONFIG.SHORT_TTL, // Short cache for token validation
    { tags: ['tokens'] }
  );
}

// ============================================================================
// BATCH OPERATIONS - New optimized functions for bulk operations
// ============================================================================

/**
 * Batch save multiple indicator data entries
 * @param {Array} indicatorDataArray - Array of indicator data objects
 * @returns {Promise<Array>} - Array of results
 */
async function batchSaveIndicatorData(indicatorDataArray) {
  const normalized = indicatorDataArray.map(data => ({
    ...data,
    symbol: mapSymbol(data.symbol)
  }));

  const result = await executeDbOperation(
    () => QueryBuilder.upsert('indicator_values', normalized),
    'Error batch saving indicator data'
  );

  // Invalidate cache for all affected symbols
  const symbols = [...new Set(normalized.map(d => d.symbol))];
  symbols.forEach(symbol => {
    invalidateCache(['indicators', `symbol_${symbol}`]);
  });

  return result;
}

/**
 * Batch save multiple grid AI entries
 * @param {Array} gridAiArray - Array of grid AI objects
 * @returns {Promise<Array>} - Array of results
 */
async function batchSaveGridAi(gridAiArray) {
  const result = await executeDbOperation(
    () => QueryBuilder.upsert('grid_ai', gridAiArray),
    'Error batch saving grid AI'
  );

  // Invalidate cache for all affected symbols and accounts
  invalidateCache(['grid_ai']);
  gridAiArray.forEach(grid => {
    if (grid.symbol) invalidateCache([`grid_symbol_${grid.symbol}`]);
    if (grid.account_number) invalidateCache([`grid_account_${grid.account_number}`]);
  });

  return result;
}

/**
 * Get database statistics for monitoring
 * @returns {Promise<Object>} - Database statistics
 */
async function getDatabaseStats() {
  const tables = [
    'trade_user_account',
    'trade_broker_account',
    'trade_bot',
    'indicator_values',
    'grid_ai',
    'position_ai'
  ];

  const stats = {};

  for (const table of tables) {
    try {
      const { count } = await executeDbOperation(
        () => QueryBuilder.count(table),
        `Error getting count for ${table}`
      );
      stats[table] = count;
    } catch (error) {
      stats[table] = 'error';
    }
  }

  stats.cacheSize = cache.size;
  stats.timestamp = new Date().toISOString();

  return stats;
}

/**
 * Clear all cache entries
 * @returns {number} - Number of entries cleared
 */
function clearAllCache() {
  const size = cache.size;
  cache.clear();
  return size;
}

/**
 * Get cache statistics
 * @returns {Object} - Cache statistics
 */
function getCacheStats() {
  const now = Date.now();
  let expiredCount = 0;
  let totalSize = 0;

  for (const [key, value] of cache.entries()) {
    totalSize++;
    if (now - value.timestamp > value.ttl) {
      expiredCount++;
    }
  }

  return {
    totalEntries: totalSize,
    expiredEntries: expiredCount,
    maxSize: CACHE_CONFIG.MAX_SIZE,
    hitRatio: cache.hitRatio || 0, // Would need to implement hit tracking
    memoryUsage: process.memoryUsage()
  };
}

/**
 * Cleanup function to be called on application shutdown
 */
function cleanup() {
  if (cacheCleanupTimer) {
    clearInterval(cacheCleanupTimer);
    cacheCleanupTimer = null;
  }
  cache.clear();
}

module.exports = {
  // User Operations
  saveUserInformation,
  lookupUserById,
  lookupUserByEmail,
  findUserByUUID,

  // Broker Account Operations
  saveTradeBrokerAccount,
  getTradeBrokerAccountById,
  getTradeBrokerAccountByLogin,
  getTradeBrokerAccountsByUserId,
  listAllBrokerAccounts,
  listAllValidBrokerAccounts,

  // Bot Operations
  saveTradeBot,
  findBotById,
  findSignalBot,
  findAllActiveBots,
  findAllActiveAutoBots,
  findAllActiveSignalBots,
  findAllActiveSignalBotsBySymbol,

  // Indicator Operations
  saveIndicatorData,
  findIndicatorValuesBySymbol,
  findIndicatorValuesByTimeframe,
  findIndicatorValuesBySymbolAndTimeframe,
  findEqualSpaceEma,
  batchSaveIndicatorData,

  // Signal Operations
  findSentSignalBySymbolAndTimeframe,
  saveSentSignal,
  sentSignalExist,
  clearExpiredSentSignal,

  // Grid Operations
  findGridSizeBySymbol,
  saveGridSize,

  // Hedged Order Operations
  findHedgedOrderByTicket,
  findHedgedOrderByTicketAndSymbol,
  saveHedgedOrder,

  // OB/OS Operations
  findOBOSWaitlist,
  registerOverBoughtOverSold,
  clearOverboughtOverSold,

  // Trade Signal Subscription Operations
  findTradeSignalSubscriptionBySymbolAndTimeframe,
  findTradeSignalSubscriptionByChatIdSymbolAndTimeframe,
  saveTradeSignalSubscription,
  deleteTradeSignalSubscription,

  // Candlestick Operations
  findCandlestickInformationBySymbolAndTimeframe,
  saveCandlestickInformation,

  // Grid AI Operations
  findAllActiveGridAi,
  findGridAiBySymbolUserIdAndAccountIdAndSymbol,
  findGridAiByAccountNumberAndSymbol,
  findGridAiById,
  saveGridAi,
  batchSaveGridAi,

  // Position AI Operations
  findPositionAiByAccountNumberAndSymbol,
  savePositionAi,

  // Utility Operations
  findTickSizeValue,
  tokenExist,
  findAccountNumberByAccountId,

  // Cache and Performance Operations
  invalidateCache,
  clearAllCache,
  getCacheStats,
  getDatabaseStats,
  cleanup,

  // Core utilities (for advanced usage)
  executeDbOperation,
  withCache,
  QueryBuilder,
};
