const candlesticks = require('./data/candlesticks');
const OrderBlockDetector = require('./lib/orderBlockDetector');


// console.log('Candlesticks:', candlesticks);
const detector = new OrderBlockDetector(candlesticks, {
  minBodyRatio: 0.5, // OB body must be < 50% of previous candle
  minBreakoutStrength: 2 // Breakout candle must be 2x OB body
});
const results = detector.analyze();

console.log(results);