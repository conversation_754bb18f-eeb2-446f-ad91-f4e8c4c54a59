const axios = require('axios');
const NodeCache = require('node-cache');
const rateLimit = require('axios-rate-limit');
const { default: axiosRetry } = require('axios-retry');

// Constants
// const BASE_URL = 'https://mytrademanager.com';
const BASE_URL = 'https://mt5.trademanager.cloud';
// const BASE_URL = 'https://mytrademanager.com';

const DEFAULT_TIMEOUT = 30000; // 30 seconds
const MAX_RETRIES = 3;
const RETRY_DELAY = 1000; // 1 second
const RATE_LIMIT = 50; // requests per second
const CACHE_TTL = 5; // 5 seconds

// Cache configuration
const cache = new NodeCache({
  stdTTL: CACHE_TTL,
  checkperiod: 120,
  useClones: false,
});

// Custom error class
class MT5Error extends Error {
  constructor(message, code, originalError = null) {
    super(message);
    this.name = 'MT5Error';
    this.code = code;
    this.originalError = originalError;
  }
}

// Create axios instance with rate limiting
const http = rateLimit(axios.create({
  baseURL: BASE_URL,
  timeout: DEFAULT_TIMEOUT,
  headers: {
    'Content-Type': 'application/json',
  }
}), { maxRPS: RATE_LIMIT });

// Configure retry logic
axiosRetry(http, {
  retries: MAX_RETRIES,
  retryDelay: (retryCount) => retryCount * RETRY_DELAY,
  retryCondition: (error) => {
    return axiosRetry.isNetworkOrIdempotentRequestError(error) ||
           error.response?.status === 429; // Rate limit exceeded
  }
});

/**
 * Validate required parameters
 * @param {Object} params - Parameters to validate
 * @param {Array} required - List of required parameter names
 * @throws {MT5Error} If required parameters are missing
 */
const validateParams = (params, required) => {
  const missing = required.filter(param => params[param] === undefined);
  if (missing.length > 0) {
    throw new MT5Error(`Missing required parameters: ${missing.join(', ')}`, 'INVALID_PARAMS');
  }
};

/**
 * Make API request with caching and error handling
 * @param {string} path - API endpoint path
 * @param {string} method - HTTP method
 * @param {Object} params - Query parameters
 * @param {Object} data - Request body
 * @param {Object} options - Additional options
 * @returns {Promise} API response
 */
const apiRequest = async (path, method, params = {}, data = {}, options = {}) => {
  const cacheKey = options.cache ? `${path}-${JSON.stringify(params)}-${JSON.stringify(data)}` : null;
  
  try {
    // Check cache
    if (cacheKey && cache.has(cacheKey)) {
      return cache.get(cacheKey);
    }

    // Make request
    const response = await http({
      url: path,
      method,
      params,
      data,
      ...options
    });

    // Cache response if needed
    if (cacheKey) {
      cache.set(cacheKey, response.data);
    }

    return response.data;
  } catch (error) {
    console.error(`[MT5Service] Error making API request to ${path}:`, {
      error: error.message,
      params,
      timestamp: new Date().toISOString()
    });

    if (error.response) {
      throw new MT5Error(
        `API request failed: ${error.response.data?.message || error.message}`,
        error.response.status,
        error
      );
    }

    throw new MT5Error(
      `Network error: ${error.message}`,
      'NETWORK_ERROR',
      error
    );
  }
};

// Connect to account
const connect = async (login, password, host, port) => {
  validateParams({ login, password, host, port }, ['login', 'password', 'host', 'port']);
  return apiRequest('/Connect', 'get', { user: login, password, host, port });
};

// Connect to account with proxy
const connectProxy = async (login, password, host, port, proxyUser, proxyPassword, proxyHost, proxyPort, proxyType) => {
  validateParams(
    { login, password, host, port, proxyUser, proxyPassword, proxyHost, proxyPort, proxyType },
    ['login', 'password', 'host', 'port', 'proxyUser', 'proxyPassword', 'proxyHost', 'proxyPort', 'proxyType']
  );
  return apiRequest('/ConnectProxy', 'get', {
    user: login, password, host, port, proxyUser, proxyPassword, proxyHost, proxyPort, proxyType
  });
};

// Connect to account with POST method
const connectPost = async (login, password, host, port) => {
  validateParams({ login, password, host, port }, ['login', 'password', 'host', 'port']);
  return apiRequest('/ConnectPost', 'post', {}, { user: login, password, host, port });
};

// Check connection state
const checkConnect = async (id) => {
  validateParams({ id }, ['id']);
  return apiRequest('/CheckConnect', 'get', { id }, {}, { cache: true });
};

// Disconnect from account
const disconnect = async (id) => {
  validateParams({ id }, ['id']);
  return apiRequest('/Disconnect', 'get', { id });
};

// Account summary
const accountSummary = async (id) => {
  validateParams({ id }, ['id']);
  return apiRequest('/AccountSummary', 'get', { id }, {}, { cache: true });
};

// List of opened orders
const openedOrders = async (id) => {
  validateParams({ id }, ['id']);
  return apiRequest('/OpenedOrders', 'get', { id }, {}, { cache: true });
};

// Order history
const orderHistory = async (id) => {
  validateParams({ id }, ['id']);
  return apiRequest('/OrderHistory', 'get', { id }, {}, { cache: true });
};

// Latest quote for the specified symbol
const getQuote = async (id, symbol) => {
  validateParams({ id, symbol }, ['id', 'symbol']);
  return apiRequest('/GetQuote', 'get', { id, symbol }, {}, { cache: true });
};

// Full information about symbol and its group
const symbolParams = async (id, symbol) => {
  validateParams({ id, symbol }, ['id', 'symbol']);
  return apiRequest('/SymbolParams', 'get', { id, symbol }, {}, { cache: true });
};

// Server timezone
const serverTimezone = async (id) => {
  validateParams({ id }, ['id']);
  return apiRequest('/ServerTimezone', 'get', { id }, {}, { cache: true });
};

// Check if market is open for specified symbol
const isTradeSession = async (id, symbol) => {
  validateParams({ id, symbol }, ['id', 'symbol']);
  return apiRequest('/IsTradeSession', 'get', { id, symbol }, {}, { cache: true });
};

// List of available symbols
const symbols = async (id) => {
  validateParams({ id }, ['id']);
  return apiRequest('/Symbols', 'get', { id }, {}, { cache: true });
};

// Ping server
const ping = async (id) => {
  return apiRequest('/Ping', 'get', {}, {}, { cache: true });
};

const profitCurrentDay = async (id) => {
  validateParams({ id }, ['id']);
  return apiRequest('/ProfitCurrentDay', 'get', { id }, {}, { cache: true });
};

// Price history for a range
const priceHistory = async (id, symbol, from, to, timeFrame) => {
  validateParams({ id, symbol, from, to, timeFrame }, ['id', 'symbol', 'from', 'to', 'timeFrame']);
  return apiRequest('/PriceHistory', 'get', {
    id, symbol, from, to, timeFrame
  }, {}, { cache: true });
};

// Price history for today
const priceHistoryToday = async (id, symbol, timeFrame) => {
  validateParams({ id, symbol, timeFrame }, ['id', 'symbol', 'timeFrame']);
  // return apiRequest('/PriceHistoryToday', 'get', { id, symbol, timeFrame }, {}, { cache: true });
  return fetch(
    `${BASE_URL}/PriceHistoryToday ?id=${id}&symbol=${symbol}&timeFrame=${timeFrame}`,
  );
};

// Price history for 30 days
const priceHistoryMonth = async (id, symbol, year, month, day, timeFrame) => {
  validateParams({ id, symbol, year, month, day, timeFrame }, ['id', 'symbol', 'year', 'month', 'day', 'timeFrame']);
  return apiRequest('/PriceHistoryMonth', 'get', {
    id, symbol, year, month, day, timeFrame
  }, {}, { cache: true });
};

// Subscribe symbol for real-time quotes
const subscribe = async (id, symbol, interval) => {
  validateParams({ id, symbol, interval }, ['id', 'symbol', 'interval']);
  return apiRequest('/Subscribe', 'get', { id, symbol, interval });
};

const subscribeMany = async (id, symbols, interval) => {
  validateParams({ id, symbols, interval }, ['id', 'symbols', 'interval']);
  return apiRequest('/SubscribeMany', 'get', { id, symbol: symbols, interval });
};

// Unsubscribe symbol for real-time quotes
const unsubscribe = async (id, symbol) => {
  validateParams({ id, symbol }, ['id', 'symbol']);
  return apiRequest('/UnSubscribe', 'get', { id, symbol });
};

const unsubscribeMany = async (id, symbols) => {
  validateParams({ id, symbols }, ['id', 'symbols']);
  return apiRequest('/UnSubscribeMany', 'get', { id, symbol: symbols });
};

// Subscribe for order updates
const subscribeOrderUpdate = async (id) => {
  validateParams({ id }, ['id']);
  return apiRequest('/SubscribeOrderUpdate', 'get', { id });
};

// Subscribe for order profit updates
const subscribeOrderProfit = async (id) => {
  validateParams({ id }, ['id']);
  return apiRequest('/SubscribeOrderProfit', 'get', { id });
};

// Unsubscribe for order profit updates
const unsubscribeOrderProfit = async (id) => {
  validateParams({ id }, ['id']);
  return apiRequest('/UnSubscribeOrderProfit', 'get', { id });
};

const unsubscribeOrderUpdate = async (id) => {
  validateParams({ id }, ['id']);
  return apiRequest('/UnSubscribeOrderUpdate', 'get', { id });
};

// Send market or pending order
const orderSend = async (id, symbol, operation, volume, price, slippage, stoploss, takeprofit, comment, expertID, stopLimitPrice, placedType) => {
  validateParams(
    { id, symbol, operation, volume, price },
    ['id', 'symbol', 'operation', 'volume', 'price']
  );
  
  return apiRequest('/OrderSend', 'get', {
    id, symbol, operation, volume, price, slippage, stoploss, takeprofit, comment, expertID, stopLimitPrice, placedType
  });
};

// Modify market or pending order
const orderModify = async (id, ticket, stoploss, takeprofit, price) => {
  validateParams({ id, ticket }, ['id', 'ticket']);
  return apiRequest('/OrderModify', 'get', {
    id, ticket, stoploss, takeprofit, price
  });
};

// Close order
const orderClose = async (id, ticket, lots, price, slippage) => {
  validateParams({ id, ticket, lots }, ['id', 'ticket', 'lots']);
  return apiRequest('/OrderClose', 'get', {
    id, ticket, lots, price, slippage
  });
};

// Clear cache
const clearCache = () => {
  cache.flushAll();
};

module.exports = {
  connect,
  connectProxy,
  connectPost,
  checkConnect,
  disconnect,
  accountSummary,
  openedOrders,
  orderHistory,
  getQuote,
  symbolParams,
  serverTimezone,
  isTradeSession,
  symbols,
  profitCurrentDay,
  priceHistory,
  priceHistoryToday,
  priceHistoryMonth,
  subscribe,
  subscribeMany,
  unsubscribe,
  unsubscribeMany,
  subscribeOrderUpdate,
  subscribeOrderProfit,
  unsubscribeOrderProfit,
  unsubscribeOrderUpdate,
  orderSend,
  orderModify,
  orderClose,
  clearCache,
  ping,
  MT5Error
};
