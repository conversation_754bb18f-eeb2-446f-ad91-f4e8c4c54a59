/**
 * Symbol mapping configuration for standardizing trading symbols
 * across different sources and formats.
 */
const SYMBOL_MAP = {
  '.USTECHCash': 'USTECH',
  'US100': 'USTECH',
  'NDX100': 'USTECH',
  'US Tech 100': 'USTECH',
  'NASDAQ': 'USTECH',
  '.US30Cash': 'US30',
  'US30m': 'US30',
  '.US500Cash': 'US500',
  'US500m': 'US500',
  'SPX500': 'US500',
  'US SP 500': 'US500'
};

// Common suffix patterns to remove
const SUFFIX_PATTERN = /\.(m|s|r|v|z)$|[mcz]$/;

/**
 * Maps a trading symbol to its standardized form
 * @param {string} symbol - The input trading symbol
 * @returns {string} The standardized symbol
 */
const mapSymbol = (symbol) => {
  // Check direct mappings first
  if (SYMBOL_MAP[symbol]) {
    return SYMBOL_MAP[symbol];
  }
  
  // Remove common suffixes if present
  return symbol.replace(SUFFIX_PATTERN, '');
};

module.exports = {
  mapSymbol
};
