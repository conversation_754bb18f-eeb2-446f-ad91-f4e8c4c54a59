const supabase = require('../supabaseClient');
const { findAccountNumberByAccountId } = require('../supabaseService'); 
async function updateGridAiAccountNumbers() {
  try {
    const { data: records, error: fetchError } = await supabase
      .from('grid_ai')
      .select('id, account_id')
      .is('account_number', null);

    if (fetchError) {
      throw fetchError;
    }

    for (const record of records) {
      const accountNumber = await findAccountNumberByAccountId(record.account_id);
      //console.log('Account number found:', accountNumber);
      if (accountNumber && accountNumber.length > 0) {
        const { error: updateError } = await supabase
          .from('grid_ai')
          .update({ account_number: accountNumber[0].login })
          .eq('id', record.id);

        if (updateError) {
          console.error(
            `Failed to update account_number for id ${record.id}:`,
            updateError.message
          );
        } else {
          console.log(`Updated account_number for grid_ai id ${record.id}`);
        }
      } else {
        console.warn(
          `No account number found for account_id ${record.account_id}`
        );
      }
    }

    console.log('GridAI account number update complete.');
  } catch (error) {
    console.error('Error updating GridAI account numbers:', error.message);
  }
}

updateGridAiAccountNumbers();
