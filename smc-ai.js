// smcTradingModel.js
// Advanced AI trading model using SMC concepts in JavaScript

const { priceHistory } = require('./mt5-service');
const { findTickSizeValue } = require('./supabaseService');
const tf = require('@tensorflow/tfjs-node');
const axios = require('axios');
const fs = require('fs');
const path = require('path');

// Configuration object for easy parameter tuning
const CONFIG = {
  sma: {
    shortPeriod: 10,
    mediumPeriod: 20,
    longPeriod: 50
  },
  orderBlocks: {
    minimumSize: 0.001,
    maximumAge: 100,
    strengthThreshold: 0.7
  },
  fvg: {
    minGapSize: 0.0005,
    maxGapAge: 50,
    imbalanceThreshold: 0.6
  },
  liquidity: {
    lookbackPeriod: 10,
    volumeThreshold: 1.5,
    rangeFactor: 2
  },
  risk: {
    maxPositionSize: 1.0,
    maxRiskPerTrade: 0.02,
    maxDailyLoss: 0.05,
    minRiskReward: 2
  },
  model: {
    epochs: 100,
    batchSize: 32,
    validationSplit: 0.2,
    learningRate: 0.001
  }
};

// Helper functions for feature normalization and validation
function normalizeValue(value, min, max) {
  if (max === min) return 0;
  return (value - min) / (max - min);
}

function validateNumber(value) {
  return typeof value === 'number' && !isNaN(value) && isFinite(value);
}

const MODEL_SAVE_PATH = path.join(__dirname, 'saved_models');
const MODEL_INFO_FILE = path.join(MODEL_SAVE_PATH, 'model_info.json');

// ---------- Data Acquisition ----------
async function fetchHistoricalDataImpl(
  symbol = 'EURUSD',
  from = '2020-01-01T00:00:00',
  to = '2024-01-01T00:00:00',
  timeframe = 60
) {
  try {
    const data = await priceHistory(
      'k233v64y-e58b-35a5-vcrv-92ml4gtg8p4z',
      symbol,
      from,
      to,
      timeframe
    );

    if (!data || !Array.isArray(data) || data.length === 0) {
      throw new Error('Invalid or empty historical data received');
    }

    console.log('Received historical data:', {
      length: data.length,
      firstItem: data[0],
      lastItem: data[data.length - 1]
    });

    const ohlcvData = data.map(d => {
      if (!d || typeof d.openPrice === 'undefined') {
        console.error('Invalid data item:', d);
        throw new Error('Invalid data format received from priceHistoryMonth');
      }
      return new OHLCV(d);
    });

    // console.log('Converted to OHLCV format:', {
    //   length: ohlcvData.length,
    //   firstItem: ohlcvData[0],
    //   lastItem: ohlcvData[ohlcvData.length - 1]
    // });

    return ohlcvData;
  } catch (error) {
    console.error('Error fetching historical data:', error);
    throw error;
  }
}

async function fetchHistoricalData() {
  return await fetchHistoricalDataImpl();
}

async function fetchTestingData() {
  return await fetchHistoricalDataImpl(
    'Step Index',
    '2023-01-01T00:00:00',
    '2024-02-29T00:00:00',
    60
  );
}

// ---------- Enhanced Data Structures ----------
class OHLCV {
  constructor(data) {
    this.time = data.time;
    this.open = data.openPrice;
    this.high = data.highPrice;
    this.low = data.lowPrice;
    this.close = data.closePrice;
  }

  get bodySize() {
    return Math.abs(this.close - this.open);
  }

  get range() {
    return this.high - this.low;
  }

  get upperWick() {
    return this.high - Math.max(this.open, this.close);
  }

  get lowerWick() {
    return Math.min(this.open, this.close) - this.low;
  }

  get isBullish() {
    return this.close > this.open;
  }
}

class OrderBlock {
  constructor(candle, type, strength) {
    this.time = candle.time;
    this.type = type;
    this.high = candle.high;
    this.low = candle.low;
    this.strength = strength;
    this.touches = 0;
    this.active = true;
  }

  isValid(currentPrice, age) {
    return (
      age <= CONFIG.orderBlocks.maximumAge &&
      this.strength >= CONFIG.orderBlocks.strengthThreshold &&
      this.active
    );
  }

  hasBeenTested(price) {
    if (this.type === 'bullish') {
      return price <= this.high && price >= this.low;
    }
    return price >= this.low && price <= this.high;
  }
}

// ---------- Technical Indicators ----------
class TechnicalIndicators {
  static calculateEMA(prices, period) {
    const multiplier = 2 / (period + 1);
    let ema = prices[0];
    return prices.map((price, i) => {
      if (i === 0) return ema;
      ema = (price - ema) * multiplier + ema;
      return ema;
    });
  }

  static calculateRSI(prices, period = 14) {
    const changes = [];
    for (let i = 1; i < prices.length; i++) {
      changes.push(prices[i] - prices[i - 1]);
    }

    let gains = changes.map(change => Math.max(change, 0));
    let losses = changes.map(change => Math.abs(Math.min(change, 0)));

    let avgGain = gains.slice(0, period).reduce((a, b) => a + b) / period;
    let avgLoss = losses.slice(0, period).reduce((a, b) => a + b) / period;

    const rsi = [100 - (100 / (1 + avgGain / avgLoss))];

    for (let i = period; i < changes.length; i++) {
      avgGain = (avgGain * (period - 1) + gains[i]) / period;
      avgLoss = (avgLoss * (period - 1) + losses[i]) / period;
      rsi.push(100 - (100 / (1 + avgGain / avgLoss)));
    }

    return rsi;
  }

  static calculateATR(data, period = 14) {
    const trueRanges = data.map((candle, i) => {
      if (i === 0) return candle.range;
      const prevClose = data[i - 1].close;
      return Math.max(
        candle.high - candle.low,
        Math.abs(candle.high - prevClose),
        Math.abs(candle.low - prevClose)
      );
    });

    let atr = trueRanges[0];
    const result = [atr];

    for (let i = 1; i < data.length; i++) {
      atr = ((atr * (period - 1)) + trueRanges[i]) / period;
      result.push(atr);
    }

    return result;
  }
}

// ---------- Feature Engineering ----------
class FeatureEngineering {
  constructor(historicalData) {
    if (!historicalData || !Array.isArray(historicalData) || historicalData.length === 0) {
      throw new Error('Invalid historical data provided to FeatureEngineering');
    }

    this.data = historicalData;
    this.features = [];
    this.labels = [];
    this.featureStats = {
      min: {},
      max: {},
    };

    console.log('FeatureEngineering initialized with:', {
      dataLength: this.data.length,
      firstItem: this.data[0],
      lastItem: this.data[this.data.length - 1]
    });
  }

  calculateFeatureStats(features) {
    const stats = {
      min: Array(features[0].length).fill(Infinity),
      max: Array(features[0].length).fill(-Infinity)
    };

    for (const feature of features) {
      feature.forEach((value, i) => {
        if (validateNumber(value)) {
          stats.min[i] = Math.min(stats.min[i], value);
          stats.max[i] = Math.max(stats.max[i], value);
        }
      });
    }

    return stats;
  }

  normalizeFeatures(features, stats) {
    return features.map(feature => 
      feature.map((value, i) => {
        if (!validateNumber(value)) return 0;
        return normalizeValue(value, stats.min[i], stats.max[i]);
      })
    );
  }

  detectOrderBlocks() {
    const orderBlocks = [];
    for (let i = 1; i < this.data.length; i++) {
      const prev = this.data[i - 1];
      const curr = this.data[i];
      
      if (prev.isBullish && !curr.isBullish) {
        const strength = this.calculateOrderBlockStrength(prev, 'bullish');
        if (strength >= CONFIG.orderBlocks.strengthThreshold) {
          orderBlocks.push(new OrderBlock(prev, 'bullish', strength));
        }
      } else if (!prev.isBullish && curr.isBullish) {
        const strength = this.calculateOrderBlockStrength(prev, 'bearish');
        if (strength >= CONFIG.orderBlocks.strengthThreshold) {
          orderBlocks.push(new OrderBlock(prev, 'bearish', strength));
        }
      }
    }
    return orderBlocks;
  }

  detectFVG() {
    const fvgZones = [];
    for (let i = 0; i < this.data.length - 1; i++) {
      const curr = this.data[i];
      const next = this.data[i + 1];
      
      if (curr.high < next.low) {
        const gapSize = next.low - curr.high;
        if (gapSize >= CONFIG.fvg.minGapSize) {
          fvgZones.push({
            type: 'bullish',
            top: next.low,
            bottom: curr.high,
            size: gapSize,
            age: 0
          });
        }
      } else if (curr.low > next.high) {
        const gapSize = curr.low - next.high;
        if (gapSize >= CONFIG.fvg.minGapSize) {
          fvgZones.push({
            type: 'bearish',
            top: curr.low,
            bottom: next.high,
            size: gapSize,
            age: 0
          });
        }
      }
    }
    return fvgZones;
  }

  detectLiquidityLevels() {
    const levels = [];
    const period = CONFIG.liquidity.lookbackPeriod;
    
    for (let i = period; i < this.data.length; i++) {
      const volumeWindow = this.data.slice(i - period, i);
      const avgVolume = volumeWindow.reduce((sum, candle) => sum + candle.volume, 0) / period;
      
      if (this.data[i].volume > avgVolume * CONFIG.liquidity.volumeThreshold) {
        levels.push({
          price: this.data[i].close,
          volume: this.data[i].volume,
          strength: this.data[i].volume / avgVolume
        });
      }
    }
    return levels;
  }

  calculateOrderBlockStrength(candle, type) {
    const volumeStrength = candle.volume / this.getAverageVolume(10);
    const sizeStrength = candle.bodySize / this.getAverageRange(10);
    return (volumeStrength + sizeStrength) / 2;
  }

  getAverageVolume(period) {
    const volumes = this.data.slice(-period).map(c => c.volume);
    return volumes.reduce((a, b) => a + b) / period;
  }

  getAverageRange(period) {
    const ranges = this.data.slice(-period).map(c => c.range);
    return ranges.reduce((a, b) => a + b) / period;
  }

  computeAllFeatures() {
    if (!this.data || this.data.length === 0) {
      throw new Error('No data available for feature computation');
    }

    const rawFeatures = [];
    const closePrices = this.data.map(d => {
      if (!d || typeof d.close === 'undefined') {
        console.error('Invalid candle data:', d);
        throw new Error('Invalid candle data found while computing features');
      }
      return d.close;
    });

    // Technical indicators
    const ema10 = TechnicalIndicators.calculateEMA(closePrices, CONFIG.sma.shortPeriod);
    const ema20 = TechnicalIndicators.calculateEMA(closePrices, CONFIG.sma.mediumPeriod);
    const ema50 = TechnicalIndicators.calculateEMA(closePrices, CONFIG.sma.longPeriod);
    const rsi = TechnicalIndicators.calculateRSI(closePrices);
    const atr = TechnicalIndicators.calculateATR(this.data);

    // SMC features
    const orderBlocks = this.detectOrderBlocks();
    const fvgZones = this.detectFVG();
    const liquidityLevels = this.detectLiquidityLevels();

    // Combine all features
    for (let i = CONFIG.sma.longPeriod; i < this.data.length - 1; i++) {
      const candle = this.data[i];
      if (!candle) {
        console.error(`Invalid candle at index ${i}`);
        continue;
      }
      
      try {
        const features = [
          // Price action features
          candle.bodySize / Math.max(candle.range, 0.00001),
          candle.upperWick / Math.max(candle.range, 0.00001),
          candle.lowerWick / Math.max(candle.range, 0.00001),
          
          // Technical indicator features
          (candle.close - ema10[i]) / Math.max(candle.close, 0.00001),
          (candle.close - ema20[i]) / Math.max(candle.close, 0.00001),
          (candle.close - ema50[i]) / Math.max(candle.close, 0.00001),
          rsi[i] / 100,
          atr[i] / Math.max(candle.close, 0.00001),
          
          // SMC features
          this.getOrderBlockStrength(orderBlocks, i),
          this.getFVGStrength(fvgZones, i),
          this.getLiquidityScore(liquidityLevels, i)
        ];

        // Validate all features
        if (features.some(f => !validateNumber(f))) {
          console.error(`Invalid feature values at index ${i}:`, features);
          continue;
        }

        // Validate that we have exactly 11 features
        if (features.length !== 11) {
          throw new Error(`Expected 11 features, but got ${features.length}`);
        }

        rawFeatures.push(features);
        
        // Label: 1 if price moves in predicted direction by target amount
        const futurePrice = this.data[i + 1].close;
        const priceChange = (futurePrice - candle.close) / Math.max(candle.close, 0.00001);
        this.labels.push(priceChange > CONFIG.risk.minRiskReward ? 1 : 0);
      } catch (error) {
        console.error(`Error processing candle at index ${i}:`, error);
        continue;
      }
    }

    // Calculate and store feature statistics
    this.featureStats = this.calculateFeatureStats(rawFeatures);
    
    // Normalize features
    this.features = this.normalizeFeatures(rawFeatures, this.featureStats);

    console.log('Feature computation completed:', {
      featuresLength: this.features.length,
      labelsLength: this.labels.length,
      featureStats: this.featureStats
    });

    return {
      featureTensor: tf.tensor2d(this.features),
      labelTensor: tf.tensor1d(this.labels)
    };
  }

  computeFeaturesForCandle(candle) {
    try {
      // Add the current candle to historical data for calculations
      const updatedData = [...this.data, candle];
      const closePrices = updatedData.map(d => d.close);
      
      // Calculate technical indicators
      const ema10 = TechnicalIndicators.calculateEMA(closePrices, CONFIG.sma.shortPeriod);
      const ema20 = TechnicalIndicators.calculateEMA(closePrices, CONFIG.sma.mediumPeriod);
      const ema50 = TechnicalIndicators.calculateEMA(closePrices, CONFIG.sma.longPeriod);
      const rsi = TechnicalIndicators.calculateRSI(closePrices);
      const atr = TechnicalIndicators.calculateATR(updatedData);

      const features = [
        // Price action features (3)
        candle.bodySize / Math.max(candle.range, 0.00001),
        candle.upperWick / Math.max(candle.range, 0.00001),
        candle.lowerWick / Math.max(candle.range, 0.00001),
        
        // Technical indicator features (5)
        (candle.close - ema10[ema10.length - 1]) / Math.max(candle.close, 0.00001),
        (candle.close - ema20[ema20.length - 1]) / Math.max(candle.close, 0.00001),
        (candle.close - ema50[ema50.length - 1]) / Math.max(candle.close, 0.00001),
        rsi[rsi.length - 1] / 100,
        atr[atr.length - 1] / Math.max(candle.close, 0.00001),
        
        // SMC features (3)
        this.getOrderBlockStrength(this.detectOrderBlocks(), this.data.length - 1),
        this.getFVGStrength(this.detectFVG(), this.data.length - 1),
        this.getLiquidityScore(this.detectLiquidityLevels(), this.data.length - 1)
      ];

      // Validate features
      if (features.some(f => !validateNumber(f))) {
        throw new Error('Invalid feature values computed');
      }

      // Validate that we have exactly 11 features
      if (features.length !== 11) {
        throw new Error(`Expected 11 features, but got ${features.length}`);
      }

      // Normalize features using stored statistics
      const normalizedFeatures = features.map((value, i) => 
        normalizeValue(value, this.featureStats.min[i], this.featureStats.max[i])
      );

      return tf.tensor2d([normalizedFeatures]);
    } catch (error) {
      console.error('Error computing features for candle:', error);
      throw error;
    }
  }

  calculateATR(period) {
    return TechnicalIndicators.calculateATR(this.data, period);
  }

  getOrderBlockStrength(orderBlocks, index) {
    const relevantBlocks = orderBlocks.filter(ob => ob.isValid(this.data[index].close, 0));
    if (relevantBlocks.length === 0) return 0;
    return Math.max(...relevantBlocks.map(ob => ob.strength));
  }

  getFVGStrength(fvgZones, index) {
    const activeZones = fvgZones.filter(fvg => fvg.age < CONFIG.fvg.maxGapAge);
    if (activeZones.length === 0) return 0;
    return Math.max(...activeZones.map(fvg => fvg.size));
  }

  getLiquidityScore(levels, index) {
    const nearbyLevels = levels.filter(level => 
      Math.abs(level.price - this.data[index].close) / this.data[index].close < 0.001
    );
    if (nearbyLevels.length === 0) return 0;
    return Math.max(...nearbyLevels.map(level => level.strength));
  }
}

// ---------- Neural Network Model ----------
class SMCModel {
  constructor() {
    this.model = null;
    this.modelInfo = {
      lastTrainedDate: null,
      dataPoints: 0,
      accuracy: 0,
      version: '1.0'
    };
  }

  async initialize() {
    try {
      // Create saved_models directory if it doesn't exist
      if (!fs.existsSync(MODEL_SAVE_PATH)) {
        fs.mkdirSync(MODEL_SAVE_PATH, { recursive: true });
      }
      
      // Try to load existing model
      const modelExists = await this.loadModel();
      if (!modelExists) {
        this.model = this.buildModel();
      }
    } catch (error) {
      console.error('Error initializing model:', error);
      this.model = this.buildModel();
    }
  }

  async loadModel() {
    try {
      // Check if model files exist
      if (fs.existsSync(MODEL_INFO_FILE)) {
        const modelInfo = JSON.parse(fs.readFileSync(MODEL_INFO_FILE, 'utf8'));
        
        // Check if model is too old (e.g., older than 1 day)
        const modelAge = Date.now() - new Date(modelInfo.lastTrainedDate).getTime();
        const ONE_DAY = 24 * 60 * 60 * 1000;
        
        if (modelAge < ONE_DAY) {
          console.log('Loading existing model...');
          this.model = await tf.loadLayersModel(`file://${path.join(MODEL_SAVE_PATH, 'model.json')}`);
          this.modelInfo = modelInfo;
          console.log('Model loaded successfully');
          return true;
        } else {
          console.log('Existing model is too old, will train new model');
          return false;
        }
      }
      return false;
    } catch (error) {
      console.error('Error loading model:', error);
      return false;
    }
  }

  async saveModel() {
    try {
      console.log('Saving model...');
      
      // Save the model architecture and weights
      await this.model.save(`file://${MODEL_SAVE_PATH}`);
      
      // Save model metadata
      this.modelInfo.lastTrainedDate = new Date().toISOString();
      fs.writeFileSync(MODEL_INFO_FILE, JSON.stringify(this.modelInfo, null, 2));
      
      console.log('Model saved successfully');
    } catch (error) {
      console.error('Error saving model:', error);
    }
  }

  buildModel() {
    const model = tf.sequential();
    
    model.add(tf.layers.dense({
      inputShape: [11], // Update to match our 11 features
      units: 64,
      activation: 'relu',
      kernelRegularizer: tf.regularizers.l2({ l2: 0.01 })
    }));
    
    model.add(tf.layers.dropout({ rate: 0.3 }));

    model.add(tf.layers.dense({
      units: 32,
      activation: 'relu',
      kernelRegularizer: tf.regularizers.l2({ l2: 0.01 })
    }));

    model.add(tf.layers.dropout({ rate: 0.2 }));

    model.add(tf.layers.dense({
      units: 1,
      activation: 'sigmoid'
    }));

    model.compile({
      optimizer: tf.train.adam(CONFIG.model.learningRate),
      loss: 'binaryCrossentropy',
      metrics: ['accuracy']
    });

    return model;
  }

  async train(featureTensor, labelTensor) {
    const history = await this.model.fit(featureTensor, labelTensor, {
      epochs: CONFIG.model.epochs,
      batchSize: CONFIG.model.batchSize,
      validationSplit: CONFIG.model.validationSplit,
      callbacks: {
        onEpochEnd: (epoch, logs) => {
          console.log(
            `Epoch ${epoch + 1}: loss = ${logs.loss.toFixed(4)}, accuracy = ${logs.acc.toFixed(4)}`
          );
          // Update model info with latest accuracy
          this.modelInfo.accuracy = logs.acc;
        }
      }
    });

    // Update model info
    this.modelInfo.dataPoints = featureTensor.shape[0];
    
    // Save the trained model
    await this.saveModel();

    return history;
  }

  async predict(features) {
    try {
      const prediction = this.model.predict(features);
      const predictionData = await prediction.data();
      prediction.dispose(); // Clean up tensor
      return predictionData[0];
    } catch (error) {
      console.error('Error in model prediction:', error);
      throw error;
    }
  }
}

// ---------- Risk Management ----------
class RiskManager {
  constructor(accountBalance) {
    this.accountBalance = accountBalance;
    this.dailyPnL = 0;
    this.openPositions = new Map();
  }

  async calculatePositionSize(entry, stopLoss, confidence, symbol) {
    // const { tick_value, tick_size } = await findTickSizeValue(symbol);
    // console.log('Tick Value: ', tick_value);
    // console.log('Tick Size: ', tick_size);
    const riskAmount = this.accountBalance * CONFIG.risk.maxRiskPerTrade;
    const pipValue = 1;
    const stopDistance = Math.abs(entry - stopLoss);
    const positionSize = (riskAmount / stopDistance) * pipValue;

    return Math.min(positionSize, CONFIG.risk.maxPositionSize);
  }

  canTakeTrade(signal) {
    return (
      this.dailyPnL > -this.accountBalance * CONFIG.risk.maxDailyLoss &&
      this.openPositions.size < 3 &&
      signal.riskRewardRatio >= CONFIG.risk.minRiskReward
    );
  }

  updatePnL(pnl) {
    this.dailyPnL += pnl;
    return this.dailyPnL;
  }
}

// ---------- Main Trading System ----------
class SMCTradingSystem {
  constructor(accountBalance) {
    this.model = new SMCModel();
    this.riskManager = new RiskManager(accountBalance);
    this.features = null;
  }

  async initialize() {
    // Initialize the model first
    await this.model.initialize();
    
    // Check if we need to train a new model
    if (!this.model.modelInfo.lastTrainedDate) {
      console.log('Training new model...');
      const historicalData = await fetchHistoricalData();
      this.features = new FeatureEngineering(historicalData);
      const { featureTensor, labelTensor } = this.features.computeAllFeatures();
      await this.model.train(featureTensor, labelTensor);
    } else {
      console.log('Using existing model from:', this.model.modelInfo.lastTrainedDate);
      // Still fetch recent data for feature computation
      const historicalData = await fetchHistoricalData();
      this.features = new FeatureEngineering(historicalData);
    }
  }

  async analyzeMarket(currentCandle, symbol) {
    const features = this.features.computeFeaturesForCandle(currentCandle);
    const prediction = await this.model.predict(features);
    console.log('Prediction:', prediction);
    
    const signal = this.generateTradeSignal(prediction, currentCandle);
    
    if (signal && this.riskManager.canTakeTrade(signal)) {
      const positionSize = await this.riskManager.calculatePositionSize(
        signal.entry,
        signal.stopLoss,
        prediction,
        symbol
      );
      
      return {
        ...signal,
        positionSize,
        confidence: prediction
      };
    }
    
    return null;
  }

  generateTradeSignal(prediction, candle) {
    console.log('Generating signal with prediction:', prediction);
    
    // Confidence thresholds
    const HIGH_CONFIDENCE = 0.7;
    const LOW_CONFIDENCE = 0.3;

    // No trade zone
    if (prediction > LOW_CONFIDENCE && prediction < HIGH_CONFIDENCE) {
      console.log('Prediction in no-trade zone:', prediction);
      return null;
    }

    // Get current market context
    const recentCandles = this.features.data.slice(-5);
    const priceAction = {
      isUptrend: recentCandles.every((c, i) => 
        i === 0 || c.close >= recentCandles[i-1].close
      ),
      isDowntrend: recentCandles.every((c, i) => 
        i === 0 || c.close <= recentCandles[i-1].close
      )
    };

    // Calculate ATR for dynamic stop loss and take profit
    const atrValues = this.features.calculateATR(14);
    const currentAtr = atrValues[atrValues.length - 1];

    // Validate ATR
    if (!currentAtr || isNaN(currentAtr) || currentAtr <= 0) {
      console.error('Invalid ATR value:', currentAtr);
      return null;
    }

    console.log('Market context:', {
      prediction,
      isUptrend: priceAction.isUptrend,
      isDowntrend: priceAction.isDowntrend,
      currentClose: candle.close,
      currentAtr
    });

    const atrMultiplier = 1.5;
    const stopDistance = currentAtr * atrMultiplier;

    // Validate stop distance
    if (!stopDistance || isNaN(stopDistance) || stopDistance <= 0) {
      console.error('Invalid stop distance:', stopDistance);
      return null;
    }

    let signal = null;

    // Buy Signal
    if (prediction >= HIGH_CONFIDENCE && !priceAction.isDowntrend) {
      const entry = candle.close;
      const stopLoss = entry - stopDistance;
      const takeProfit = entry + (stopDistance * CONFIG.risk.minRiskReward);

      // Validate values
      if (!validateNumber(entry) || !validateNumber(stopLoss) || !validateNumber(takeProfit)) {
        console.error('Invalid signal values:', { entry, stopLoss, takeProfit });
        return null;
      }

      console.log('Generating BUY signal');
      signal = {
        type: 'BUY',
        entry,
        stopLoss,
        takeProfit,
        confidence: prediction
      };
    }
    // Sell Signal
    else if (prediction <= LOW_CONFIDENCE && !priceAction.isUptrend) {
      const entry = candle.close;
      const stopLoss = entry + stopDistance;
      const takeProfit = entry - (stopDistance * CONFIG.risk.minRiskReward);

      // Validate values
      if (!validateNumber(entry) || !validateNumber(stopLoss) || !validateNumber(takeProfit)) {
        console.error('Invalid signal values:', { entry, stopLoss, takeProfit });
        return null;
      }

      console.log('Generating SELL signal');
      signal = {
        type: 'SELL',
        entry,
        stopLoss,
        takeProfit,
        confidence: 1 - prediction
      };
    }

    if (signal) {
      // Calculate and validate risk-reward ratio
      const riskDistance = Math.abs(signal.stopLoss - signal.entry);
      const rewardDistance = Math.abs(signal.takeProfit - signal.entry);

      if (!riskDistance || !rewardDistance) {
        console.error('Invalid risk/reward distances:', { riskDistance, rewardDistance });
        return null;
      }

      const riskRewardRatio = rewardDistance / riskDistance;

      if (!validateNumber(riskRewardRatio)) {
        console.error('Invalid risk-reward ratio:', riskRewardRatio);
        return null;
      }
                             
      console.log('Signal details:', {
        type: signal.type,
        entry: signal.entry,
        stopLoss: signal.stopLoss,
        takeProfit: signal.takeProfit,
        riskRewardRatio,
        confidence: signal.confidence
      });

      // Only return signal if it meets minimum risk-reward ratio
      if (riskRewardRatio >= CONFIG.risk.minRiskReward) {
        signal.riskRewardRatio = riskRewardRatio;
        return signal;
      }
    }

    return null;
  }
}

// ---------- Usage Example ----------
async function main() {
  const accountBalance = 10000;
  const tradingSystem = new SMCTradingSystem(accountBalance);
  
  try {
    await tradingSystem.initialize();
    console.log('Trading system initialized and model trained');
    
    // Simulate real-time trading
    const currentCandle = new OHLCV({
      time: Date.now(),
      openPrice: 1.2345,
      highPrice: 1.2355,
      lowPrice: 1.2335,
      closePrice: 1.2350,
    });

    const testingData = await fetchTestingData();

    for (let data of testingData) {
      const signal = await tradingSystem.analyzeMarket(data, 'Step Index');
      
      if (signal) {
        console.log('Trade Signal Generated:', signal);
        // Here you would integrate with your MT5 service to place the actual trade
      } else {
        // console.log('No valid trading opportunity found');
      }
  }
  } catch (error) {
    console.error('Error in trading system:', error);
  }
}

// Start the trading system
main().catch(console.error);
