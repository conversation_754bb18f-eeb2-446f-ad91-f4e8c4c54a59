const { findCandlestickInformationBySymbolAndTimeframe } = require('./supabaseService');

// Function to find local maxima
function findLocalMaxima(candlesticks, range = 2) {
  let maxima = [];
  for (let i = range; i < candlesticks.length - range; i++) {
    if (!candlesticks[i] || typeof candlesticks[i].high !== 'number') {
      console.error(`Invalid candlestick at index ${i}:`, candlesticks[i]);
      continue;
    }
    let isMax = true;
    for (let j = i - range; j <= i + range; j++) {
      if (j !== i && candlesticks[j] && typeof candlesticks[j].high === 'number' && candlesticks[j].high >= candlesticks[i].high) {
        isMax = false;
        break;
      }
    }
    if (isMax) {
      maxima.push({ index: i, price: candlesticks[i].high });
    }
  }
  return maxima;
}

// Function to find local minima
function findLocalMinima(candlesticks, range = 2) {
  let minima = [];
  for (let i = range; i < candlesticks.length - range; i++) {
    if (!candlesticks[i] || typeof candlesticks[i].low !== 'number') {
      console.error(`Invalid candlestick at index ${i}:`, candlesticks[i]);
      continue;
    }
    let isMin = true;
    for (let j = i - range; j <= i + range; j++) {
      if (j !== i && candlesticks[j] && typeof candlesticks[j].low === 'number' && candlesticks[j].low <= candlesticks[i].low) {
        isMin = false;
        break;
      }
    }
    if (isMin) {
      minima.push({ index: i, price: candlesticks[i].low });
    }
  }
  return minima;
}

// Function to cluster prices within a threshold percentage
function clusterPrices(prices, thresholdPercentage = 1) {
  if (!prices || prices.length === 0) {
    console.error('No prices provided to clusterPrices function');
    return [];
  }

  prices.sort((a, b) => {
    if (!a || !b || typeof a.price !== 'number' || typeof b.price !== 'number') {
      console.error('Invalid price object:', a, b);
      return 0;
    }
    return a.price - b.price;
  });

  let clusters = [];
  let currentCluster = [prices[0]];

  for (let i = 1; i < prices.length; i++) {
    if (!prices[i] || typeof prices[i].price !== 'number') {
      console.error(`Invalid price object at index ${i}:`, prices[i]);
      continue;
    }

    let previousPrice = currentCluster[currentCluster.length - 1].price;
    let currentPrice = prices[i].price;
    let diffPercentage = (Math.abs(currentPrice - previousPrice) / previousPrice) * 100;

    if (diffPercentage <= thresholdPercentage) {
      currentCluster.push(prices[i]);
    } else {
      clusters.push(currentCluster);
      currentCluster = [prices[i]];
    }
  }

  clusters.push(currentCluster);
  return clusters;
}

// Function to calculate average price of clusters
function getClusterLevels(clusters) {
  return clusters.map(cluster => {
    let sum = cluster.reduce((acc, point) => acc + point.price, 0);
    let avgPrice = sum / cluster.length;
    return {
      level: avgPrice,
      points: cluster
    };
  });
}

// Main function to detect support and resistance levels
function detectSupportResistance(candlesticks) {
  if (!candlesticks || candlesticks.length === 0) {
    console.error('No candlesticks provided to detectSupportResistance function');
    return { resistanceLevels: [], supportLevels: [] };
  }

  let maxima = findLocalMaxima(candlesticks);
  let minima = findLocalMinima(candlesticks);

  // console.log('Maxima:', maxima);
  // console.log('Minima:', minima);

  if (maxima.length === 0 && minima.length === 0) {
    console.warn('No local maxima or minima found');
    return { resistanceLevels: [], supportLevels: [] };
  }

  let maxClusters = clusterPrices(maxima);
  let minClusters = clusterPrices(minima);

  let resistanceLevels = getClusterLevels(maxClusters);
  let supportLevels = getClusterLevels(minClusters);

  return {
    resistanceLevels,
    supportLevels
  };
}

// Function to simulate trading based on support and resistance levels
function simulateTrading(candlesticks, levels) {
  let balance = 0;
  let position = null; // null, 'long', or 'short'
  let trades = [];

  for (let i = 0; i < candlesticks.length; i++) {
    let candle = candlesticks[i];
    let price = candle.close;

    // Check for buy signals (price touches support level)
    for (let support of levels.supportLevels) {
      if (price <= support.level && !position) {
        position = 'long';
        trades.push({
          type: 'Buy',
          price: price,
          time: candle.time,
          index: i
        });
        console.log(`Buy at ${price} on ${candle.time}`);
        break;
      }
    }

    // Check for sell signals (price touches resistance level)
    for (let resistance of levels.resistanceLevels) {
      if (price >= resistance.level && position === 'long') {
        position = null;
        trades.push({
          type: 'Sell',
          price: price,
          time: candle.time,
          index: i
        });
        console.log(`Sell at ${price} on ${candle.time}`);
        break;
      }
    }
  }

  return trades;
}

// Example usage:
// let candlesticks = [
//   { time: '2023-10-01', open: 100, high: 105, low: 95, close: 102 },
//   { time: '2023-10-02', open: 102, high: 110, low: 100, close: 108 },
//   { time: '2023-10-03', open: 108, high: 112, low: 105, close: 107 },
//   { time: '2023-10-04', open: 107, high: 109, low: 100, close: 101 },
//   { time: '2023-10-05', open: 101, high: 103, low: 95, close: 99 },
//   { time: '2023-10-06', open: 99, high: 102, low: 94, close: 96 },
//   { time: '2023-10-07', open: 96, high: 98, low: 90, close: 92 },
//   { time: '2023-10-08', open: 92, high: 95, low: 88, close: 93 },
//   { time: '2023-10-09', open: 93, high: 97, low: 91, close: 95 },
//   { time: '2023-10-10', open: 95, high: 99, low: 93, close: 98 }
// ];

// let levels = detectSupportResistance(candlesticks);
// console.log('Resistance Levels:', levels.resistanceLevels);
// console.log('Support Levels:', levels.supportLevels);

// let trades = simulateTrading(candlesticks, levels);
// console.log('Trades Executed:', trades);


const findSupportResistanceLevels = async (symbol, timeframe) => {
  try {
    const candles = await findCandlestickInformationBySymbolAndTimeframe(symbol, timeframe);
    console.log('Candles received:', candles);
    if (!candles || candles.length === 0) {
      throw new Error('No candlestick data found');
    }
    console.log('First candle:', candles[0]);
    console.log('Last candle:', candles[candles.length - 1]);
    console.log('Number of candles:', candles.length);

    const levels = detectSupportResistance(candles);
    console.log('Detected levels:', levels);
    return levels;
  } catch (error) {
    console.error('Error in findSupportResistanceLevels:', error);
    throw error;
  }
};


module.exports = {
  detectSupportResistance,
  // simulateTrading,
  findSupportResistanceLevels
};
