const express = require('express');
const {
  saveUserInformation,
  lookupUserById,
  lookupUserByEmail,
  findUserByUUID,
  saveTradeBrokerAccount,
  getTradeBrokerAccountById,
  getTradeBrokerAccountByLogin,
  getTradeBrokerAccountsByUserId,
  removeBrokerTradingAccount,
  saveTradeAccountSymbol,
  getTradeAccountSymbolByUserIdAndBrokerAccountId,
  saveTradeUserAccountSymbolDefault,
  getTradeUserAccountSymbolDefaultByUserIdAndBrokerAccountIdAndSymbol,
  saveTradeBot,
  getTradeBotByUserIdAndBrokerAccountIdAndSymbol,
  getTradeBotByUserIdAndBrokerAccountId,
  getTradeBotByUserId,
  findBotById
} = require('./supabaseService');

const app = express();
const port = process.env.PORT || 3000;

app.use(express.json());

app.post('/save-user-info', async (req, res) => {
  try {
    await saveUserInformation(req.body);
    res.status(200).send('User information saved successfully');
  } catch (error) {
    res.status(500).send('Error saving user information');
  }
});

app.get('/user/:id', async (req, res) => {
  const data = await lookupUserById(Number(req.params.id));
  res.json(data);
});

app.get('/user/email/:email', async (req, res) => {
  const data = await lookupUserByEmail(req.params.email);
  res.json(data);
});

app.get('/user/uuid/:uuid', async (req, res) => {
  const data = await findUserByUUID(req.params.uuid);
  res.json(data);
});

app.post('/save-trade-broker-account', async (req, res) => {
  try {
    await saveTradeBrokerAccount(req.body);
    res.status(200).send('Trade broker account saved successfully');
  } catch (error) {
    res.status(500).send('Error saving trade broker account');
  }
});

app.get('/trade-broker-account/:id', async (req, res) => {
  const data = await getTradeBrokerAccountById(Number(req.params.id));
  res.json(data);
});

app.get('/trade-broker-account/login/:login', async (req, res) => {
  const data = await getTradeBrokerAccountByLogin(Number(req.params.login));
  res.json(data);
});

app.get('/trade-broker-accounts/user/:userAccountId', async (req, res) => {
  const data = await getTradeBrokerAccountsByUserId(
    Number(req.params.userAccountId)
  );
  res.json(data);
});

app.delete('/trade-broker-account/:id', async (req, res) => {
  try {
    await removeBrokerTradingAccount(Number(req.params.id));
    res.status(200).send('Trade broker account removed successfully');
  } catch (error) {
    res.status(500).send('Error removing trade broker account');
  }
});

app.post('/save-trade-account-symbol', async (req, res) => {
  try {
    await saveTradeAccountSymbol(req.body);
    res.status(200).send('Trade account symbol saved successfully');
  } catch (error) {
    res.status(500).send('Error saving trade account symbol');
  }
});

app.get('/trade-account-symbol', async (req, res) => {
  const { userAccountId, brokerAccountId } = req.query;
  const data = await getTradeAccountSymbolByUserIdAndBrokerAccountId(
    Number(userAccountId),
    Number(brokerAccountId)
  );
  res.json(data);
});

app.post('/save-trade-user-account-symbol-default', async (req, res) => {
  try {
    await saveTradeUserAccountSymbolDefault(req.body);
    res
      .status(200)
      .send('Trade user account symbol default saved successfully');
  } catch (error) {
    res.status(500).send('Error saving trade user account symbol default');
  }
});

app.get('/trade-user-account-symbol-default', async (req, res) => {
  const { userAccountId, brokerAccountId, symbol } = req.query;
  const data =
    await getTradeUserAccountSymbolDefaultByUserIdAndBrokerAccountIdAndSymbol(
      Number(userAccountId),
      Number(brokerAccountId),
      symbol
    );
  res.json(data);
});

app.post('/save-trade-bot', async (req, res) => {
  try {
    await saveTradeBot(req.body);
    res.status(200).send('Trade bot saved successfully');
  } catch (error) {
    res.status(500).send('Error saving trade bot');
  }
});

app.get('/trade-bot', async (req, res) => {
  const { userAccountId, brokerAccountId, symbol } = req.query;
  const data = await getTradeBotByUserIdAndBrokerAccountIdAndSymbol(
    Number(userAccountId),
    Number(brokerAccountId),
    symbol
  );
  res.json(data);
});

app.get('/trade-bots', async (req, res) => {
  const { userAccountId, brokerAccountId } = req.query;
  const data = await getTradeBotByUserIdAndBrokerAccountId(
    Number(userAccountId),
    Number(brokerAccountId)
  );
  res.json(data);
});

app.get('/user-trade-bots/:userAccountId', async (req, res) => {
  const data = await getTradeBotByUserId(Number(req.params.userAccountId));
  res.json(data);
});

app.get('/trade-bot/:id', async (req, res) => {
  const data = await findBotById(Number(req.params.id));
  res.json(data);
});

app.listen(port, () => {
  console.log(`Server is running on http://localhost:${port}`);
});
