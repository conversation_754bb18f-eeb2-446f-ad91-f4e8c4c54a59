# Trade Manager Server

A robust trading management system built with Node.js and Supabase, designed to handle trading operations, signal processing, and automated trading strategies.

## Recent Optimizations

### 1. Database Service Layer (supabaseService.js)
- **Improved Error Handling**
  - Implemented centralized error handling with `executeDbOperation`
  - Enhanced error messages with better context
  - Added proper error propagation throughout the service

- **Query Builder Pattern**
  - Introduced a `QueryBuilder` object for fluent database operations
  - Reduced code duplication
  - Standardized query construction

- **Caching Layer**
  - Added in-memory caching with TTL (Time To Live)
  - Implemented `withCache` wrapper for frequently accessed data
  - Default cache expiration of 5 minutes (configurable)

- **Code Organization**
  - Grouped related functions (User, Broker, Bot operations)
  - Removed duplicate code through helper functions
  - Added comprehensive JSDoc documentation

- **Performance Improvements**
  - Consolidated similar database queries
  - Optimized symbol mapping operations
  - Reduced unnecessary database calls

### 2. Symbol Mapper (symbol-mapper.js)
- **Optimized Implementation**
  - Replaced multiple if-else conditions with lookup tables
  - Consolidated suffix patterns into a single regex
  - Added proper documentation
  - Fixed bug in `.z` suffix replacement

## Project Structure

```
trade-manager-server/
├── supabaseClient.js    # Supabase client configuration
├── supabaseService.js   # Database service layer
├── symbol-mapper.js     # Symbol mapping utilities
└── util.js             # Common utilities
```

## Key Features

- User account management
- Broker account integration
- Automated trading bots
- Signal processing and subscription
- Technical indicator calculations
- Grid trading AI
- Telegram bot integration

## Performance Improvements

The recent optimizations have led to:
- Reduced database calls through intelligent caching
- Better memory management
- Improved error handling and debugging
- More maintainable and type-safe code
- Enhanced query performance

## Best Practices Implemented

1. **Error Handling**
   - Consistent error propagation
   - Detailed error messages
   - Proper logging

2. **Code Quality**
   - TypeScript-like documentation
   - Modular design
   - Clean code principles

3. **Performance**
   - Smart caching strategies
   - Optimized database queries
   - Efficient memory usage

4. **Maintainability**
   - Clear documentation
   - Consistent coding style
   - Modular architecture

## Getting Started

1. Ensure you have Node.js installed
2. Set up your Supabase project and credentials
3. Configure environment variables
4. Install dependencies
5. Start the server

## Environment Variables

```env
SUPABASE_URL=your_supabase_url
SUPABASE_KEY=your_supabase_key
```

## Contributing

When contributing to this project, please:
1. Follow the established coding style
2. Add proper documentation
3. Write clear commit messages
4. Test your changes thoroughly

## License

This project is licensed under the MIT License - see the LICENSE file for details.
