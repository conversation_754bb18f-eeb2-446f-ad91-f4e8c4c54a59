{"name": "trade-manager-server", "version": "1.0.0", "main": "index.js", "scripts": {"test": "node index.js", "start": "node mt5-server.js"}, "author": "Israel Umeadi", "license": "ISC", "description": "", "dependencies": {"@supabase/supabase-js": "^2.44.3", "@tensorflow/tfjs-node": "^4.22.0", "amqplib": "^0.10.8", "axios": "^1.7.2", "axios-rate-limit": "^1.4.0", "axios-retry": "^4.5.0", "compression": "^1.7.4", "cors": "^2.8.5", "date-fns": "^4.1.0", "express": "^4.19.2", "mqtt": "^5.13.0", "node-cache": "^5.1.2", "swagger-jsdoc": "^6.2.8", "swagger-ui-express": "^5.0.1", "technicalindicators": "^3.1.0", "uuid": "^11.1.0"}}