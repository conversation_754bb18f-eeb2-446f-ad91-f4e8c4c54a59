// trendlineTradingSystem.js
// Rule-based trendline trading system using only price data with outcome tracking and optimizations

const { priceHistory } = require('./mt5-service');
const fs = require('fs');
const path = require('path');

// ---------------- Configuration ----------------
const CONFIG = {
  trendline: {
    regressionPeriod: 50, // Number of candles used for linear regression
    deviationThreshold: 0.01 // Minimum relative deviation (e.g., 1%) from trendline required to consider a signal
  },
  risk: {
    maxPositionSize: 1.0,
    maxRiskPerTrade: 0.02, // 2% risk per trade
    maxDailyLoss: 0.05, // 5% max daily loss
    minRiskReward: 2 // Minimum risk-reward ratio
  },
  simulation: {
    minHoldingPeriod: 2 // Minimum number of candles to wait after signal before evaluating outcome
  },
  strategy: {
    atrMultiplier: 2.0 // Increase ATR multiplier to widen stops
  }
};

// ---------------- Helper Functions ----------------
function validateNumber(value) {
  return typeof value === 'number' && !isNaN(value) && isFinite(value);
}

// ---------------- Data Acquisition ----------------
async function fetchHistoricalDataImpl(
  symbol = 'EURUSD',
  from = '2020-01-01T00:00:00',
  to = '2024-01-01T00:00:00',
  timeframe = 60
) {
  try {
    const data = await priceHistory(
      'k233v64y-e58b-35a5-vcrv-92ml4gtg8p4z',
      symbol,
      from,
      to,
      timeframe
    );
    if (!data || !Array.isArray(data) || data.length === 0) {
      throw new Error('Invalid or empty historical data received');
    }
    console.log('Received historical data:', {
      length: data.length,
      firstItem: data[0],
      lastItem: data[data.length - 1]
    });
    const ohlcvData = data.map(d => {
      if (!d || typeof d.openPrice === 'undefined') {
        console.error('Invalid data item:', d);
        throw new Error('Invalid data format received');
      }
      return new OHLCV(d);
    });
    return ohlcvData;
  } catch (error) {
    console.error('Error fetching historical data:', error);
    throw error;
  }
}

async function fetchHistoricalData() {
  return await fetchHistoricalDataImpl();
}

async function fetchTestingData() {
  return await fetchHistoricalDataImpl(
    'Step Index',
    '2023-01-01T00:00:00',
    '2024-02-29T00:00:00',
    60
  );
}

// ---------------- Enhanced Data Structures ----------------
class OHLCV {
  constructor(data) {
    this.time = data.time;
    this.open = data.openPrice;
    this.high = data.highPrice;
    this.low = data.lowPrice;
    this.close = data.closePrice;
    this.volume = data.volume || 1;
  }

  get bodySize() {
    return Math.abs(this.close - this.open);
  }

  get range() {
    return this.high - this.low;
  }

  get upperWick() {
    return this.high - Math.max(this.open, this.close);
  }

  get lowerWick() {
    return Math.min(this.open, this.close) - this.low;
  }

  get isBullish() {
    return this.close > this.open;
  }
}

// ---------------- Technical Indicators ----------------
class TechnicalIndicators {
  // Calculate ATR for dynamic stop-loss
  static calculateATR(data, period = 14) {
    const trueRanges = data.map((candle, i) => {
      if (i === 0) return candle.range;
      const prevClose = data[i - 1].close;
      return Math.max(
        candle.high - candle.low,
        Math.abs(candle.high - prevClose),
        Math.abs(candle.low - prevClose)
      );
    });
    let atr = trueRanges[0];
    const result = [atr];
    for (let i = 1; i < data.length; i++) {
      atr = (atr * (period - 1) + trueRanges[i]) / period;
      result.push(atr);
    }
    return result;
  }
}

// ---------------- Trendline Calculation ----------------
// Compute linear regression (slope, intercept, trendValue) over a window of candles.
// x is taken as sequential integers [0, period-1] and y as the close prices.
function computeLinearRegression(candles, period) {
  if (candles.length < period)
    throw new Error('Not enough data for regression');
  let sumX = 0,
    sumY = 0,
    sumXY = 0,
    sumX2 = 0;
  for (let i = 0; i < period; i++) {
    const x = i;
    const y = candles[i].close;
    sumX += x;
    sumY += y;
    sumXY += x * y;
    sumX2 += x * x;
  }
  const n = period;
  const slope = (n * sumXY - sumX * sumY) / (n * sumX2 - sumX * sumX);
  const intercept = (sumY - slope * sumX) / n;
  const trendValue = intercept + slope * (period - 1);
  return { slope, intercept, trendValue };
}

// ---------------- Trendline Signal Generator ----------------
// Uses only price data and a rule-based trendline strategy to generate signals.
class TrendlineSignalGenerator {
  constructor(historicalData) {
    if (
      !historicalData ||
      !Array.isArray(historicalData) ||
      historicalData.length === 0
    ) {
      throw new Error('Invalid historical data provided');
    }
    this.data = historicalData;
    this.regressionPeriod = CONFIG.trendline.regressionPeriod;
  }

  // Generate a trading signal based on the latest candle and the trendline.
  // For an uptrend (positive slope): if the current candle's close is below the trendline
  // AND the deviation is above the threshold, generate a BUY signal.
  // For a downtrend (negative slope): if the current candle's close is above the trendline
  // AND the deviation is above the threshold, generate a SELL signal.
  generateSignal(currentCandle) {
    if (this.data.length < this.regressionPeriod) {
      console.error('Not enough data for trendline calculation');
      return null;
    }
    const windowData = this.data.slice(-this.regressionPeriod);
    const { slope, trendValue } = computeLinearRegression(
      windowData,
      this.regressionPeriod
    );
    const atrValues = TechnicalIndicators.calculateATR(this.data);
    const currentAtr = atrValues[atrValues.length - 1];
    if (!currentAtr || currentAtr <= 0) {
      console.error('Invalid ATR value:', currentAtr);
      return null;
    }
    const atrMultiplier = CONFIG.strategy.atrMultiplier;
    const stopDistance = currentAtr * atrMultiplier;

    // Compute relative deviation from trendline.
    const deviation = Math.abs(currentCandle.close - trendValue) / trendValue;
    console.log('Computed deviation:', deviation.toFixed(4));
    if (deviation < CONFIG.trendline.deviationThreshold) {
      console.log(
        'Signal filtered out due to insufficient deviation:',
        deviation
      );
      return null;
    }

    console.log('Trendline context:', {
      slope,
      trendValue,
      currentCandleClose: currentCandle.close,
      currentAtr,
      deviation
    });

    let signal = null;
    // For uptrend: if current candle is below the trendline, signal BUY.
    if (slope > 0 && currentCandle.close < trendValue) {
      const entry = currentCandle.close;
      const stopLoss = entry - stopDistance;
      const takeProfit = entry + stopDistance * CONFIG.risk.minRiskReward;
      signal = { type: 'BUY', entry, stopLoss, takeProfit };
    }
    // For downtrend: if current candle is above the trendline, signal SELL.
    else if (slope < 0 && currentCandle.close > trendValue) {
      const entry = currentCandle.close;
      const stopLoss = entry + stopDistance;
      const takeProfit = entry - stopDistance * CONFIG.risk.minRiskReward;
      signal = { type: 'SELL', entry, stopLoss, takeProfit };
    }
    if (signal) {
      const riskDistance = Math.abs(signal.stopLoss - signal.entry);
      const rewardDistance = Math.abs(signal.takeProfit - signal.entry);
      const riskRewardRatio = rewardDistance / riskDistance;
      console.log('Signal details:', { ...signal, riskRewardRatio });
      if (riskRewardRatio >= CONFIG.risk.minRiskReward) {
        signal.riskRewardRatio = riskRewardRatio;
        return signal;
      }
    }
    return null;
  }
}

// ---------------- Trade Outcome Simulation ----------------
// Simulate the trade outcome by scanning future candles. A minimum holding period is required.
function simulateTradeOutcome(signal, futureCandles) {
  // Skip the first CONFIG.simulation.minHoldingPeriod candles to allow a grace period.
  const startIndex = CONFIG.simulation.minHoldingPeriod;
  for (let i = startIndex; i < futureCandles.length; i++) {
    const candle = futureCandles[i];
    if (signal.type === 'BUY') {
      const reachedTP = candle.high >= signal.takeProfit;
      const reachedSL = candle.low <= signal.stopLoss;
      if (reachedTP && reachedSL) {
        return { outcome: 'ambiguous', candleIndex: i };
      } else if (reachedTP) {
        return { outcome: 'success', candleIndex: i };
      } else if (reachedSL) {
        return { outcome: 'failure', candleIndex: i };
      }
    } else if (signal.type === 'SELL') {
      const reachedTP = candle.low <= signal.takeProfit;
      const reachedSL = candle.high >= signal.stopLoss;
      if (reachedTP && reachedSL) {
        return { outcome: 'ambiguous', candleIndex: i };
      } else if (reachedTP) {
        return { outcome: 'success', candleIndex: i };
      } else if (reachedSL) {
        return { outcome: 'failure', candleIndex: i };
      }
    }
  }
  return { outcome: 'no outcome', candleIndex: null };
}

// ---------------- Risk Management ----------------
class RiskManager {
  constructor(accountBalance) {
    this.accountBalance = accountBalance;
    this.dailyPnL = 0;
    this.openPositions = new Map();
  }

  async calculatePositionSize(entry, stopLoss, symbol) {
    const riskAmount = this.accountBalance * CONFIG.risk.maxRiskPerTrade;
    const pipValue = 1; // Simplified pip value
    const stopDistance = Math.abs(entry - stopLoss);
    const positionSize = (riskAmount / stopDistance) * pipValue;
    return Math.min(positionSize, CONFIG.risk.maxPositionSize);
  }

  canTakeTrade(signal) {
    return (
      this.dailyPnL > -this.accountBalance * CONFIG.risk.maxDailyLoss &&
      this.openPositions.size < 3 &&
      signal.riskRewardRatio >= CONFIG.risk.minRiskReward
    );
  }

  updatePnL(pnl) {
    this.dailyPnL += pnl;
    return this.dailyPnL;
  }
}

// ---------------- Trendline Trading System ----------------
class TrendlineTradingSystem {
  constructor(accountBalance) {
    this.riskManager = new RiskManager(accountBalance);
    this.historicalData = [];
    this.signalGenerator = null;
    this.stats = {
      totalSignals: 0,
      successes: 0,
      failures: 0,
      ambiguous: 0,
      noOutcome: 0
    };
  }

  async initialize() {
    this.historicalData = await fetchHistoricalData();
    console.log(
      'Historical data loaded:',
      this.historicalData.length,
      'data points'
    );
    this.signalGenerator = new TrendlineSignalGenerator(this.historicalData);
  }

  // Analyze a new candle, update historical data, and generate a signal.
  async analyzeMarket(newCandle, symbol) {
    this.historicalData.push(newCandle);
    this.signalGenerator.data = this.historicalData;
    const signal = this.signalGenerator.generateSignal(newCandle);
    if (signal && this.riskManager.canTakeTrade(signal)) {
      const positionSize = await this.riskManager.calculatePositionSize(
        signal.entry,
        signal.stopLoss,
        symbol
      );
      signal.positionSize = positionSize;
      return signal;
    }
    return null;
  }
}

// ---------------- Usage Example and Simulation ----------------
async function main() {
  const accountBalance = 10000;
  const tradingSystem = new TrendlineTradingSystem(accountBalance);

  try {
    await tradingSystem.initialize();
    console.log('Trendline trading system initialized');

    // Fetch testing data for simulation
    const testingData = await fetchTestingData();
    let totalSignals = 0,
      successes = 0,
      failures = 0,
      ambiguous = 0,
      noOutcome = 0;

    for (let i = 0; i < testingData.length; i++) {
      const candleData = testingData[i];
      const signal = await tradingSystem.analyzeMarket(
        candleData,
        'Step Index'
      );
      if (signal) {
        totalSignals++;
        console.log('Trade Signal Generated at', candleData.time, ':', signal);
        const futureCandles = testingData.slice(i + 1);
        const outcome = simulateTradeOutcome(signal, futureCandles);
        console.log(
          `Outcome for signal at ${candleData.time}:`,
          outcome.outcome
        );
        if (outcome.outcome === 'success') {
          successes++;
        } else if (outcome.outcome === 'failure') {
          failures++;
        } else if (outcome.outcome === 'ambiguous') {
          ambiguous++;
        } else {
          noOutcome++;
        }
      } else {
        console.log('No valid trading signal for candle at', candleData.time);
      }
    }

    console.log('Signal Simulation Results:');
    console.log('Total Signals:', totalSignals);
    console.log('Successful Signals:', successes);
    console.log('Failed Signals:', failures);
    console.log('Ambiguous Outcomes:', ambiguous);
    console.log('No Outcome:', noOutcome);
  } catch (error) {
    console.error('Error in trading system:', error);
  }
}

main().catch(console.error);
