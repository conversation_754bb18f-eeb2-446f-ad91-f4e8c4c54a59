const { fetchTestingData, fetchTodayTestingData, fetchSyntheticTestingData, calculateATR } = require('./trade-system-lib');
const { SMA } = require('technicalindicators');
const fs = require('fs');
const path = require('path');

/**
 * Calculate SMA values for the candles
 */
function calculateSMA(candles, period = 20) {
  const closes = candles.map(c => c.close);
  const sma = SMA.calculate({ period, values: closes });
  console.log('SMA calculation:', {
    inputLength: closes.length,
    smaLength: sma.length,
    firstSMA: sma[0],
    lastSMA: sma[sma.length - 1]
  });
  return sma;
}

/**
 * Find swing points in price action with minimal filtering
 */
function findSwingPoints(candles, lookback = 2) { // Reduced lookback to 2
  console.log('\nValidating candle data:');
  console.log('Total candles:', candles.length);
  console.log('First candle:', JSON.stringify(candles[0], null, 2));
  console.log('Last candle:', JSON.stringify(candles[candles.length - 1], null, 2));
  
  const swings = [];
  const smaValues = calculateSMA(candles);
  
  // Simple high/low detection without SMA filtering initially
  for (let i = lookback; i < candles.length - lookback; i++) {
    const current = candles[i];
    const before = candles.slice(i - lookback, i);
    const after = candles.slice(i + 1, i + lookback + 1);
    
    // Check for any significant price movement
    const isHigherThanBefore = before.every(c => c.high < current.high);
    const isHigherThanAfter = after.every(c => c.high < current.high);
    const isLowerThanBefore = before.every(c => c.low > current.low);
    const isLowerThanAfter = after.every(c => c.low > current.low);
    
    if (isHigherThanBefore && isHigherThanAfter) {
      console.log(`\nPotential swing high at index ${i}:`, {
        price: current.high,
        time: current.time
      });
      swings.push({
        type: 'high',
        price: current.high,
        index: i,
        candle: current,
        sma: smaValues[i - 20 + smaValues.length] || null
      });
    }
    
    if (isLowerThanBefore && isLowerThanAfter) {
      console.log(`\nPotential swing low at index ${i}:`, {
        price: current.low,
        time: current.time
      });
      swings.push({
        type: 'low',
        price: current.low,
        index: i,
        candle: current,
        sma: smaValues[i - 20 + smaValues.length] || null
      });
    }
  }
  
  console.log('\nSwing points found:', swings.length);
  swings.forEach((swing, i) => {
    console.log(`Swing ${i + 1}: ${swing.type} at price ${swing.price}, time: ${swing.candle.time}`);
  });
  
  return swings;
}

/**
 * Detect pullback based on price action
 */
function detectPullback(candles) {
  const lookback = 20;
  if (candles.length < lookback * 2) {
    console.log('Not enough candles for analysis:', candles.length);
    return null;
  }
  
  const recentCandles = candles.slice(-lookback);
  const smaValues = calculateSMA(candles);
  const currentSMA = smaValues[smaValues.length - 1];
  const currentCandle = recentCandles[recentCandles.length - 1];
  const atr = calculateATR(recentCandles);
  
  // Calculate additional market metrics
  const priceChange = ((currentCandle.close - recentCandles[0].close) / recentCandles[0].close) * 100;
  const volatility = (atr / currentCandle.close) * 100;
  const trend = currentCandle.close > currentSMA ? 'BULLISH' : 'BEARISH';
  const momentum = recentCandles.slice(-3).reduce((acc, curr) => acc + (curr.close - curr.open), 0);
  
  console.log('\n╔══ MARKET CONDITIONS ' + '═'.repeat(58) + '╗');
  console.log('║                                                                            ║');
  console.log(`║  Time: ${new Date(currentCandle.time).toLocaleString().padEnd(40)}                ║`);
  console.log('║                                                                            ║');
  console.log(`║  Current Price: $${currentCandle.close.toFixed(2).padEnd(10)}  SMA: $${currentSMA.toFixed(2).padEnd(10)}  ATR: ${atr.toFixed(5).padEnd(8)}     ║`);
  console.log(`║  Price Change: ${priceChange >= 0 ? '▲' : '▼'} ${Math.abs(priceChange).toFixed(2).padEnd(6)}%  Volatility: ${volatility.toFixed(2).padEnd(6)}%                      ║`);
  console.log(`║  Market Trend: ${trend.padEnd(8)}  Momentum: ${momentum >= 0 ? '▲' : '▼'} ${Math.abs(momentum).toFixed(5).padEnd(8)}              ║`);
  console.log(`║  SMA Distance: ${((Math.abs(currentCandle.close - currentSMA) / currentSMA) * 100).toFixed(2).padEnd(6)}%                                                ║`);
  console.log('║                                                                            ║');
  console.log('╚' + '═'.repeat(76) + '╝');
  
  const swingPoints = findSwingPoints(candles);
  
  if (swingPoints.length < 2) {
    console.log('Not enough swing points found');
    return null;
  }
  
  const pullbacks = [];
  
  for (let i = 1; i < swingPoints.length; i++) {
    const current = swingPoints[i];
    const previous = swingPoints[i - 1];
    
    if (current.type === previous.type) continue;
    
    const movement = Math.abs(current.price - previous.price);
    const retracement = Math.abs(currentCandle.close - current.price);
    const retracementRatio = retracement / movement;
    
    console.log(`\nAnalyzing swing pair at ${new Date(current.candle.time).toLocaleString()}:`, {
      currentType: current.type,
      previousType: previous.type,
      movement: movement.toFixed(5),
      retracement: retracement.toFixed(5),
      retracementRatio: (retracementRatio * 100).toFixed(1) + '%',
      atr: atr.toFixed(5)
    });
    
    let score = 0;
    
    // Check for valid retracement ratio (between 30% and 70%)
    if (retracementRatio >= 0.3 && retracementRatio <= 0.7) {
      score += 0.3;
      console.log('Valid retracement ratio (30-70%), score +0.3');
    }
    
    // Check trend alignment with SMA
    const trendAligned = (current.type === 'low' && currentCandle.close > currentSMA) ||
                        (current.type === 'high' && currentCandle.close < currentSMA);
    if (trendAligned) {
      score += 0.2;
      console.log('Price aligned with SMA trend, score +0.2');
    }
    
    // Check momentum using last 3 candles
    const last3Candles = recentCandles.slice(-3);
    const momentumScore = last3Candles[2].close - last3Candles[0].close;
    const momentumAligned = (current.type === 'low' && momentumScore > 0) ||
                          (current.type === 'high' && momentumScore < 0);
    if (momentumAligned) {
      score += 0.2;
      console.log('Price momentum aligned with trade direction, score +0.2');
    }
    
    // Check if movement is significant compared to ATR
    if (movement > atr * 2) {
      score += 0.2;
      console.log('Significant price movement (> 2 ATR), score +0.2');
    }
    
    // Check volume trend (if available)
    if (currentCandle.volume > recentCandles.slice(-5).reduce((sum, c) => sum + c.volume, 0) / 5) {
      score += 0.1;
      console.log('Above average volume, score +0.1');
    }
    
    console.log('Total score:', score.toFixed(2));
    
    if (score >= 0.5) {
      pullbacks.push({
        from: previous,
        to: current,
        score,
        retracementRatio,
        movement,
        retracement,
        atr,
        sma: currentSMA,
        levels: calculateTradeLevels(current, previous, atr, currentSMA)
      });
    }
  }
  
  return pullbacks;
}

/**
 * Calculate trade levels using SMA and swing points
 */
function calculateTradeLevels(current, previous, atr, sma) {
  const direction = current.type === 'low' ? 'up' : 'down';
  const movement = Math.abs(current.price - previous.price);
  
  let entry, stopLoss, takeProfit;
  
  if (direction === 'up') {
    entry = current.price;
    stopLoss = Math.max(entry - (atr * 1.5), current.price - (movement * 0.3));
    takeProfit = entry + (movement * 0.8);
  } else {
    entry = current.price;
    stopLoss = Math.min(entry + (atr * 1.5), current.price + (movement * 0.3));
    takeProfit = entry - (movement * 0.8);
  }
  
  const risk = Math.abs(entry - stopLoss);
  const reward = Math.abs(entry - takeProfit);
  const riskReward = reward / risk;
  
  return {
    direction,
    entry,
    stopLoss,
    takeProfit,
    riskReward
  };
}

/**
 * Main function to detect pullback opportunities
 */
function detectPullbackTrades(candles) {
  const pullbacks = detectPullback(candles);
  
  if (!pullbacks || pullbacks.length === 0) {
    return {
      trades: [],
      pullbacks: []
    };
  }
  
  const trades = pullbacks
    .filter(p => p.levels.riskReward >= 1)
    .map(pullback => ({
      type: pullback.levels.direction === 'up' ? 'buy' : 'sell',
      entry: pullback.levels.entry,
      stopLoss: pullback.levels.stopLoss,
      takeProfit: pullback.levels.takeProfit,
      score: pullback.score,
      riskReward: pullback.levels.riskReward,
      movement: pullback.movement,
      retracement: pullback.retracement,
      retracementRatio: pullback.retracementRatio,
      sma: pullback.sma,
      signalTime: new Date(pullback.to.candle.time).toLocaleString(),
      signalIndex: pullback.to.index,
      candleTime: pullback.to.candle.time // Store original timestamp
    }));
  
  return {
    trades,
    pullbacks
  };
}

/**
 * Test a trade signal against future price action
 */
function testSignal(trade, futureCandles) {
  const entryPrice = trade.entry;
  const stopLoss = trade.stopLoss;
  const takeProfit = trade.takeProfit;
  let result = 'OPEN';
  let exitPrice = null;
  let exitTime = null;
  let pnl = 0;
  let maxDrawdown = 0;
  let holdingPeriod = 0;
  
  for (const candle of futureCandles) {
    holdingPeriod++;
    
    if (trade.type === 'buy') {
      // Check stop loss
      if (candle.low <= stopLoss) {
        result = 'LOSS';
        exitPrice = stopLoss;
        exitTime = candle.time;
        pnl = ((stopLoss - entryPrice) / entryPrice) * 100;
        break;
      }
      // Check take profit
      if (candle.high >= takeProfit) {
        result = 'WIN';
        exitPrice = takeProfit;
        exitTime = candle.time;
        pnl = ((takeProfit - entryPrice) / entryPrice) * 100;
        break;
      }
      // Track drawdown
      const currentDrawdown = ((candle.low - entryPrice) / entryPrice) * 100;
      maxDrawdown = Math.min(maxDrawdown, currentDrawdown);
    } else {
      // Check stop loss
      if (candle.high >= stopLoss) {
        result = 'LOSS';
        exitPrice = stopLoss;
        exitTime = candle.time;
        pnl = ((entryPrice - stopLoss) / entryPrice) * 100;
        break;
      }
      // Check take profit
      if (candle.low <= takeProfit) {
        result = 'WIN';
        exitPrice = takeProfit;
        exitTime = candle.time;
        pnl = ((entryPrice - takeProfit) / entryPrice) * 100;
        break;
      }
      // Track drawdown
      const currentDrawdown = ((entryPrice - candle.high) / entryPrice) * 100;
      maxDrawdown = Math.min(maxDrawdown, currentDrawdown);
    }
  }
  
  return {
    result,
    entryPrice,
    exitPrice,
    pnl,
    maxDrawdown,
    holdingPeriod,
    entryTime: new Date(trade.candleTime).toLocaleString(), // Use candlestick time
    exitTime: exitTime ? new Date(exitTime).toLocaleString() : 'Still Open'
  };
}

/**
 * Display trade results with styled console output
 */
function calculateCapitalStats(testResults, initialCapital) {
  let capital = initialCapital;
  let peak = initialCapital;
  let maxDrawdown = 0;
  let consecutiveWins = 0;
  let maxConsecutiveWins = 0;
  let consecutiveLosses = 0;
  let maxConsecutiveLosses = 0;
  let equity = [initialCapital];
  
  testResults.forEach(result => {
    // Update capital
    const pnlAmount = (capital * result.pnl) / 100;
    capital += pnlAmount;
    equity.push(capital);
    
    // Update peak and drawdown
    if (capital > peak) {
      peak = capital;
    }
    const drawdown = ((peak - capital) / peak) * 100;
    maxDrawdown = Math.min(maxDrawdown, drawdown);
    
    // Update streaks
    if (result.result === 'WIN') {
      consecutiveWins++;
      consecutiveLosses = 0;
      maxConsecutiveWins = Math.max(maxConsecutiveWins, consecutiveWins);
    } else if (result.result === 'LOSS') {
      consecutiveLosses++;
      consecutiveWins = 0;
      maxConsecutiveLosses = Math.max(maxConsecutiveLosses, consecutiveLosses);
    }
  });
  
  return {
    finalCapital: capital,
    peak,
    maxDrawdown,
    maxConsecutiveWins,
    maxConsecutiveLosses,
    returnRate: ((capital - initialCapital) / initialCapital) * 100,
    equity
  };
}

function calculateLotSize(capital, riskPercent, entryPrice, stopLoss, symbol) {
  const riskAmount = capital * (riskPercent / 100);
  const priceDiff = Math.abs(entryPrice - stopLoss);
  
  // Contract sizes and pip values for different instruments
  const contractSpecs = {
    // Precious Metals
    'XAUUSD': { contractSize: 100, pipValue: 0.01 },     // Gold (1 lot = 100 oz)
    'XAGUSD': { contractSize: 5000, pipValue: 0.01 },    // Silver (1 lot = 5000 oz)
    
    // Major Pairs (1 standard lot = 100,000 units)
    'EURUSD': { contractSize: 100000, pipValue: 0.0001 }, // EUR/USD
    'GBPUSD': { contractSize: 100000, pipValue: 0.0001 }, // GBP/USD
    'USDJPY': { contractSize: 100000, pipValue: 0.01 },   // USD/JPY
    'USDCHF': { contractSize: 100000, pipValue: 0.0001 }, // USD/CHF
    'USDCAD': { contractSize: 100000, pipValue: 0.0001 }, // USD/CAD
    'AUDUSD': { contractSize: 100000, pipValue: 0.0001 }, // AUD/USD
    'NZDUSD': { contractSize: 100000, pipValue: 0.0001 }, // NZD/USD
    
    // Cross Pairs (1 standard lot = 100,000 units)
    'EURGBP': { contractSize: 100000, pipValue: 0.0001 }, // EUR/GBP
    'GBPJPY': { contractSize: 100000, pipValue: 0.01 },   // GBP/JPY
    'EURJPY': { contractSize: 100000, pipValue: 0.01 },   // EUR/JPY
    'AUDJPY': { contractSize: 100000, pipValue: 0.01 },   // AUD/JPY
    'CHFJPY': { contractSize: 100000, pipValue: 0.01 },   // CHF/JPY
    'EURCHF': { contractSize: 100000, pipValue: 0.0001 }, // EUR/CHF
    'EURAUD': { contractSize: 100000, pipValue: 0.0001 }, // EUR/AUD
    'GBPAUD': { contractSize: 100000, pipValue: 0.0001 }, // GBP/AUD
    'AUDNZD': { contractSize: 100000, pipValue: 0.0001 }, // AUD/NZD
    'EURCAD': { contractSize: 100000, pipValue: 0.0001 }, // EUR/CAD
    'GBPCAD': { contractSize: 100000, pipValue: 0.0001 }, // GBP/CAD
    
    // Deriv Synthetics (Volatility Indices)
    'V10': { contractSize: 1, pipValue: 0.0001 },    // Volatility 10 Index
    'V25': { contractSize: 1, pipValue: 0.0001 },    // Volatility 25 Index
    'V50': { contractSize: 1, pipValue: 0.0001 },    // Volatility 50 Index
    'V75': { contractSize: 1, pipValue: 0.0001 },    // Volatility 75 Index
    'V100': { contractSize: 1, pipValue: 0.0001 },   // Volatility 100 Index
    'VIX': { contractSize: 1, pipValue: 0.0001 },    // Volatility Index
    
    // Deriv Synthetics (Crash/Boom Indices)
    'CRASH500': { contractSize: 1, pipValue: 0.0001 },  // Crash 500 Index
    'CRASH1000': { contractSize: 1, pipValue: 0.0001 }, // Crash 1000 Index
    'BOOM500': { contractSize: 1, pipValue: 0.0001 },   // Boom 500 Index
    'BOOM1000': { contractSize: 1, pipValue: 0.0001 },  // Boom 1000 Index
    
    // Deriv Synthetics (Step Indices)
    'STEPINDI': { contractSize: 1, pipValue: 0.0001 },  // Step Index
    'STPINRNG': { contractSize: 1, pipValue: 0.0001 },  // Step Index Range
    
    // Deriv Synthetics (Jump Indices)
    'JUMPINDI': { contractSize: 1, pipValue: 0.0001 },  // Jump Index
    'JMPINRNG': { contractSize: 1, pipValue: 0.0001 }   // Jump Index Range
  };
  
  const spec = contractSpecs[symbol] || { contractSize: 100000, pipValue: 0.0001 }; // Default to standard forex specs
  
  // Calculate position size based on risk
  const pipRisk = priceDiff / spec.pipValue;
  const lotSize = (riskAmount / (pipRisk * spec.contractSize * spec.pipValue));
  
  // Round to 2 decimal places and ensure minimum lot size
  return Math.max(0.01, Math.round(lotSize * 100) / 100).toFixed(2);
}

function displayTradeResults(trades, testResults, initialCapital, symbol) {
  console.log('\n' + '═'.repeat(80));
  console.log('║' + ' '.repeat(20) + `PULLBACK TRADING SYSTEM - ${symbol}` + ' '.repeat(20) + '║');
  console.log('═'.repeat(80));
  
  // Calculate statistics
  let totalTrades = testResults.length;
  let winningTrades = testResults.filter(r => r.result === 'WIN').length;
  let losingTrades = testResults.filter(r => r.result === 'LOSS').length;
  let openTrades = testResults.filter(r => r.result === 'OPEN').length;
  let totalPnL = testResults.reduce((sum, r) => sum + r.pnl, 0);
  let avgPnL = totalPnL / totalTrades;
  let avgHoldingPeriod = testResults.reduce((sum, r) => sum + r.holdingPeriod, 0) / totalTrades;
  
  // Get start and end dates from trade data
  const startDate = trades.length > 0 ? new Date(trades[0].candleTime) : new Date();
  const lastTrade = trades[trades.length - 1];
  const endDate = lastTrade ? new Date(lastTrade.candleTime) : new Date();
  
  // Calculate capital statistics
  const capitalStats = calculateCapitalStats(testResults, initialCapital);
  
  // Display summary with symbol and dates
  console.log('\n╔══ PERFORMANCE SUMMARY ' + '═'.repeat(57) + '╗');
  console.log('║                                                                            ║');
  console.log(`║  Symbol: ${symbol.padEnd(10)}  Testing Period: ${startDate.toLocaleDateString()} - ${endDate.toLocaleDateString()}     ║`);
  console.log(`║  Duration: ${Math.ceil((endDate - startDate) / (1000 * 60 * 60 * 24))} days                                                              ║`);
  console.log(`║  Total Trades: ${totalTrades.toString().padEnd(8)} Win Rate: ${((winningTrades / totalTrades) * 100).toFixed(1).padEnd(6)}%  Success: ${winningTrades}/${totalTrades}        ║`);
  console.log(`║  Total P&L: ${totalPnL.toFixed(2).padEnd(10)}%  Avg P&L: ${avgPnL.toFixed(2).padEnd(6)}%  Max DD: ${capitalStats.maxDrawdown.toFixed(2)}%      ║`);
  console.log(`║  Avg Hold Time: ${avgHoldingPeriod.toFixed(1).padEnd(6)} candles                                          ║`);
  console.log('║                                                                            ║');
  console.log('╚' + '═'.repeat(76) + '╝');
  
  // Capital simulation results
  console.log('\n╔══ CAPITAL SIMULATION ' + '═'.repeat(57) + '╗');
  console.log('║                                                                            ║');
  console.log(`║  Initial Capital: $${initialCapital.toFixed(2).padEnd(10)}  Final Capital: $${capitalStats.finalCapital.toFixed(2).padEnd(10)}              ║`);
  console.log(`║  Total Return: ${capitalStats.returnRate.toFixed(2).padEnd(6)}%  Peak Capital: $${capitalStats.peak.toFixed(2).padEnd(10)}              ║`);
  console.log(`║  Max Drawdown($): $${((capitalStats.peak * capitalStats.maxDrawdown) / 100).toFixed(2).padEnd(8)}  Max Drawdown(%): ${capitalStats.maxDrawdown.toFixed(2).padEnd(6)}%              ║`);
  console.log(`║  Max Win Streak: ${capitalStats.maxConsecutiveWins.toString().padEnd(4)}  Max Loss Streak: ${capitalStats.maxConsecutiveLosses.toString().padEnd(4)}                            ║`);
  console.log('║                                                                            ║');
  console.log('╚' + '═'.repeat(76) + '╝');
  
  // Position distribution
  console.log('\n╔══ POSITION DISTRIBUTION ' + '═'.repeat(54) + '╗');
  console.log('║                                                                            ║');
  
  // Calculate system rating (0-5 stars)
  const winRate = (winningTrades / totalTrades) * 100;
  const profitFactor = Math.abs(
    testResults.filter(r => r.pnl > 0).reduce((sum, r) => sum + r.pnl, 0) /
    testResults.filter(r => r.pnl < 0).reduce((sum, r) => sum + r.pnl, 0) || 1
  );
  const returnRate = (capitalStats.finalCapital - initialCapital) / initialCapital * 100;
  
  let rating = 0;
  if (winRate > 50) rating++;
  if (winRate > 60) rating++;
  if (returnRate > 10) rating++;
  if (profitFactor > 1.5) rating++;
  if (Math.abs(capitalStats.maxDrawdown) < 15) rating++;
  
  const stars = '★'.repeat(rating) + '☆'.repeat(5 - rating);
  console.log(`║  System Rating: ${stars}  ${rating}/5                                           ║`);
  console.log('║                                                                            ║');
  console.log('╚' + '═'.repeat(76) + '╝');
  
  // Trade details with money values
  console.log('\n╔══ DETAILED TRADE RESULTS ' + '═'.repeat(53) + '╗');
  
  let runningCapital = initialCapital;
  testResults.forEach((result, i) => {
    const trade = trades[i];
    const lots = calculateLotSize(
      runningCapital * Math.pow(1 + result.pnl/100, i),
      2, // Risk 2% per trade
      trade.entry,
      trade.stopLoss,
      symbol
    );
    
    console.log('║' + '─'.repeat(76) + '║');
    console.log(
      `║ Trade #${(i + 1).toString().padStart(2)} │ ${result.result.padEnd(4)} │ ${trade.type.toUpperCase().padEnd(4)} │ ` +
      `P&L: $${(runningCapital * result.pnl / 100).toFixed(2).padStart(7)} │ Lots: ${lots.padStart(6)} │ ` +
      `TP: ${trade.takeProfit.toFixed(5)} │ SL: ${trade.stopLoss.toFixed(5)} │ Score: ${trade.score.toFixed(2)} ║`
    );
    console.log(`║ Entry: ${result.entryTime.padEnd(40)} ${result.entryPrice.toFixed(2).padStart(10)} ║`);
    console.log(`║ Exit:  ${result.exitTime.padEnd(40)} ${(result.exitPrice ? result.exitPrice.toFixed(5) : '-').padStart(10)} ║`);
    console.log(`║ Hold:  ${result.holdingPeriod.toString().padEnd(3)} candles   Balance: $${(runningCapital * (1 + result.pnl/100)).toFixed(2).padStart(9)}                           ║`);
    
    runningCapital *= (1 + result.pnl/100);
  });
  
  console.log('║                                                                            ║');
  console.log('╚' + '═'.repeat(76) + '╝');
  
  // System rating with money metrics
  if (winningTrades > 0 || losingTrades > 0) {
    console.log('\n╔══ SYSTEM RATING ' + '═'.repeat(61) + '╗');
    console.log('║                                                                            ║');
    
    console.log(`║  System Rating: ${stars}  ${rating}/5                                           ║`);
    console.log('║                                                                            ║');
    console.log('╚' + '═'.repeat(76) + '╝');
  }
  
  // Trade details with money values
  console.log('\n╔══ DETAILED TRADE RESULTS ' + '═'.repeat(53) + '╗');
  
  runningCapital = initialCapital;
  testResults.forEach((result, i) => {
    const trade = trades[i];
    const lots = calculateLotSize(
      runningCapital * Math.pow(1 + result.pnl/100, i),
      2, // Risk 2% per trade
      trade.entry,
      trade.stopLoss,
      symbol
    );
    
    console.log('║' + '─'.repeat(76) + '║');
    console.log(
      `║ Trade #${(i + 1).toString().padStart(2)} │ ${result.result.padEnd(4)} │ ${trade.type.toUpperCase().padEnd(4)} │ ` +
      `P&L: $${(runningCapital * result.pnl / 100).toFixed(2).padStart(7)} │ Lots: ${lots.padStart(6)} │ ` +
      `TP: ${trade.takeProfit.toFixed(5)} │ SL: ${trade.stopLoss.toFixed(5)} │ Score: ${trade.score.toFixed(2)} ║`
    );
    console.log(`║ Entry: ${result.entryTime.padEnd(40)} ${result.entryPrice.toFixed(2).padStart(10)} ║`);
    console.log(`║ Exit:  ${result.exitTime.padEnd(40)} ${(result.exitPrice ? result.exitPrice.toFixed(5) : '-').padStart(10)} ║`);
    console.log(`║ Hold:  ${result.holdingPeriod.toString().padEnd(3)} candles   Balance: $${(runningCapital * (1 + result.pnl/100)).toFixed(2).padStart(9)}                           ║`);
    
    runningCapital *= (1 + result.pnl/100);
  });
  
  console.log('║                                                                            ║');
  console.log('╚' + '═'.repeat(76) + '╝');
}

function saveSimulationLog(trades, testResults, initialCapital, symbol) {
  const timestamp = new Date().toISOString().replace(/:/g, '-');
  const logDir = path.join(__dirname, 'simulation_logs');
  
  // Create logs directory if it doesn't exist
  if (!fs.existsSync(logDir)) {
    fs.mkdirSync(logDir);
  }
  
  const logFile = path.join(logDir, `${symbol}_${timestamp}.log`);
  let logContent = [];
  
  // Capture console output
  const log = console.log;
  const logs = [];
  console.log = (...args) => {
    logs.push(args.join(' '));
    log.apply(console, args);
  };
  
  // Generate simulation results
  displayTradeResults(trades, testResults, initialCapital, symbol);
  
  // Restore console.log
  console.log = log;
  
  // Write to file
  fs.writeFileSync(logFile, logs.join('\n'));
  console.log(`\nSimulation log saved to: ${logFile}`);
  
  // Save detailed trade data as JSON
  const jsonData = {
    symbol,
    timestamp,
    initialCapital,
    testingPeriod: {
      start: trades.length > 0 ? new Date(trades[0].candleTime).toISOString() : new Date().toISOString(),
      end: trades.length > 0 ? new Date(trades[trades.length - 1].candleTime).toISOString() : new Date().toISOString(),
      durationDays: trades.length > 0 ? 
        Math.ceil((new Date(trades[trades.length - 1].candleTime) - new Date(trades[0].candleTime)) / (1000 * 60 * 60 * 24)) : 0
    },
    summary: {
      totalTrades: testResults.length,
      winningTrades: testResults.filter(r => r.result === 'WIN').length,
      losingTrades: testResults.filter(r => r.result === 'LOSS').length,
      openTrades: testResults.filter(r => r.result === 'OPEN').length,
      finalCapital: testResults.reduce((capital, r) => capital * (1 + r.pnl/100), initialCapital),
      totalPnL: testResults.reduce((sum, r) => sum + r.pnl, 0),
      maxDrawdown: Math.min(...testResults.map(r => r.maxDrawdown))
    },
    trades: trades.map((trade, i) => {
      const currentCapital = initialCapital * 
        testResults.slice(0, i).reduce((acc, r) => acc * (1 + r.pnl/100), 1);
      
      return {
        number: i + 1,
        symbol,
        type: trade.type,
        entry: {
          time: testResults[i].entryTime,
          price: testResults[i].entryPrice
        },
        exit: {
          time: testResults[i].exitTime,
          price: testResults[i].exitPrice
        },
        takeProfit: trade.takeProfit,
        stopLoss: trade.stopLoss,
        lotSize: calculateLotSize(currentCapital, 2, trade.entry, trade.stopLoss, symbol),
        result: testResults[i].result,
        pnl: testResults[i].pnl,
        score: trade.score,
        holdingPeriod: testResults[i].holdingPeriod,
        maxDrawdown: testResults[i].maxDrawdown
      };
    })
  };
  
  const jsonFile = path.join(logDir, `${symbol}_${timestamp}.json`);
  fs.writeFileSync(jsonFile, JSON.stringify(jsonData, null, 2));
  console.log(`Trade data saved to: ${jsonFile}`);
}

/**
 * Start trading function
 */
async function startTrading() {
  try {
    // const symbol = 'BTCUSDm'; // Get symbol from environment or default to XAUUSDm
    // const data = await fetchTestingData(symbol);

    const symbol = 'Volatility 10 Index'; // Get symbol from environment or default to XAUUSDm
    const data = await fetchSyntheticTestingData(symbol);

    const timestamp = new Date().toISOString().replace(/:/g, '-');
    const logDir = path.join(__dirname, 'logs');
    const logFile = path.join(logDir, `${symbol}_api_${timestamp}.log`);
    if (!fs.existsSync(logDir)) {
      fs.mkdirSync(logDir, { recursive: true });
    }
    fs.writeFileSync(logFile, JSON.stringify(data));

    const initialCapital = 500;
    
    const result = detectPullbackTrades(data);
    const testResults = result.trades.map(trade => {
      const futureCandles = data.slice(trade.signalIndex + 1);
      return testSignal(trade, futureCandles);
    });
    
    // Display and save results with symbol
    displayTradeResults(result.trades, testResults, initialCapital, symbol);
    saveSimulationLog(result.trades, testResults, initialCapital, symbol);
    
  } catch (error) {
    console.error('Error:', error.stack);
  }
}

startTrading().catch(console.error);
