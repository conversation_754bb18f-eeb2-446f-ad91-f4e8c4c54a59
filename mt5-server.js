const express = require('express');
const bodyParser = require('body-parser');
const process = require('process');
const apiClient = require('./mt5-service'); 
const cors = require('cors');
const compression = require('compression');
const {sendBotOrder} = require('./bot-lib');
const { processQuickProfit } = require('./process-quick-profit');
const tradeLib = require('./trade-lib');
const {findBotById, tokenExist} = require('./supabaseService');
const { minifyOrder } = require('./util');
const {findSupportResistanceLevels} = require('./candle-resistance-support-detector');
const swaggerJsDoc = require('swagger-jsdoc');
const swaggerUi = require('swagger-ui-express');

const WebSocketServer = require('./lib/websocket-server');

// Constants
const DEFAULT_PORT = 80;
const DEFAULT_SLIPPAGE = 10000;

const blacklist_tokens = [];

const blacklistInterval = setInterval(() => {
  removeFromBlacklist();
}, 1000 * 60 * 60);


const addToBlacklist = (token) => {
  blacklist_tokens.push(token);
};


const isTokenInBlacklist = (token) => {
  return false; //blacklist_tokens.includes(token);
};


const removeFromBlacklist = (token) => {
  if(!isTokenInBlacklist(token)) {
    return;
  }
  blacklist_tokens = blacklist_tokens.filter(t => t !== token);
};

const checkAndBlacklist = async (token) => {
  let exists = await tokenExist(token);
  if(!exists || exists.length === 0) {
    addToBlacklist(token);
  }
};

// Error handling for uncaught exceptions
process.on('uncaughtException', (err) => {
  console.error('Uncaught Exception:', {
    message: err.message,
    stack: err.stack,
    timestamp: new Date().toISOString()
  });
  clearInterval(blacklistInterval);
});

/**
 * Response handler wrapper for consistent error handling
 * @param {Function} handler - Async route handler
 * @returns {Function} - Express middleware
 */
const asyncHandler = (handler) => async (req, res, next) => {
  try {
    await handler(req, res, next);
  } catch (error) {
    console.error('Request failed:', {
      path: req.path,
      method: req.method,
      error: error.message,
      stack: error.stack
    });
    res.status(500).json(createResponse(false, null, 'Internal server error', error));
  }
};

/**
 * Validate required parameters middleware
 * @param {string[]} params - Required parameter names
 * @returns {Function} - Express middleware
 */
const validateParams = (params) => (req, res, next) => {
  const data = { ...req.query, ...req.body };
  const missingParams = params.filter(param => !(param in data));
  
  if (missingParams.length > 0) {
    return res.status(400).json(createResponse(false, null, 'Missing required parameters', new Error(`Missing parameters: ${missingParams.join(', ')}`)));
  }
  next();
};

// Standard response format function
const createResponse = (success, data = null, message = '', error = null) => ({
  success,
  data,
  message,
  ...(error && { error: process.env.NODE_ENV === 'development' ? error : error.message })
});

// Initialize Express app
const app = express();

// Swagger Configuration
const swaggerOptions = {
  definition: {
    openapi: '3.0.0',
    info: {
      title: 'MT5 Trading API',
      version: '1.0.0',
      description: 'API for MetaTrader 5 trading operations',
      contact: {
        name: 'Israel Umeadi'
      },
      servers: [
        {
          url: 'http://localhost:80',
          description: 'Development server'
        }
      ]
    },
    components: {
      securitySchemes: {
        bearerAuth: {
          type: 'http',
          scheme: 'bearer',
          bearerFormat: 'JWT'
        }
      }
    },
    security: [{
      bearerAuth: []
    }]
  },
  apis: ['./mt5-server.js']
};

const swaggerDocs = swaggerJsDoc(swaggerOptions);

// ws server
// const server = http.createServer(app);
// const wss = new WebSocket.Server({ server });


// const wsServer = new WebSocketServer({
//     server,
//     validateClientId: (clientId) => {
//         return /^\d{6}$/.test(clientId); // 6-digit account number
//     },
//     onMessage: (ws, clientId, message, server) => {
//         try {
//             const data = JSON.parse(message);
//             server.sendToClient(clientId, {
//                 type: 'private',
//                 message: `Private message for ${clientId}`,
//                 original: data
//             });
//         } catch (error) {
//             console.error('Error processing message:', error);
//         }
//     },
//     onConnection: (ws, clientId, server) => {
//         console.log(`Client ${clientId} connected`);
//     }
// });

// wsServer.start();

// // Start a real-time service that sends current time every 5 seconds
// wsServer.startRealtimeService('timeUpdate', 5000, () => ({
//     currentTime: new Date().toLocaleTimeString(),
//     serverStatus: 'online',
//     connectedClients: wsServer.getClientCount()
// }));

// // Optional: Start another service with different interval
// wsServer.startRealtimeService('randomNumber', 2000, () => ({
//     randomValue: Math.floor(Math.random() * 100)
// }));



// CORS configuration
const corsOptions = {
  origin: [
    'http://localhost:3000',
    'http://127.0.0.1:3000',
    'https://izywezy-app-6942ea444326.herokuapp.com',
    'https://trade-manager-signal-provider-06ba11a91a44.herokuapp.com'
  ],
  methods: ['GET', 'POST', 'PUT', 'DELETE', 'OPTIONS'],
  allowedHeaders: [
    'Content-Type',
    'Authorization',
    'X-Requested-With',
    'Access-Control-Allow-Origin',
    'Access-Control-Allow-Methods',
    'Access-Control-Allow-Headers'
  ],
  credentials: true,
  optionsSuccessStatus: 200
};

// Middleware
app.use(cors(corsOptions));
app.use(compression());
app.use(bodyParser.json());
app.use(bodyParser.urlencoded({ extended: true }));

// Basic security middleware
app.use((req, res, next) => {
  res.setHeader('X-Content-Type-Options', 'nosniff');
  res.setHeader('X-Frame-Options', 'DENY');
  res.setHeader('X-XSS-Protection', '1; mode=block');
  next();
});

// Middleware to check for blacklisted tokens
const checkBlacklistedToken = (req, res, next) => {
  const token = req.headers['authorization'] || req.body.tradeAccountToken;
  
  if (token && isTokenInBlacklist(token)) {
    return res.status(401).json(createResponse(false, null, 'Token is blacklisted', new Error('This token has been invalidated. Please authenticate again.')));
  }
  next();
};

// Apply the blacklist check middleware to all routes
app.use(checkBlacklistedToken);

// Swagger UI setup
app.use('/api-docs', swaggerUi.serve, swaggerUi.setup(swaggerDocs, { explorer: true }));

/**
 * @swagger
 * /connect:
 *   get:
 *     summary: Connect to a MetaTrader 5 account
 *     description: Establishes a connection to a MetaTrader 5 account using provided credentials
 *     tags: [Connection]
 *     parameters:
 *       - in: query
 *         name: login
 *         required: true
 *         schema:
 *           type: string
 *         description: MT5 account login
 *       - in: query
 *         name: password
 *         required: true
 *         schema:
 *           type: string
 *         description: MT5 account password
 *       - in: query
 *         name: host
 *         required: true
 *         schema:
 *           type: string
 *         description: MT5 server host
 *       - in: query
 *         name: port
 *         required: true
 *         schema:
 *           type: string
 *         description: MT5 server port
 *     responses:
 *       200:
 *         description: Connection successful
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                 data:
 *                   type: object
 *                 message:
 *                   type: string
 *       500:
 *         description: Connection failed
 */
app.get('/connect', asyncHandler(async (req, res) => {
  const { login, password, host, port } = req.query;
  try {
    const data = await apiClient.connect(login, password, host, port);
    res.json(createResponse(true, data, 'Connected successfully'));
  } catch (error) {
    res.status(500).json(createResponse(false, null, 'Connection failed', error));
  }
}));

/**
 * @swagger
 * /connectProxy:
 *   get:
 *     summary: Connect to a MetaTrader 5 account using a proxy
 *     description: Establishes a connection to a MetaTrader 5 account using provided credentials and proxy settings
 *     tags: [Connection]
 *     parameters:
 *       - in: query
 *         name: login
 *         required: true
 *         schema:
 *           type: string
 *         description: MT5 account login
 *       - in: query
 *         name: password
 *         required: true
 *         schema:
 *           type: string
 *         description: MT5 account password
 *       - in: query
 *         name: host
 *         required: true
 *         schema:
 *           type: string
 *         description: MT5 server host
 *       - in: query
 *         name: port
 *         required: true
 *         schema:
 *           type: string
 *         description: MT5 server port
 *       - in: query
 *         name: proxyUser
 *         required: true
 *         schema:
 *           type: string
 *         description: Proxy username
 *       - in: query
 *         name: proxyPassword
 *         required: true
 *         schema:
 *           type: string
 *         description: Proxy password
 *       - in: query
 *         name: proxyHost
 *         required: true
 *         schema:
 *           type: string
 *         description: Proxy host
 *       - in: query
 *         name: proxyPort
 *         required: true
 *         schema:
 *           type: string
 *         description: Proxy port
 *       - in: query
 *         name: proxyType
 *         required: true
 *         schema:
 *           type: string
 *         description: Proxy type
 *     responses:
 *       200:
 *         description: Connection successful
 *       500:
 *         description: Connection failed
 */
app.get('/connectProxy', asyncHandler(async (req, res) => {
  const {
    login,
    password,
    host,
    port,
    proxyUser,
    proxyPassword,
    proxyHost,
    proxyPort,
    proxyType
  } = req.query;
  try {
    const data = await apiClient.connectProxy(
      login,
      password,
      host,
      port,
      proxyUser,
      proxyPassword,
      proxyHost,
      proxyPort,
      proxyType
    );
    res.json(createResponse(true, data, 'Connected successfully'));
  } catch (error) {
    res.status(500).json(createResponse(false, null, 'Connection failed', error));
  }
}));

/**
 * @swagger
 * /connectPost:
 *   post:
 *     summary: Connect to a MetaTrader 5 account using POST method
 *     description: Establishes a connection to a MetaTrader 5 account using provided credentials via POST
 *     tags: [Connection]
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             required:
 *               - login
 *               - password
 *               - host
 *               - port
 *             properties:
 *               login:
 *                 type: string
 *                 description: MT5 account login
 *               password:
 *                 type: string
 *                 description: MT5 account password
 *               host:
 *                 type: string
 *                 description: MT5 server host
 *               port:
 *                 type: string
 *                 description: MT5 server port
 *     responses:
 *       200:
 *         description: Connection successful
 *       500:
 *         description: Connection failed
 */
app.post('/connectPost', asyncHandler(async (req, res) => {
  const { login, password, host, port } = req.body;
  try {
    const data = await apiClient.connectPost(login, password, host, port);
    res.json(createResponse(true, data, 'Connected successfully'));
  } catch (error) {
    res.status(500).json(createResponse(false, null, 'Connection failed', error));
  }
}));

/**
 * @swagger
 * /checkConnect:
 *   get:
 *     summary: Check connection state
 *     description: Checks the state of connection to a MetaTrader 5 account
 *     tags: [Connection]
 *     parameters:
 *       - in: query
 *         name: id
 *         required: true
 *         schema:
 *           type: string
 *         description: Connection ID or token
 *     responses:
 *       200:
 *         description: Connection state checked successfully
 *       500:
 *         description: Error checking connection state
 */
app.get('/checkConnect', asyncHandler(async (req, res) => {
  const { id } = req.query;
  try {
    const data = await apiClient.checkConnect(id);
    res.json(createResponse(true, data, 'Connection state checked successfully'));
  } catch (error) {
    console.error('Error checking connection state:', error);
    res.status(500).json(createResponse(false, null, 'Error checking connection state', error));
  }
}));

/**
 * @swagger
 * /disconnect:
 *   get:
 *     summary: Disconnect from account
 *     description: Disconnects from a MetaTrader 5 account
 *     tags: [Connection]
 *     parameters:
 *       - in: query
 *         name: id
 *         required: true
 *         schema:
 *           type: string
 *         description: Connection ID or token
 *     responses:
 *       200:
 *         description: Disconnected successfully
 *       500:
 *         description: Error disconnecting from account
 */
app.get('/disconnect', asyncHandler(async (req, res) => {
  const { id } = req.query;
  try {
    const data = await apiClient.disconnect(id);
    res.json(createResponse(true, data, 'Disconnected successfully'));
  } catch (error) {
    res.status(500).json(createResponse(false, null, 'Error disconnecting from account', error));
  }
}));

/**
 * @swagger
 * /accountSummary:
 *   get:
 *     summary: Get account summary
 *     description: Retrieves summary information for a MetaTrader 5 account
 *     tags: [Account]
 *     parameters:
 *       - in: query
 *         name: id
 *         required: true
 *         schema:
 *           type: string
 *         description: Connection ID or token
 *     responses:
 *       200:
 *         description: Account summary retrieved successfully
 *       500:
 *         description: Error retrieving account summary
 */
app.get('/accountSummary', asyncHandler(async (req, res) => {
  const { id } = req.query;
  try {
    const data = await apiClient.accountSummary(id);
    res.json(createResponse(true, data, 'Account summary retrieved successfully'));
  } catch (error) {
    res.status(500).json(createResponse(false, null, 'Error retrieving account summary', error));
  }
}));

/**
 * @swagger
 * /openedOrders:
 *   get:
 *     summary: Get list of opened orders
 *     description: Retrieves all opened orders for a MetaTrader 5 account
 *     tags: [Orders]
 *     parameters:
 *       - in: query
 *         name: id
 *         required: true
 *         schema:
 *           type: string
 *         description: Connection ID or token
 *     responses:
 *       200:
 *         description: Orders retrieved successfully
 *       500:
 *         description: Error retrieving opened orders
 */
app.get('/openedOrders', asyncHandler(async (req, res) => {
  const { id } = req.query;
  try {
    const data = await apiClient.openedOrders(id);
    res.json(createResponse(true, data, 'Orders retrieved successfully'));
  } catch (error) {
    checkAndBlacklist(id);
    res.status(500).json(createResponse(false, null, 'Error retrieving opened orders', error));
  }
}));



/**
 * @swagger
 * /openedOrdersCount:
 *   get:
 *     summary: Get count of opened orders
 *     description: Retrieves the count of all opened orders for a MetaTrader 5 account
 *     tags: [Orders]
 *     parameters:
 *       - in: query
 *         name: id
 *         required: true
 *         schema:
 *           type: string
 *         description: Connection ID or token
 *     responses:
 *       200:
 *         description: Orders retrieved successfully
 *       500:
 *         description: Error retrieving opened orders
 */
app.get('/openedOrdersCount', asyncHandler(async (req, res) => {
  const { id } = req.query;
  try {
    const data = await apiClient.openedOrders(id);
    const count = data.length;
    res.json(createResponse(true, {count}, 'Orders retrieved successfully'));
  } catch (error) {
    checkAndBlacklist(id);
    res.status(500).json(createResponse(false, null, 'Error retrieving opened orders', error));
  }
}));



/**
 * @swagger
 * /openedOrdersMini:
 *   get:
 *     summary: Get opened orders in a minimized format
 *     description: Retrieves currently opened orders for a MetaTrader 5 account in a minimized format with reduced data
 *     tags: [Orders]
 *     parameters:
 *       - in: query
 *         name: id
 *         required: true
 *         schema:
 *           type: string
 *         description: Connection ID or token
 *     responses:
 *       200:
 *         description: Orders retrieved successfully
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                 data:
 *                   type: array
 *                   items:
 *                     type: object
 *                     description: Minimized order information
 *       500:
 *         description: Error retrieving opened orders
 */
app.get('/openedOrdersMini', asyncHandler(async (req, res) => {
  const { id } = req.query;
  try {
    const data = await apiClient.openedOrders(id);
    const miniData = data.map(minifyOrder);
    res.json(createResponse(true, miniData, 'Orders retrieved successfully'));
  } catch (error) {
    checkAndBlacklist(id);
    res.status(500).json(createResponse(false, null, 'Error retrieving opened orders', error));
  }
}));


/**
 * @swagger
 * /orderHistory:
 *   get:
 *     summary: Get order history
 *     description: Retrieves order history for a MetaTrader 5 account
 *     tags: [Orders]
 *     parameters:
 *       - in: query
 *         name: id
 *         required: true
 *         schema:
 *           type: string
 *         description: Connection ID or token
 *     responses:
 *       200:
 *         description: Order history retrieved successfully
 *       500:
 *         description: Error retrieving order history
 */
app.get('/orderHistory', asyncHandler(async (req, res) => {
  const { id } = req.query;
  try {
    const data = await apiClient.orderHistory(id);
    res.json(createResponse(true, data, 'Order history retrieved successfully'));
  } catch (error) {
    res.status(500).json(createResponse(false, null, 'Error retrieving order history', error));
  }
}));

/**
 * @swagger
 * /getQuote:
 *   get:
 *     summary: Get latest quote for a symbol
 *     description: Retrieves the latest quote for the specified symbol
 *     tags: [Market Data]
 *     parameters:
 *       - in: query
 *         name: id
 *         required: true
 *         schema:
 *           type: string
 *         description: Connection ID or token
 *       - in: query
 *         name: symbol
 *         required: true
 *         schema:
 *           type: string
 *         description: Trading symbol (e.g., EURUSD)
 *     responses:
 *       200:
 *         description: Quote retrieved successfully
 *       500:
 *         description: Error retrieving quote
 */
app.get('/getQuote', asyncHandler(async (req, res) => {
  const { id, symbol } = req.query;
  try {
    const data = await apiClient.getQuote(id, symbol);
    res.json(createResponse(true, data, 'Quote retrieved successfully'));
  } catch (error) {
    checkAndBlacklist(id);
    res.status(500).json(createResponse(false, null, 'Error retrieving quote', error));
  }
}));

/**
 * @swagger
 * /symbolParams:
 *   get:
 *     summary: Get symbol parameters
 *     description: Retrieves full information about a symbol and its group
 *     tags: [Market Data]
 *     parameters:
 *       - in: query
 *         name: id
 *         required: true
 *         schema:
 *           type: string
 *         description: Connection ID or token
 *       - in: query
 *         name: symbol
 *         required: true
 *         schema:
 *           type: string
 *         description: Trading symbol (e.g., EURUSD)
 *     responses:
 *       200:
 *         description: Symbol parameters retrieved successfully
 *       500:
 *         description: Error retrieving symbol parameters
 */
app.get('/symbolParams', asyncHandler(async (req, res) => {
  const { id, symbol } = req.query;
  try {
    const data = await apiClient.symbolParams(id, symbol);
    res.json(createResponse(true, data, 'Symbol parameters retrieved successfully'));
  } catch (error) {
    res.status(500).json(createResponse(false, null, 'Error retrieving symbol parameters', error));
  }
}));

/**
 * @swagger
 * /serverTimezone:
 *   get:
 *     summary: Get server timezone
 *     description: Retrieves the timezone of the MetaTrader 5 server
 *     tags: [System]
 *     parameters:
 *       - in: query
 *         name: id
 *         required: true
 *         schema:
 *           type: string
 *         description: Connection ID or token
 *     responses:
 *       200:
 *         description: Server timezone retrieved successfully
 *       500:
 *         description: Error retrieving server timezone
 */
app.get('/serverTimezone', asyncHandler(async (req, res) => {
  const { id } = req.query;
  try {
    const data = await apiClient.serverTimezone(id);
    res.json(createResponse(true, data, 'Server timezone retrieved successfully'));
  } catch (error) {
    res.status(500).json(createResponse(false, null, 'Error retrieving server timezone', error));
  }
}));

/**
 * @swagger
 * /isTradeSession:
 *   get:
 *     summary: Check if market is open
 *     description: Checks if the market is currently open for trading for a specific symbol
 *     tags: [Market Data]
 *     parameters:
 *       - in: query
 *         name: id
 *         required: true
 *         schema:
 *           type: string
 *         description: Connection ID or token
 *       - in: query
 *         name: symbol
 *         required: true
 *         schema:
 *           type: string
 *         description: Trading symbol (e.g., EURUSD)
 *     responses:
 *       200:
 *         description: Trade session checked successfully
 *       500:
 *         description: Error checking trade session
 */
app.get('/isTradeSession', asyncHandler(async (req, res) => {
  const { id, symbol } = req.query;
  try {
    const data = await apiClient.isTradeSession(id, symbol);
    res.json(createResponse(true, data, 'Trade session checked successfully'));
  } catch (error) {
    res.status(500).json(createResponse(false, null, 'Error checking trade session', error));
  }
}));

/**
 * @swagger
 * /symbols:
 *   get:
 *     summary: Get list of available symbols
 *     description: Retrieves a list of all available trading symbols
 *     tags: [Market Data]
 *     parameters:
 *       - in: query
 *         name: id
 *         required: true
 *         schema:
 *           type: string
 *         description: Connection ID or token
 *     responses:
 *       200:
 *         description: Symbols retrieved successfully
 *       500:
 *         description: Error retrieving symbols
 */
app.get('/symbols', asyncHandler(async (req, res) => {
  const { id } = req.query;
  try {
    const data = await apiClient.symbols(id);
    res.json(createResponse(true, data, 'Symbols retrieved successfully'));
  } catch (error) {
    res.status(500).json(createResponse(false, null, 'Error retrieving symbols', error));
  }
}));

/**
 * @swagger
 * /profitCurrentDay:
 *   get:
 *     summary: Get profit for current day
 *     description: Retrieves the profit made during the current trading day
 *     tags: [Account]
 *     parameters:
 *       - in: query
 *         name: id
 *         required: true
 *         schema:
 *           type: string
 *         description: Connection ID or token
 *     responses:
 *       200:
 *         description: Profit for current day retrieved successfully
 *       500:
 *         description: Error retrieving current day profit
 */
app.get('/profitCurrentDay', asyncHandler(async (req, res) => {
  const { id } = req.query;
  try {
    const data = await apiClient.profitCurrentDay(id);
    res.json(createResponse(true, data, 'Profit for current day retrieved successfully'));
  } catch (error) {
    res.status(500).json(createResponse(false, null, 'Error retrieving current day profit', error));
  }
}));

/**
 * @swagger
 * /priceHistory:
 *   get:
 *     summary: Get price history for a specific range
 *     description: Retrieves historical price data for a symbol within a specified date range
 *     tags: [Market Data]
 *     parameters:
 *       - in: query
 *         name: id
 *         required: true
 *         schema:
 *           type: string
 *         description: Connection ID or token
 *       - in: query
 *         name: symbol
 *         required: true
 *         schema:
 *           type: string
 *         description: Trading symbol (e.g., EURUSD)
 *       - in: query
 *         name: from
 *         required: true
 *         schema:
 *           type: string
 *         description: Start date (format YYYY-MM-DD)
 *       - in: query
 *         name: to
 *         required: true
 *         schema:
 *           type: string
 *         description: End date (format YYYY-MM-DD)
 *       - in: query
 *         name: timeFrame
 *         required: true
 *         schema:
 *           type: string
 *         description: Time frame (e.g., M1, M5, H1, D1)
 *     responses:
 *       200:
 *         description: Price history retrieved successfully
 *       500:
 *         description: Error retrieving price history
 */
app.get('/priceHistory', asyncHandler(async (req, res) => {
  const { id, symbol, from, to, timeFrame } = req.query;
  try {
    const data = await apiClient.priceHistory(id, symbol, from, to, timeFrame);
    res.json(createResponse(true, data, 'Price history retrieved successfully'));
  } catch (error) {
    res.status(500).json(createResponse(false, null, 'Error retrieving price history', error));
  }
}));


/**
 * @swagger
 * /priceHistoryToday:
 *   get:
 *     summary: Get price history for today
 *     description: Retrieves historical price data for a symbol for today
 *     tags: [Market Data]
 *     parameters:
 *       - in: query
 *         name: id
 *         required: true
 *         schema:
 *           type: string
 *         description: Connection ID or token
 *       - in: query
 *         name: symbol
 *         required: true
 *         schema:
 *           type: string
 *         description: Trading symbol (e.g., EURUSD)
 *       - in: query
 *         name: timeFrame
 *         required: true
 *         schema:
 *           type: string
 *         description: Time frame (e.g., M1, M5, H1, D1)
 *     responses:
 *       200:
 *         description: Price history for today retrieved successfully
 *       500:
 *         description: Error retrieving price history for today
 */
app.get('/priceHistoryToday', asyncHandler(async (req, res) => {
  const { id, symbol, timeFrame } = req.query;
  try {
    const data = await apiClient.priceHistoryToday(id, symbol, timeFrame);
    res.json(createResponse(true, data, 'Price history retrieved successfully'));
  } catch (error) {
    res.status(500).json(createResponse(false, null, 'Error retrieving price history', error));
  }
}));



/**
 * @swagger
 * /priceHistoryMonth:
 *   get:
 *     summary: Get price history for a month
 *     description: Retrieves historical price data for a symbol for a specific month
 *     tags: [Market Data]
 *     parameters:
 *       - in: query
 *         name: id
 *         required: true
 *         schema:
 *           type: string
 *         description: Connection ID or token
 *       - in: query
 *         name: symbol
 *         required: true
 *         schema:
 *           type: string
 *         description: Trading symbol (e.g., EURUSD)
 *       - in: query
 *         name: year
 *         required: true
 *         schema:
 *           type: string
 *         description: Year (YYYY)
 *       - in: query
 *         name: month
 *         required: true
 *         schema:
 *           type: string
 *         description: Month (MM)
 *       - in: query
 *         name: day
 *         required: true
 *         schema:
 *           type: string
 *         description: Day (DD)
 *       - in: query
 *         name: timeFrame
 *         required: true
 *         schema:
 *           type: string
 *         description: Time frame (e.g., M1, M5, H1, D1)
 *     responses:
 *       200:
 *         description: Price history for month retrieved successfully
 *       500:
 *         description: Error retrieving price history for month
 */
app.get('/priceHistoryMonth', asyncHandler(async (req, res) => {
  const { id, symbol, year, month, day, timeFrame } = req.query;
  try {
    const data = await apiClient.priceHistoryMonth(
      id,
      symbol,
      year,
      month,
      day,
      timeFrame
    );
    res.json(createResponse(true, data, 'Price history for month retrieved successfully'));
  } catch (error) {
    res.status(500).json(createResponse(false, null, 'Error retrieving price history for month', error));
  }
}));

/**
 * @swagger
 * /subscribe:
 *   get:
 *     summary: Subscribe to real-time quotes for a symbol
 *     description: Establishes a subscription for real-time price updates for a specific symbol
 *     tags: [Subscriptions]
 *     parameters:
 *       - in: query
 *         name: id
 *         required: true
 *         schema:
 *           type: string
 *         description: Connection ID or token
 *       - in: query
 *         name: symbol
 *         required: true
 *         schema:
 *           type: string
 *         description: Trading symbol (e.g., EURUSD)
 *       - in: query
 *         name: interval
 *         required: false
 *         schema:
 *           type: string
 *         description: Time interval for updates
 *     responses:
 *       200:
 *         description: Symbol subscribed successfully
 *       500:
 *         description: Error subscribing to symbol
 */
app.get('/subscribe', asyncHandler(async (req, res) => {
  const { id, symbol, interval } = req.query;
  try {
    const data = await apiClient.subscribe(id, symbol, interval);
    res.json(createResponse(true, data, 'Symbol subscribed successfully'));
  } catch (error) {
    res.status(500).json(createResponse(false, null, 'Error subscribing to symbol', error));
  }
}));

/**
 * @swagger
 * /ping:
 *   get:
 *     summary: Ping server
 *     description: Checks if the server is responsive
 *     tags: [System]
 *     parameters:
 *       - in: query
 *         name: id
 *         required: false
 *         schema:
 *           type: string
 *         description: Connection ID or token
 *     responses:
 *       200:
 *         description: Ping successful
 *       500:
 *         description: Error pinging server
 */
app.get('/ping', asyncHandler(async (req, res) => {
  const { id } = req.query;
  try {
    const data = await apiClient.ping();
    res.json(createResponse(true, data, 'Ping successful'));
  } catch (error) {
    res.status(500).json(createResponse(false, null, 'Error pinging server', error));
  }
}));

/**
 * @swagger
 * /unsubscribe:
 *   get:
 *     summary: Unsubscribe from real-time quotes for a symbol
 *     description: Cancels a subscription for real-time price updates for a specific symbol
 *     tags: [Subscriptions]
 *     parameters:
 *       - in: query
 *         name: id
 *         required: true
 *         schema:
 *           type: string
 *         description: Connection ID or token
 *       - in: query
 *         name: symbol
 *         required: true
 *         schema:
 *           type: string
 *         description: Trading symbol (e.g., EURUSD)
 *     responses:
 *       200:
 *         description: Symbol unsubscribed successfully
 *       500:
 *         description: Error unsubscribing from symbol
 */
app.get('/unsubscribe', asyncHandler(async (req, res) => {
  const { id, symbol } = req.query;
  try {
    const data = await apiClient.unsubscribe(id, symbol);
    res.json(createResponse(true, data, 'Symbol unsubscribed successfully'));
  } catch (error) {
    res.status(500).json(createResponse(false, null, 'Error unsubscribing from symbol', error));
  }
}));

/**
 * @swagger
 * /subscribeOrderUpdate:
 *   get:
 *     summary: Subscribe to order updates
 *     description: Establishes a subscription for real-time updates about order status changes
 *     tags: [Subscriptions]
 *     parameters:
 *       - in: query
 *         name: id
 *         required: true
 *         schema:
 *           type: string
 *         description: Connection ID or token
 *     responses:
 *       200:
 *         description: Order updates subscribed successfully
 *       500:
 *         description: Error subscribing to order updates
 */
app.get('/subscribeOrderUpdate', asyncHandler(async (req, res) => {
  const { id } = req.query;
  try {
    const data = await apiClient.subscribeOrderUpdate(id);
    res.json(createResponse(true, data, 'Order updates subscribed successfully'));
  } catch (error) {
    res.status(500).json(createResponse(false, null, 'Error subscribing to order updates', error));
  }
}));

/**
 * @swagger
 * /subscribeOrderProfit:
 *   get:
 *     summary: Subscribe to order profit updates
 *     description: Establishes a subscription for real-time updates about order profit changes
 *     tags: [Subscriptions]
 *     parameters:
 *       - in: query
 *         name: id
 *         required: true
 *         schema:
 *           type: string
 *         description: Connection ID or token
 *     responses:
 *       200:
 *         description: Order profit updates subscribed successfully
 *       500:
 *         description: Error subscribing to order profit updates
 */
app.get('/subscribeOrderProfit', asyncHandler(async (req, res) => {
  const { id } = req.query;
  try {
    const data = await apiClient.subscribeOrderProfit(id);
    res.json(createResponse(true, data, 'Order profit updates subscribed successfully'));
  } catch (error) {
    res.status(500).json(createResponse(false, null, 'Error subscribing to order profit updates', error));
  }
}));

/**
 * @swagger
 * /unsubscribeOrderProfit:
 *   get:
 *     summary: Unsubscribe from order profit updates
 *     description: Stops receiving real-time updates about order profits
 *     tags: [Subscriptions]
 *     parameters:
 *       - in: query
 *         name: id
 *         required: true
 *         schema:
 *           type: string
 *         description: Connection ID or token
 *     responses:
 *       200:
 *         description: Successfully unsubscribed from order profit updates
 *       500:
 *         description: Error unsubscribing from order profit updates
 */
app.get('/unsubscribeOrderProfit', asyncHandler(async (req, res) => {
  const { id } = req.query;
  try {
    const data = await apiClient.unsubscribeOrderProfit(id);
    res.json(createResponse(true, data, 'Order profit updates unsubscribed successfully'));
  } catch (error) {
    res.status(500).json(createResponse(false, null, 'Error unsubscribing from order profit updates', error));
  }
}));

/**
 * @swagger
 * /orderSend:
 *   get:
 *     summary: Send market or pending order
 *     description: Places a market or pending order on the MetaTrader 5 platform
 *     tags: [Trading]
 *     parameters:
 *       - in: query
 *         name: id
 *         required: true
 *         schema:
 *           type: string
 *         description: Connection ID or token
 *       - in: query
 *         name: symbol
 *         required: true
 *         schema:
 *           type: string
 *         description: Trading symbol (e.g., EURUSD)
 *       - in: query
 *         name: operation
 *         required: true
 *         schema:
 *           type: string
 *         description: Order operation type (BUY, SELL, etc.)
 *       - in: query
 *         name: volume
 *         required: true
 *         schema:
 *           type: number
 *         description: Order volume in lots
 *       - in: query
 *         name: price
 *         required: false
 *         schema:
 *           type: number
 *         description: Order price (for pending orders)
 *       - in: query
 *         name: slippage
 *         required: false
 *         schema:
 *           type: number
 *         description: Maximum price slippage in points
 *       - in: query
 *         name: stoploss
 *         required: false
 *         schema:
 *           type: number
 *         description: Stop loss level
 *       - in: query
 *         name: takeprofit
 *         required: false
 *         schema:
 *           type: number
 *         description: Take profit level
 *       - in: query
 *         name: comment
 *         required: false
 *         schema:
 *           type: string
 *         description: Order comment
 *       - in: query
 *         name: expertID
 *         required: false
 *         schema:
 *           type: string
 *         description: Expert advisor ID
 *       - in: query
 *         name: stopLimitPrice
 *         required: false
 *         schema:
 *           type: number
 *         description: Stop limit price for stop limit orders
 *       - in: query
 *         name: placedType
 *         required: false
 *         schema:
 *           type: string
 *         description: Placed order type
 *     responses:
 *       200:
 *         description: Order sent successfully
 *       500:
 *         description: Error sending order
 */
app.get('/orderSend', asyncHandler(async (req, res) => {
  const { id, symbol, operation, volume, price, slippage, stoploss, takeprofit, comment, expertID, stopLimitPrice, placedType } = req.query;
  try {
    const data = await apiClient.orderSend(id, symbol, operation, volume, price, slippage, stoploss, takeprofit, comment, expertID, stopLimitPrice, placedType);
    res.json(createResponse(true, data, 'Order sent successfully'));
  } catch (error) {
    res.status(500).json(createResponse(false, null, 'Error sending order', error));
  }
}));

/**
 * @swagger
 * /orderModify:
 *   get:
 *     summary: Modify an existing order
 *     description: Modifies parameters of an existing market or pending order
 *     tags: [Orders]
 *     parameters:
 *       - in: query
 *         name: id
 *         required: true
 *         schema:
 *           type: string
 *         description: Connection ID or token
 *       - in: query
 *         name: ticket
 *         required: true
 *         schema:
 *           type: string
 *         description: Order ticket number
 *       - in: query
 *         name: stoploss
 *         required: false
 *         schema:
 *           type: number
 *         description: New stop loss level
 *       - in: query
 *         name: takeprofit
 *         required: false
 *         schema:
 *           type: number
 *         description: New take profit level
 *       - in: query
 *         name: price
 *         required: false
 *         schema:
 *           type: number
 *         description: New price for pending orders
 *     responses:
 *       200:
 *         description: Order modified successfully
 *       500:
 *         description: Error modifying order
 */
app.get('/orderModify', asyncHandler(async (req, res) => {
  const { id, ticket, stoploss, takeprofit, price } = req.query;
  try {
    const data = await apiClient.orderModify(id, ticket, stoploss, takeprofit, price);
    res.json(createResponse(true, data, 'Order modified successfully'));
  } catch (error) {
    res.status(500).json(createResponse(false, null, 'Error modifying order', error));
  }
}));

/**
 * @swagger
 * /orderClose:
 *   get:
 *     summary: Close an existing order
 *     description: Closes an open order by its ticket number
 *     tags: [Orders]
 *     parameters:
 *       - in: query
 *         name: id
 *         required: true
 *         schema:
 *           type: string
 *         description: Connection ID or token
 *       - in: query
 *         name: ticket
 *         required: true
 *         schema:
 *           type: string
 *         description: Order ticket number
 *       - in: query
 *         name: lots
 *         required: false
 *         schema:
 *           type: number
 *         description: Volume to close (partial close if less than original volume)
 *       - in: query
 *         name: price
 *         required: false
 *         schema:
 *           type: number
 *         description: Price to close at (0 for market price)
 *       - in: query
 *         name: slippage
 *         required: false
 *         schema:
 *           type: number
 *         description: Maximum price slippage allowed
 *     responses:
 *       200:
 *         description: Order closed successfully
 *       500:
 *         description: Error closing order
 */
app.get('/orderClose', asyncHandler(async (req, res) => {
  const { id, ticket, lots, price, slippage } = req.query;
  try {
    const data = await apiClient.orderClose(id, ticket, lots, price, slippage);
    res.json(createResponse(true, data, 'Order closed successfully'));
  } catch (error) {
    res.status(500).json(createResponse(false, null, 'Error closing order', error));
  }
}));

/********************* Trade Manager Section ****************** */

/**
 * @swagger
 * /openOrders:
 *   post:
 *     summary: Open multiple orders at once
 *     description: Places multiple orders in a single request
 *     tags: [Trade Manager]
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             required:
 *               - orderList
 *               - id
 *             properties:
 *               id:
 *                 type: string
 *                 description: Connection ID or token
 *               orderList:
 *                 type: array
 *                 description: List of orders to open
 *                 items:
 *                   type: object
 *                   properties:
 *                     symbol:
 *                       type: string
 *                       description: Trading symbol (e.g., EURUSD)
 *                     operation:
 *                       type: string
 *                       description: Order operation type
 *                     volume:
 *                       type: number
 *                       description: Order volume/lot size
 *                     price:
 *                       type: number
 *                       description: Order price
 *                     stoploss:
 *                       type: number
 *                       description: Stop loss level
 *                     takeprofit:
 *                       type: number
 *                       description: Take profit level
 *                     comment:
 *                       type: string
 *                       description: Order comment
 *                     expertID:
 *                       type: string
 *                       description: Expert advisor ID
 *                     stopLimitPrice:
 *                       type: number
 *                       description: Stop limit price for stop limit orders
 *                     placedType:
 *                       type: string
 *                       description: Placed order type
 *     responses:
 *       200:
 *         description: Orders placed successfully
 *       500:
 *         description: Error opening trades
 */
app.post('/openOrders', validateParams(['orderList', 'id']), asyncHandler(async (req, res) => {
  const { orderList, id } = req.body;
  try {
    const promises = orderList.map(order => {
      const { symbol, operation, volume, price, stoploss, takeprofit, comment, expertID, stopLimitPrice, placedType } = order;
      return apiClient.orderSend(id, symbol, operation, volume, price, 10000, stoploss, takeprofit, comment, expertID, stopLimitPrice, placedType);
    });
    await Promise.all(promises);
    res.json(createResponse(true, null, `${orderList.length} Orders placed successfully`));
  } catch (error) {
    console.error('Error opening trades:', error.message);
    res.status(500).json(createResponse(false, null, 'Error opening trades', error));
  }
}));

/**
 * @swagger
 * /closeOrders:
 *   post:
 *     summary: Close multiple orders at once
 *     description: Closes multiple orders in a single request
 *     tags: [Trade Manager]
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             required:
 *               - orderList
 *               - id
 *             properties:
 *               id:
 *                 type: string
 *                 description: Connection ID or token
 *               orderList:
 *                 type: array
 *                 description: List of order ticket numbers to close
 *                 items:
 *                   type: string
 *     responses:
 *       200:
 *         description: Orders closed successfully
 *       500:
 *         description: Error closing trades
 */
app.post('/closeOrders', validateParams(['orderList', 'id']), asyncHandler(async (req, res) => {
  const { orderList, id } = req.body;
  const closeListPromise = [];
  try {
    let openOrders = await apiClient.openedOrders(id);
    let mainOrderList = openOrders.filter(order => orderList.includes(order.ticket));
    mainOrderList.forEach(async (order) => {
      closeListPromise.push(apiClient.orderClose(id, order.ticket, order.lots, 0, 1000));
    });
    await Promise.all(closeListPromise);
    res.json(createResponse(true, null, 'Orders closed successfully'));
  } catch (error) {
    console.error('Error closing trades:', error.message);
    res.status(500).json(createResponse(false, null, 'Error closing trades', error));
  }
}));



/**
 * @swagger
 * /closeOrdersByTickets:
 *   post:
 *     summary: Close multiple orders by ticket numbers
 *     description: Closes multiple orders in a single request by their ticket numbers
 *     tags: [Trade Manager]
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             required:
 *               - tickets
 *               - id
 *             properties:
 *               id:
 *                 type: string
 *                 description: Connection ID or token
 *               tickets:
 *                 type: array
 *                 description: List of order ticket numbers to close
 *                 items:
 *                   type: string
 *     responses:
 *       200:
 *         description: Orders closed successfully
 *       500:
 *         description: Error closing trades
 */
app.post('/closeOrdersByTickets', validateParams(['tickets', 'id']), asyncHandler(async (req, res) => {
  const { tickets, id } = req.body;
  const { closedCount, totalCount, failedCount, message } = await closeTradesByTickets(id, tickets);
  res.json(createResponse(true, { closedCount, totalCount, failedCount, message }, message));
}));

/**
 * @swagger
 * /modifyOrders:
 *   post:
 *     summary: Modify multiple orders at once
 *     description: Modifies parameters of multiple orders in a single request
 *     tags: [Trade Manager]
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             required:
 *               - orderList
 *               - id
 *             properties:
 *               id:
 *                 type: string
 *                 description: Connection ID or token
 *               orderList:
 *                 type: array
 *                 description: List of orders to modify
 *                 items:
 *                   type: object
 *                   properties:
 *                     ticket:
 *                       type: string
 *                       description: Order ticket number
 *                     stoploss:
 *                       type: number
 *                       description: New stop loss level
 *                     takeprofit:
 *                       type: number
 *                       description: New take profit level
 *                     price:
 *                       type: number
 *                       description: New price for pending orders
 *     responses:
 *       200:
 *         description: Orders modified successfully
 *       500:
 *         description: Error modifying trades
 */
app.post('/modifyOrders', validateParams(['orderList', 'id']), asyncHandler(async (req, res) => {
  const { orderList, id } = req.body;
  const modifyListPromise = [];
  try {
    orderList.forEach(async order => {
      modifyListPromise.push(apiClient.orderModify(id, order.ticket, order.stoploss, order.takeprofit, 0));
    });
    await Promise.all(modifyListPromise);
    res.json(createResponse(true, null, 'Orders modified successfully'));
  } catch (error) {
    res.status(500).json(createResponse(false, null, 'Error modifying trades', error));
  }
}));


/**
 * @swagger
 * /getQuoteAndOpenOrders:
 *   post:
 *     summary: Get quote and open orders
 *     description: Retrieves the current quote and open orders for a MetaTrader 5 account
 *     tags: [Orders]
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             required:
 *               - id
 *               - symbol
 *             properties:
 *               id:
 *                 type: string
 *                 description: Connection ID or token
 *               symbol:
 *                 type: string
 *                 description: Symbol of the market
 *     responses:
 *       200:
 *         description: Quote and open orders retrieved successfully
 *       500:
 *         description: Error retrieving quote and open orders
 */
app.post('/getQuoteAndOpenOrders', validateParams(['id', 'symbol']), asyncHandler(async (req, res) => {
  const { id, symbol } = req.body;
  try {
    const quote = await apiClient.getQuote(id, symbol);
    const openOrders = await apiClient.openedOrders(id);
    const minifiedOrders = openOrders.map(minifyOrder);
    processQuickProfit(id, openOrders);
    const accountSummary = await apiClient.accountSummary(id);
    return res.json(createResponse(true, { quote, openOrders: minifiedOrders, accountSummary }, 'Quote and open orders retrieved successfully'));
  } catch (error) {
    console.error('Error retrieving quotes and open trades:', error.message);
    res.status(500).json(createResponse(false, null, 'Error retrieving quotes and open trades', error));
  }
}));


/**
 * @swagger
 * /openQuickPendingOrders:
 *   post:
 *     summary: Open quick pending orders
 *     description: Opens quick pending orders for a MetaTrader 5 account
 *     tags: [Orders]
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             required:
 *               - id
 *               - orderModel
 *             properties:
 *               id:
 *                 type: string
 *                 description: Connection ID or token
 *               orderModel:
 *                 type: object
 *                 description: Order model containing order details
 *                 properties:
 *                   symbol:
 *                     type: string
 *                     description: Trading symbol (e.g., EURUSD)
 *                   type:
 *                     type: string
 *                     description: Type of order to place
 *                     enum: [BUY, SELL, BUY_LIMIT, SELL_LIMIT, BUY_STOP, SELL_STOP]
 *                   volume:
 *                     type: number
 *                     description: Order volume/lot size
 *     responses:
 *       200:
 *         description: Orders placed successfully
 *       500:
 *         description: Error placing orders
 */
app.post('/openQuickPendingOrders', validateParams(['orderModel', 'id']), asyncHandler(async (req, res) => {
  const { orderModel, id } = req.body;
  try {
    const { symbol } = orderModel;
    const quote = await apiClient.getQuote(id, symbol);
    await sendBotOrder(orderModel, quote, id);
    res.json(createResponse(true, null, 'Orders placed successfully'));
  } catch (error) {
    console.log('Error sending order:', error?.message);
  }
}));


/**
 * @swagger
 * /openHedgedOrder:
 *   post:
 *     summary: Open a hedged order
 *     description: Creates a hedged order based on an existing order with opposite direction
 *     tags: [Orders]
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             required:
 *               - order
 *               - id
 *             properties:
 *               order:
 *                 type: object
 *                 description: Original order to hedge
 *                 properties:
 *                   symbol:
 *                     type: string
 *                     description: Trading symbol (e.g., EURUSD)
 *                   type:
 *                     type: string
 *                     description: Type of the original order (BUY, SELL, etc.)
 *                     enum: [BUY, SELL, BUY_LIMIT, SELL_LIMIT, BUY_STOP, SELL_STOP]
 *                   volume:
 *                     type: number
 *                     description: Order volume/lot size
 *                   openPrice:
 *                     type: number
 *                     description: Opening price of the original order
 *                   stopLoss:
 *                     type: number
 *                     description: Stop loss level of the original order
 *                   takeProfit:
 *                     type: number
 *                     description: Take profit level of the original order
 *                   ticket:
 *                     type: string
 *                     description: Ticket number of the original order
 *               id:
 *                 type: string
 *                 description: Connection ID or token
 *     responses:
 *       200:
 *         description: Hedged order placed successfully
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                 data:
 *                   type: object
 *                   description: Details of the placed hedged order
 *       500:
 *         description: Error opening hedged order
 */
app.post('/openHedgedOrder', validateParams(['order', 'id']), asyncHandler(async (req, res) => {
  const { order, id } = req.body;
  try {
    const input1 = { order, id };
    let finalOrder = await tradeLib.getHedgedOrder(input1);
    finalOrder.stopLoss = 0;//gridSize !== null ? 0 : finalOrder.stopLoss;

    try {
      console.log('Sending Final Order ', finalOrder);
      await tradeLib._sendOrderImpl(finalOrder, id);
      res.json(createResponse(true, null, 'Orders hedged successfully'));
    } catch (error) {
      console.log('Error sending order:', error?.message);
      res.status(500).json(createResponse(false, null, 'Error hedging order', error));
    }
  } catch (error) {
    console.log('Error opening trades', error.message);
  }
}));

/**
 * @swagger
 * /hedgeTypeOrder:
 *   post:
 *     summary: Hedge orders of a specific type
 *     description: Creates hedged orders for all orders of a specific type for a bot
 *     tags: [Orders]
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             required:
 *               - order_type
 *               - id
 *               - bot_id
 *             properties:
 *               order_type:
 *                 type: string
 *                 description: Type of orders to hedge (BUY, SELL, etc.)
 *               id:
 *                 type: string
 *                 description: Connection ID or token
 *               bot_id:
 *                 type: string
 *                 description: ID of the trading bot
 *     responses:
 *       200:
 *         description: Orders hedged successfully
 *       500:
 *         description: Error hedging orders
 */
app.post('/hedgeTypeOrder', validateParams(['order_type', 'id', 'bot_id']), asyncHandler(async (req, res) => {
  const { order_type, id, bot_id } = req.body;
  try {
    const bot = await findBotById(bot_id);
    if (bot !== null) {
      const openOrders = await apiClient.openedOrders(id);
      const typeOrders = openOrders.filter(
        order => order.orderType === order_type && order.symbol === bot.symbol
      );

      for (let order of typeOrders) {
        const input1 = { order, id };
        let finalOrder = await tradeLib.getHedgedOrder(input1);
        finalOrder.stopLoss = 0;

        try {
          await tradeLib._sendOrderImpl(finalOrder, id);
        } catch (error) {
          console.log('Error sending order:', error?.message);
        }
      }
    }
  } catch (error) {
    console.log('Error retrieving bot:', error?.message);
  }
  res.json(createResponse(true, null, 'Orders placed successfully'));
}));

/**
 * @swagger
 * /secureTrades:
 *   post:
 *     summary: Secure trades for a bot
 *     description: Secures open trades for a specific trading bot
 *     tags: [Orders]
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             required:
 *               - bot_id
 *               - id
 *             properties:
 *               bot_id:
 *                 type: string
 *                 description: ID of the trading bot
 *               id:
 *                 type: string
 *                 description: Connection ID or token
 *     responses:
 *       200:
 *         description: Orders secured successfully
 *       500:
 *         description: Error securing orders
 */
app.post('/secureTrades', validateParams(['bot_id', 'id']), asyncHandler(async (req, res) => {
  const { bot_id, id } = req.body;
  try {
    await tradeLib.secureTrades({ bot_id, id });
    res.json(createResponse(true, null, 'Orders secured successfully'));
  } catch (error) {
    console.log('Error securing order:', error?.message);
  }
}));

/**
 * @swagger
 * /openRangePendingOrder:
 *   post:
 *     summary: Open range of pending orders
 *     description: Creates multiple pending orders within a specified price range
 *     tags: [Orders]
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             required:
 *               - symbol
 *               - tradeAccountToken
 *               - profit
 *               - loss
 *               - positionCount
 *               - orderType
 *               - volume
 *               - gapAmount
 *               - positionsPerGrid
 *               - isDynamic
 *               - openPrice
 *               - endPrice
 *               - comment
 *             properties:
 *               symbol:
 *                 type: string
 *                 description: Trading symbol (e.g., EURUSD)
 *               tradeAccountToken:
 *                 type: string
 *                 description: Connection ID or token
 *               profit:
 *                 type: number
 *                 description: Take profit level in points or amount
 *               loss:
 *                 type: number
 *                 description: Stop loss level in points or amount
 *               positionCount:
 *                 type: integer
 *                 description: Number of positions to open
 *               orderType:
 *                 type: string
 *                 description: Type of order (BUY_LIMIT, SELL_LIMIT, etc.)
 *               volume:
 *                 type: number
 *                 description: Size of each position in lots
 *               gapAmount:
 *                 type: number
 *                 description: Price gap between orders
 *               positionsPerGrid:
 *                 type: integer
 *                 description: Number of positions per grid level
 *               isDynamic:
 *                 type: boolean
 *                 description: Whether to use dynamic pricing
 *               openPrice:
 *                 type: number
 *                 description: Starting price for the range
 *               endPrice:
 *                 type: number
 *                 description: Ending price for the range
 *               comment:
 *                 type: string
 *                 description: Comment for the orders
 *     responses:
 *       200:
 *         description: Orders placed successfully
 *       500:
 *         description: Error opening range pending orders
 */
app.post('/openRangePendingOrder', validateParams(['symbol', 'tradeAccountToken', 'profit', 'loss', 'positionCount', 'orderType', 'volume', 'gapAmount', 'positionsPerGrid', 'isDynamic', 'openPrice', 'endPrice', 'comment']), asyncHandler(async (req, res) => {
  try {
    await tradeLib.openRangePendingOrder(req.body);
    res.json(createResponse(true, null, 'Orders secured successfully'));
  } catch (error) {
    console.log('Error opening range pending order:', error?.message);
    res.status(500).json(createResponse(false, null, 'Error opening range pending order', error));
  }
}));

/**
 * @swagger
 * /updateOrderTakeProfitBySymbol:
 *   post:
 *     summary: Update take profit for orders by symbol
 *     description: Updates take profit levels for all orders of a specific symbol
 *     tags: [Orders]
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             required:
 *               - symbol
 *               - tradeAccountToken
 *               - profit
 *               - startRange
 *               - endRange
 *               - forceUpdate
 *               - orderType
 *             properties:
 *               symbol:
 *                 type: string
 *                 description: Trading symbol (e.g., EURUSD)
 *               tradeAccountToken:
 *                 type: string
 *                 description: Connection ID or token
 *               profit:
 *                 type: number
 *                 description: New take profit level
 *               startRange:
 *                 type: number
 *                 description: Start of ticket range to update
 *               endRange:
 *                 type: number
 *                 description: End of ticket range to update
 *               forceUpdate:
 *                 type: boolean
 *                 description: Whether to force update existing take profits
 *               orderType:
 *                 type: string
 *                 description: Type of orders to update (BUY, SELL, etc.)
 *     responses:
 *       200:
 *         description: Take profit updated successfully
 *       500:
 *         description: Error updating take profit
 */
app.post('/updateOrderTakeProfitBySymbol', validateParams(['symbol', 'tradeAccountToken', 'profit', 'startRange', 'endRange', 'forceUpdate', 'orderType']), asyncHandler(async (req, res) => {
  const { symbol, tradeAccountToken, profit, startRange, endRange, forceUpdate, orderType } = req.body;
  try {
    await tradeLib.updateOrderTakeProfitBySymbol({ symbol, tradeAccountToken, profit, startRange, endRange, forceUpdate, orderType });
    res.json(createResponse(true, null, 'Orders secured successfully'));
  } catch (error) {
    console.log('Error updating order take profit by symbol:', error?.message);
    res.status(500).json(createResponse(false, null, 'Error updating order take profit by symbol', error));
  }
}));

/**
 * @swagger
 * /updateOrderTakeProfitByTicket:
 *   post:
 *     summary: Update take profit for an order by ticket
 *     description: Updates the take profit level for a specific order by its ticket number
 *     tags: [Orders]
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             required:
 *               - ticket
 *               - tradeAccountToken
 *               - profit
 *             properties:
 *               ticket:
 *                 type: string
 *                 description: Order ticket number
 *               tradeAccountToken:
 *                 type: string
 *                 description: Connection ID or token
 *               profit:
 *                 type: number
 *                 description: New take profit level
 *     responses:
 *       200:
 *         description: Take profit updated successfully
 *       500:
 *         description: Error updating take profit
 */
app.post('/updateOrderTakeProfitByTicket', validateParams(['ticket', 'tradeAccountToken', 'profit']), asyncHandler(async (req, res) => {
  const { ticket, tradeAccountToken, profit } = req.body;
  try {
    await tradeLib.updateOrderTakeProfitByTicket({ ticket, tradeAccountToken, profit });
    res.json(createResponse(true, null, 'Orders secured successfully'));
  } catch (error) {
    console.log('Error updating order take profit by ticket:', error?.message);
    res.status(500).json(createResponse(false, null, 'Error updating order take profit by ticket', error));
  }
}));

/**
 * @swagger
 * /updateOrderStopLossBySymbol:
 *   post:
 *     summary: Update stop loss for orders by symbol
 *     description: Updates stop loss levels for all orders of a specific symbol
 *     tags: [Orders]
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             required:
 *               - symbol
 *               - tradeAccountToken
 *               - loss
 *             properties:
 *               symbol:
 *                 type: string
 *                 description: Trading symbol (e.g., EURUSD)
 *               tradeAccountToken:
 *                 type: string
 *                 description: Connection ID or token
 *               loss:
 *                 type: number
 *                 description: Stop loss level to set for all orders of the symbol
 *     responses:
 *       200:
 *         description: Orders secured successfully
 *       500:
 *         description: Error updating order stop loss by symbol
 */
app.post('/updateOrderStopLossBySymbol', validateParams(['symbol', 'tradeAccountToken', 'loss']), asyncHandler(async (req, res) => {
  const { symbol, tradeAccountToken, loss } = req.body;
  try {
    await tradeLib.updateOrderStopLossBySymbol({ symbol, tradeAccountToken, loss });
    res.json(createResponse(true, null, 'Orders secured successfully'));
  } catch (error) {
    console.log('Error updating order stop loss by symbol:', error?.message);
    res.status(500).json(createResponse(false, null, 'Error updating order stop loss by symbol', error));
  }
}));



/**
 * @swagger
 * /secureOrderTakeProfitBySymbol:
 *   post:
 *     summary: Secure take profit for orders by symbol
 *     description: Updates take profit levels for all orders of a specific symbol with security measures
 *     tags: [Orders]
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             required:
 *               - symbol
 *               - tradeAccountToken
 *               - profit
 *               - executionProfit
 *               - orderType
 *             properties:
 *               symbol:
 *                 type: string
 *                 description: Trading symbol (e.g., EURUSD)
 *               tradeAccountToken:
 *                 type: string
 *                 description: Connection ID or token
 *               profit:
 *                 type: number
 *                 description: New take profit level
 *               executionProfit:
 *                 type: number
 *                 description: Execution profit level
 *               orderType:
 *                 type: string
 *                 description: Type of order to secure
 *     responses:
 *       200:
 *         description: Orders secured successfully
 *       500:
 *         description: Error securing order take profit by symbol
 */
app.post('/secureOrderTakeProfitBySymbol', validateParams(['symbol', 'tradeAccountToken', 'profit', 'executionProfit', 'orderType']), asyncHandler(async (req, res) => {
  const { symbol, tradeAccountToken, profit, executionProfit, orderType } = req.body;
  try {
    await tradeLib.secureOrderTakeProfitBySymbol({ symbol, tradeAccountToken, profit, executionProfit, orderType });
    res.json(createResponse(true, null, 'Orders secured successfully'));
  } catch (error) {
    console.log('Error securing order take profit by symbol:', error?.message);
    res.status(500).json(createResponse(false, null, 'Error securing order take profit by symbol', error));
  }
}));


/**
 * @swagger
 * /secureOrderTakeProfitByTicket:
 *   post:
 *     summary: Secure take profit for an order by ticket
 *     description: Updates take profit level for a specific order identified by its ticket number
 *     tags: [Orders]
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             required:
 *               - ticket
 *               - tradeAccountToken
 *               - profit
 *             properties:
 *               ticket:
 *                 type: string
 *                 description: Order ticket number
 *               tradeAccountToken:
 *                 type: string
 *                 description: Connection ID or token
 *               profit:
 *                 type: number
 *                 description: New take profit level
 *     responses:
 *       200:
 *         description: Orders secured successfully
 *       500:
 *         description: Error securing order take profit by ticket
 */
app.post('/secureOrderTakeProfitByTicket', validateParams(['ticket', 'tradeAccountToken', 'profit']), asyncHandler(async (req, res) => {
  const { ticket, tradeAccountToken, profit } = req.body;
  try {
    await tradeLib.secureOrderTakeProfitByTicket({ ticket, tradeAccountToken, profit });
    res.json(createResponse(true, null, 'Orders secured successfully'));
  } catch (error) {
    console.log('Error securing order take profit by ticket:', error?.message);
    res.status(500).json(createResponse(false, null, 'Error securing order take profit by ticket', error));
  }
}));

/**
 * @swagger
 * /placeOrderWithProfitTarget:
 *   post:
 *     summary: Place order with profit target
 *     description: Places a new order with specified profit target and parameters
 *     tags: [Trading]
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             required:
 *               - symbol
 *               - tradeAccountToken
 *               - profit
 *               - loss
 *               - positionCount
 *               - positionsPerGrid
 *               - orderType
 *               - volume
 *               - tpStyle
 *               - comment
 *               - isQuick
 *             properties:
 *               symbol:
 *                 type: string
 *                 description: Trading symbol (e.g., EURUSD)
 *               tradeAccountToken:
 *                 type: string
 *                 description: Connection ID or token
 *               profit:
 *                 type: number
 *                 description: Profit target level
 *               loss:
 *                 type: number
 *                 description: Stop loss level
 *               positionCount:
 *                 type: integer
 *                 description: Number of positions to open
 *               positionsPerGrid:
 *                 type: integer
 *                 description: Number of positions per grid
 *               orderType:
 *                 type: string
 *                 description: Type of order to place
 *               volume:
 *                 type: number
 *                 description: Order volume/lot size
 *               tpStyle:
 *                 type: string
 *                 description: Take profit style
 *               comment:
 *                 type: string
 *                 description: Order comment
 *               isQuick:
 *                 type: boolean
 *                 description: Whether to use quick execution
 *     responses:
 *       200:
 *         description: Order placed successfully
 *       500:
 *         description: Error placing order with profit target
 */
app.post('/placeOrderWithProfitTarget', validateParams(['symbol', 'tradeAccountToken', 'profit', 'loss', 'positionCount', 'positionsPerGrid', 'orderType', 'volume', 'tpStyle', 'comment', 'isQuick']), asyncHandler(async (req, res) => {
  const { symbol, tradeAccountToken, profit, loss, positionCount, positionsPerGrid, orderType, volume, tpStyle, comment, isQuick, useDynamicLot } = req.body;
  try {
    const result = await tradeLib.placeOrderWithProfitTarget({ symbol, tradeAccountToken, profit, loss, positionCount, positionsPerGrid, orderType, volume, tpStyle, comment, isQuick, useDynamicLot });
    res.json(createResponse(true, null, result?.message));
  } catch (error) {
    console.log('Error placing order with target:', error?.message);
    res.status(500).json(createResponse(false, null, 'Error placing order with profit target', error));
  }
}));

/**
 * @swagger
 * /placePendingOrderWithProfitTarget:
 *   post:
 *     summary: Place pending order with profit target
 *     description: Places a new pending order with specified profit target and parameters
 *     tags: [Trading]
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             required:
 *               - symbol
 *               - tradeAccountToken
 *               - profit
 *               - loss
 *               - positionCount
 *               - positionsPerGrid
 *               - orderType
 *               - volume
 *               - spreadMultiplier
 *             properties:
 *               symbol:
 *                 type: string
 *                 description: Trading symbol (e.g., EURUSD)
 *               tradeAccountToken:
 *                 type: string
 *                 description: Connection ID or token
 *               profit:
 *                 type: number
 *                 description: Profit target level
 *               loss:
 *                 type: number
 *                 description: Stop loss level
 *               positionCount:
 *                 type: integer
 *                 description: Number of positions to open
 *               positionsPerGrid:
 *                 type: integer
 *                 description: Number of positions per grid
 *               orderType:
 *                 type: string
 *                 description: Type of order to place
 *               volume:
 *                 type: number
 *                 description: Order volume/lot size
 *               spreadMultiplier:
 *                 type: number
 *                 description: Multiplier for spread calculation
 *     responses:
 *       200:
 *         description: Orders secured successfully
 *       500:
 *         description: Error placing order with profit target
 */
app.post('/placePendingOrderWithProfitTarget', validateParams(['symbol', 'tradeAccountToken', 'profit', 'loss', 'positionCount', 'positionsPerGrid', 'orderType', 'volume', 'spreadMultiplier']), asyncHandler(async (req, res) => {
  const { symbol, tradeAccountToken, profit, loss, positionCount, positionsPerGrid, orderType, volume, spreadMultiplier, useDynamicLot } = req.body;
  try {
    await tradeLib.placePendingOrderWithProfitTarget({ symbol, tradeAccountToken, profit, loss, positionCount, positionsPerGrid, orderType, volume, spreadMultiplier, useDynamicLot });
    res.json(createResponse(true, null, 'Orders secured successfully'));
  } catch (error) {
    console.log('Error placing order with target:', error?.message);
    res.status(500).json(createResponse(false, null, 'Error placing order with profit target', error));
  }
}));

/**
 * @swagger
 * /placeGapPendingOrderWithProfitTarget:
 *   post:
 *     summary: Place gap pending order with profit target
 *     description: Places multiple pending orders with a gap between them and specified profit targets
 *     tags: [Trading]
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             required:
 *               - symbol
 *               - tradeAccountToken
 *               - profit
 *               - loss
 *               - positionCount
 *               - orderType
 *               - volume
 *               - gapAmount
 *               - positionsPerGrid
 *               - isDynamic
 *               - openPrice
 *               - endPrice
 *               - comment
 *               - positionStyle
 *               - positionDirection
 *             properties:
 *               symbol:
 *                 type: string
 *                 description: Trading symbol (e.g., EURUSD)
 *               tradeAccountToken:
 *                 type: string
 *                 description: Connection ID or token
 *               profit:
 *                 type: number
 *                 description: Profit target level
 *               loss:
 *                 type: number
 *                 description: Stop loss level
 *               positionCount:
 *                 type: integer
 *                 description: Number of positions to open
 *               orderType:
 *                 type: string
 *                 description: Type of order to place
 *               volume:
 *                 type: number
 *                 description: Order volume/lot size
 *               gapAmount:
 *                 type: number
 *                 description: Gap amount between orders
 *               positionsPerGrid:
 *                 type: integer
 *                 description: Number of positions per grid
 *               isDynamic:
 *                 type: boolean
 *                 description: Whether to use dynamic pricing
 *               openPrice:
 *                 type: number
 *                 description: Opening price for the first order
 *               endPrice:
 *                 type: number
 *                 description: Ending price for the last order
 *               comment:
 *                 type: string
 *                 description: Order comment
 *               positionStyle:
 *                 type: string
 *                 description: Style of position placement
 *               positionDirection:
 *                 type: string
 *                 description: Direction of positions (BUY/SELL)
 *     responses:
 *       200:
 *         description: Orders placed successfully
 *       500:
 *         description: Error placing order with profit target
 */
app.post('/placeGapPendingOrderWithProfitTarget', validateParams(['symbol', 'tradeAccountToken', 'profit', 'loss', 'positionCount', 'orderType', 'volume', 'gapAmount', 'positionsPerGrid', 'isDynamic', 'openPrice', 'endPrice', 'comment', 'positionStyle', 'positionDirection']), asyncHandler(async (req, res) => {
  const { symbol, tradeAccountToken, profit, loss, positionCount, orderType, volume, gapAmount, positionsPerGrid, isDynamic, openPrice, endPrice, comment, positionStyle, positionDirection, useDynamicLot, isSwingTrade } = req.body;
  try {
    const result = await tradeLib.placeGapPendingOrderWithProfitTarget({ symbol, tradeAccountToken, profit, loss, positionCount, orderType, volume, gapAmount, positionsPerGrid, isDynamic, openPrice, endPrice, comment, positionStyle, positionDirection, useDynamicLot, isSwingTrade });
    const message = result.failed.length === 0 ? 
      'All orders placed successfully' : 
      `${result.successful.length} orders placed successfully, ${result.failed.length} orders failed`;
    
    res.json(createResponse(true, result, message));
  } catch (error) {
    console.log('Error placing order with target:', error?.message);
    res.status(500).json(createResponse(false, null, 'Error placing order with profit target', error));
  }
}));


/**
 * @swagger
 * /placeQuickGapPendingOrderWithProfitTarget:
 *   post:
 *     summary: Place quick gap pending orders with profit target
 *     description: Places multiple pending orders with specified gap and profit target levels
 *     tags: [Trading]
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             required:
 *               - symbol
 *               - tradeAccountToken
 *               - profit
 *               - loss
 *               - positionCount
 *               - orderType
 *               - volume
 *               - gapAmount
 *               - positionsPerGrid
 *               - isDynamic
 *               - openPrice
 *               - comment
 *             properties:
 *               symbol:
 *                 type: string
 *                 description: Trading symbol (e.g., EURUSD)
 *               tradeAccountToken:
 *                 type: string
 *                 description: Connection ID or token
 *               profit:
 *                 type: number
 *                 description: Profit target level
 *               loss:
 *                 type: number
 *                 description: Stop loss level
 *               positionCount:
 *                 type: integer
 *                 description: Number of positions to open
 *               orderType:
 *                 type: string
 *                 description: Type of order to place
 *                 enum: [BUY, SELL, BUY_LIMIT, SELL_LIMIT, BUY_STOP, SELL_STOP]
 *               volume:
 *                 type: number
 *                 description: Order volume/lot size
 *               gapAmount:
 *                 type: number
 *                 description: Gap amount between orders
 *               positionsPerGrid:
 *                 type: integer
 *                 description: Number of positions per grid
 *               isDynamic:
 *                 type: boolean
 *                 description: Whether to use dynamic pricing
 *               openPrice:
 *                 type: number
 *                 description: Opening price for the first order
 *               comment:
 *                 type: string
 *                 description: Order comment
 *     responses:
 *       200:
 *         description: Orders secured successfully
 *       500:
 *         description: Error placing order with profit target
 */
app.post('/placeQuickGapPendingOrderWithProfitTarget', validateParams(['symbol', 'tradeAccountToken', 'profit', 'loss', 'positionCount', 'orderType', 'volume', 'gapAmount', 'positionsPerGrid', 'isDynamic', 'openPrice', 'comment']), asyncHandler(async (req, res) => {
  const { symbol, tradeAccountToken, profit, loss, positionCount, orderType, volume, gapAmount, positionsPerGrid, isDynamic, openPrice, comment, useDynamicLot, isSwingTrade } = req.body;
  try {
    await tradeLib.placeQuickGapPendingOrderWithProfitTarget({ symbol, tradeAccountToken, profit, loss, positionCount, orderType, volume, gapAmount, positionsPerGrid, isDynamic, openPrice, comment, useDynamicLot, isSwingTrade });
    res.json(createResponse(true, null, 'Orders secured successfully'));
  } catch (error) {
    console.log('Error placing order with target:', error?.message);
    res.status(500).json(createResponse(false, null, 'Error placing order with profit target', error));
  }
}));

/**
 * @swagger
 * /placeAdvancePendingOrderWithProfitTarget:
 *   post:
 *     summary: Place advanced pending orders with profit target range
 *     description: Places multiple pending orders with a range of profit targets from start to end
 *     tags: [Trading]
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             required:
 *               - symbol
 *               - tradeAccountToken
 *               - profitStart
 *               - profitEnd
 *               - loss
 *               - positionCount
 *               - orderType
 *               - volume
 *               - gapAmount
 *               - positionsPerGrid
 *               - comment
 *             properties:
 *               symbol:
 *                 type: string
 *                 description: Trading symbol (e.g., EURUSD)
 *               tradeAccountToken:
 *                 type: string
 *                 description: Connection ID or token
 *               profitStart:
 *                 type: number
 *                 description: Starting profit target level
 *               profitEnd:
 *                 type: number
 *                 description: Ending profit target level
 *               loss:
 *                 type: number
 *                 description: Stop loss level
 *               positionCount:
 *                 type: integer
 *                 description: Number of positions to open
 *               orderType:
 *                 type: string
 *                 description: Type of order to place
 *                 enum: [BUY, SELL, BUY_LIMIT, SELL_LIMIT, BUY_STOP, SELL_STOP]
 *               volume:
 *                 type: number
 *                 description: Order volume/lot size
 *               gapAmount:
 *                 type: number
 *                 description: Gap amount between orders
 *               positionsPerGrid:
 *                 type: integer
 *                 description: Number of positions per grid
 *               comment:
 *                 type: string
 *                 description: Order comment
 *     responses:
 *       200:
 *         description: Orders secured successfully
 *       500:
 *         description: Error placing order with profit target
 */
app.post('/placeAdvancePendingOrderWithProfitTarget', validateParams(['symbol', 'tradeAccountToken', 'profitStart', 'profitEnd', 'loss', 'positionCount', 'orderType', 'volume', 'gapAmount', 'positionsPerGrid', 'comment']), asyncHandler(async (req, res) => {
  const { symbol, tradeAccountToken, profitStart, profitEnd, loss, positionCount, orderType, volume, gapAmount, positionsPerGrid, comment } = req.body;
  try {
    await tradeLib.placeAdvancePendingOrderWithProfitTarget({ symbol, tradeAccountToken, profitStart, profitEnd, loss, positionCount, orderType, volume, gapAmount, positionsPerGrid, comment });
    res.json(createResponse(true, null, 'Orders secured successfully'));
  } catch (error) {
    console.log('Error placing order with target:', error?.message);
    res.json(createResponse(false, null, error?.message));
  }
}));

/**
 * @swagger
 * /calculateTakeProfit:
 *   post:
 *     summary: Calculate take profit level
 *     description: Calculates the take profit price level based on a target profit amount
 *     tags: [Calculations]
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             required:
 *               - tradeAccountToken
 *               - symbol
 *               - lotSize
 *               - profitTarget
 *               - orderType
 *             properties:
 *               tradeAccountToken:
 *                 type: string
 *                 description: Connection ID or token
 *               symbol:
 *                 type: string
 *                 description: Trading symbol (e.g., EURUSD)
 *               lotSize:
 *                 type: number
 *                 description: Size of the position in lots
 *               profitTarget:
 *                 type: number
 *                 description: Target profit amount in account currency
 *               orderType:
 *                 type: string
 *                 description: Type of order (BUY, SELL, etc.)
 *     responses:
 *       200:
 *         description: Take profit calculated successfully
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                 data:
 *                   type: object
 *                   properties:
 *                     takeProfit:
 *                       type: number
 *                       description: Calculated take profit price level
 *       500:
 *         description: Error calculating take profit
 */
app.post('/calculateTakeProfit', validateParams(['tradeAccountToken', 'symbol', 'lotSize', 'profitTarget', 'orderType']), asyncHandler(async (req, res) => {
  const { tradeAccountToken, symbol, lotSize, profitTarget, orderType } = req.body;
  try {
    const takeProfit = await tradeLib.calculateTakeProfit({ tradeAccountToken, symbol, lotSize, profitTarget, orderType });
    res.json(createResponse(true, { takeProfit }, 'Take profit calculated successfully'));
  } catch (error) { 
    console.log('Error calculating take profit:', error?.message);
    res.status(500).json(createResponse(false, null, 'Error calculating take profit', error));
  }
}));

/**
 * @swagger
 * /calculateStopLoss:
 *   post:
 *     summary: Calculate stop loss level
 *     description: Calculates the stop loss price level based on a target loss amount
 *     tags: [Calculations]
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             required:
 *               - tradeAccountToken
 *               - symbol
 *               - lotSize
 *               - lossTarget
 *               - orderType
 *             properties:
 *               tradeAccountToken:
 *                 type: string
 *                 description: Connection ID or token
 *               symbol:
 *                 type: string
 *                 description: Trading symbol (e.g., EURUSD)
 *               lotSize:
 *                 type: number
 *                 description: Size of the position in lots
 *               lossTarget:
 *                 type: number
 *                 description: Target loss amount in account currency
 *               orderType:
 *                 type: string
 *                 description: Type of order (BUY, SELL, etc.)
 *     responses:
 *       200:
 *         description: Stop loss calculated successfully
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                 data:
 *                   type: object
 *                   properties:
 *                     stopLoss:
 *                       type: number
 *                       description: Calculated stop loss price level
 *       500:
 *         description: Error calculating stop loss
 */
app.post('/calculateStopLoss', validateParams(['tradeAccountToken', 'symbol', 'lotSize', 'lossTarget', 'orderType']), asyncHandler(async (req, res) => {
  const { tradeAccountToken, symbol, lotSize, lossTarget, orderType } = req.body;
  try {
    const stopLoss = await tradeLib.calculateStopLoss({ tradeAccountToken, symbol, lotSize, lossTarget, orderType });
    res.json(createResponse(true, { stopLoss }, 'Stop loss calculated successfully'));
  } catch (error) {
    console.log('Error calculating stop loss:', error?.message);
    res.status(500).json(createResponse(false, null, 'Error calculating stop loss', error));
  } 
}));

/**
 * @swagger
 * /calculatePriceChange:
 *   post:
 *     summary: Calculate price change for a target amount
 *     description: Calculates the required price change to achieve a target profit/loss amount
 *     tags: [Calculations]
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             required:
 *               - tradeAccountToken
 *               - lotSize
 *               - symbol
 *               - amount
 *             properties:
 *               tradeAccountToken:
 *                 type: string
 *                 description: Connection ID or token
 *               lotSize:
 *                 type: number
 *                 description: Size of the position in lots
 *               symbol:
 *                 type: string
 *                 description: Trading symbol (e.g., EURUSD)
 *               amount:
 *                 type: number
 *                 description: Target profit/loss amount in account currency
 *     responses:
 *       200:
 *         description: Price change calculated successfully
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                 data:
 *                   type: object
 *                   properties:
 *                     priceChange:
 *                       type: number
 *                       description: Required price change in points
 *       500:
 *         description: Error calculating price change
 */
app.post('/calculatePriceChange', validateParams(['tradeAccountToken', 'lotSize', 'symbol', 'amount']), asyncHandler(async (req, res) => {
  const { tradeAccountToken, lotSize, symbol, amount } = req.body;
  try {
    const priceChange = await tradeLib.calculatePriceChange({ tradeAccountToken, lotSize, symbol, amount });
    res.json(createResponse(true, { priceChange }, 'Price change calculated successfully'));
  } catch (error) {
    console.log('Error calculating price change:', error?.message);
    res.status(500).json(createResponse(false, null, 'Error calculating price change', error));
  }
}));

/**
 * @swagger
 * /calculateStopLevel:
 *   post:
 *     summary: Calculate minimum stop level
 *     description: Calculates the minimum stop level distance required by the broker for a symbol
 *     tags: [Calculations]
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             required:
 *               - tradeAccountToken
 *               - symbol
 *             properties:
 *               tradeAccountToken:
 *                 type: string
 *                 description: Connection ID or token
 *               symbol:
 *                 type: string
 *                 description: Trading symbol (e.g., EURUSD)
 *     responses:
 *       200:
 *         description: Stop level calculated successfully
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                 data:
 *                   type: object
 *                   properties:
 *                     stopLevel:
 *                       type: number
 *                       description: Minimum stop level in points
 *       500:
 *         description: Error calculating stop level
 */
app.post('/calculateStopLevel', validateParams(['tradeAccountToken', 'symbol']), asyncHandler(async (req, res) => {
  const { tradeAccountToken, symbol } = req.body;
  try {
    const stopLevel = tradeLib.calculateStopLevel({ tradeAccountToken, symbol });
    res.json(createResponse(true, { stopLevel }, 'Stop level calculated successfully'));
  } catch (error) {
    console.log('Error calculating stop level:', error?.message);
    res.status(500).json(createResponse(false, null, 'Error calculating stop level', error));
  }
}));

/**
 * @swagger
 * /calculatePriceChangeAmount:
 *   post:
 *     summary: Calculate monetary amount for a price change
 *     description: Calculates the profit/loss amount that would result from a specified price change
 *     tags: [Calculations]
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             required:
 *               - tradeAccountToken
 *               - lotSize
 *               - symbol
 *               - priceChange
 *             properties:
 *               tradeAccountToken:
 *                 type: string
 *                 description: Connection ID or token
 *               lotSize:
 *                 type: number
 *                 description: Size of the position in lots
 *               symbol:
 *                 type: string
 *                 description: Trading symbol (e.g., EURUSD)
 *               priceChange:
 *                 type: number
 *                 description: Price change in points
 *     responses:
 *       200:
 *         description: Price change amount calculated successfully
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                 data:
 *                   type: object
 *                   properties:
 *                     priceChangeAmount:
 *                       type: number
 *                       description: Resulting profit/loss amount in account currency
 *       500:
 *         description: Error calculating price change amount
 */
app.post('/calculatePriceChangeAmount', validateParams(['tradeAccountToken', 'lotSize', 'symbol', 'priceChange']), asyncHandler(async (req, res) => {
  const { tradeAccountToken, lotSize, symbol, priceChange } = req.body;
  try {
    const priceChangeAmount = await tradeLib.calculatePriceChangeAmount({ tradeAccountToken, lotSize, symbol, priceChange });
    res.json(createResponse(true, { priceChangeAmount }, 'Price change amount calculated successfully'));
  } catch (error) {
    console.log('Error calculating price change amount:', error?.message);
    res.status(500).json(createResponse(false, null, 'Error calculating price change amount', error));
  }
}));

/**
 * @swagger
 * /calculateSpreadAmount:
 *   post:
 *     summary: Calculate spread cost
 *     description: Calculates the monetary cost of the spread for a given symbol and lot size
 *     tags: [Calculations]
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             required:
 *               - tradeAccountToken
 *               - symbol
 *               - lotSize
 *             properties:
 *               tradeAccountToken:
 *                 type: string
 *                 description: Connection ID or token
 *               symbol:
 *                 type: string
 *                 description: Trading symbol (e.g., EURUSD)
 *               lotSize:
 *                 type: number
 *                 description: Size of the position in lots
 *     responses:
 *       200:
 *         description: Spread amount calculated successfully
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                 data:
 *                   type: object
 *                   properties:
 *                     spreadAmount:
 *                       type: number
 *                       description: Cost of the spread in account currency
 *       500:
 *         description: Error calculating spread amount
 */
app.post('/calculateSpreadAmount', validateParams(['tradeAccountToken', 'symbol', 'lotSize']), asyncHandler(async (req, res) => {
  const { tradeAccountToken, symbol, lotSize } = req.body;
  try {
    const spreadAmount = await tradeLib.calculateSpreadAmount({ tradeAccountToken, symbol, lotSize });
    res.json(createResponse(true, { spreadAmount }, 'Spread amount calculated successfully'));
  } catch (error) {
    console.log('Error calculating spread amount:', error?.message);
    res.status(500).json(createResponse(false, null, 'Error calculating spread amount', error));
  }
}));

/**
 * @swagger
 * /calculateStopLevelAmount:
 *   post:
 *     summary: Calculate stop level cost
 *     description: Calculates the monetary value of the minimum stop level for a given symbol and lot size
 *     tags: [Calculations]
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             required:
 *               - tradeAccountToken
 *               - symbol
 *               - lotSize
 *             properties:
 *               tradeAccountToken:
 *                 type: string
 *                 description: Connection ID or token
 *               symbol:
 *                 type: string
 *                 description: Trading symbol (e.g., EURUSD)
 *               lotSize:
 *                 type: number
 *                 description: Size of the position in lots
 *     responses:
 *       200:
 *         description: Stop level amount calculated successfully
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                 data:
 *                   type: object
 *                   properties:
 *                     stopLevelAmount:
 *                       type: number
 *                       description: Monetary value of the minimum stop level in account currency
 *       500:
 *         description: Error calculating stop level amount
 */
app.post('/calculateStopLevelAmount', validateParams(['tradeAccountToken', 'symbol', 'lotSize']), asyncHandler(async (req, res) => {
  const { tradeAccountToken, symbol, lotSize } = req.body;
  try {
    const stopLevelAmount = await tradeLib.calculateStopLevelAmount({ tradeAccountToken, symbol, lotSize });
    res.json(createResponse(true, { stopLevelAmount }, 'Stop level amount calculated successfully'));
  } catch (error) {
    console.log('Error calculating stop level amount:', error?.message);
    res.status(500).json(createResponse(false, null, 'Error calculating stop level amount', error));
  }
}));

/**
 * @swagger
 * /placeQuickTradeOrder:
 *   post:
 *     summary: Place multiple quick trade orders
 *     description: Places multiple trade orders quickly with the same parameters
 *     tags: [Orders]
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             required:
 *               - orderModel
 *               - id
 *               - positionCount
 *             properties:
 *               orderModel:
 *                 type: object
 *                 properties:
 *                   symbol:
 *                     type: string
 *                     description: Trading symbol (e.g., EURUSD)
 *                   operation:
 *                     type: string
 *                     description: Order operation type (BUY, SELL, etc.)
 *                   volume:
 *                     type: number
 *                     description: Order volume/lot size
 *                   stoploss:
 *                     type: number
 *                     description: Stop loss level
 *                   takeprofit:
 *                     type: number
 *                     description: Take profit level
 *                   comment:
 *                     type: string
 *                     description: Order comment
 *               id:
 *                 type: string
 *                 description: Connection ID or token
 *               positionCount:
 *                 type: integer
 *                 description: Number of positions to open with the same parameters
 *     responses:
 *       200:
 *         description: Orders placed successfully
 *       500:
 *         description: Error placing orders
 */
app.post('/placeQuickTradeOrder', validateParams(['orderModel', 'id', 'positionCount']), asyncHandler(async (req, res) => {
  const { orderModel, id, positionCount } = req.body;
  for (let i = 0; i < positionCount; i++) {
    try {
      apiClient.orderSend(
        id,
        orderModel.symbol,
        orderModel.operation,
        orderModel.volume,
        0,
        10000,
        orderModel.stoploss,
        orderModel.takeprofit,
        orderModel.comment,
        '0',
        0,
        'Manually'
      );
    } catch (error) {
      console.log('Error sending order:', error?.message);
    }
  }
  res.json(createResponse(true, null, 'Orders placed successfully'));
}));

/**
 * @swagger
 * /closeTrades:
 *   post:
 *     summary: Close multiple trades based on criteria
 *     description: Closes trades for a specified symbol and order type within profit/loss ranges
 *     tags: [Orders]
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             required:
 *               - tradeAccountToken
 *               - symbol
 *               - orderType
 *               - startRange
 *               - endRange
 *               - maxProfit
 *               - maxLoss
 *             properties:
 *               tradeAccountToken:
 *                 type: string
 *                 description: Connection ID or token
 *               symbol:
 *                 type: string
 *                 description: Trading symbol (e.g., EURUSD)
 *               orderType:
 *                 type: string
 *                 description: Type of order to close (BUY, SELL, etc.)
 *               startRange:
 *                 type: number
 *                 description: Start of ticket range to close
 *               endRange:
 *                 type: number
 *                 description: End of ticket range to close
 *               maxProfit:
 *                 type: number
 *                 description: Maximum profit threshold for closing
 *               maxLoss:
 *                 type: number
 *                 description: Maximum loss threshold for closing
 *               positionCount:
 *                 type: number
 *                 description: Number of positions to close
 *     responses:
 *       200:
 *         description: Orders closed successfully
 *       500:
 *         description: Error closing trades
 */
app.post('/closeTrades', validateParams(['tradeAccountToken', 'symbol', 'orderType', 'startRange', 'endRange', 'maxProfit', 'maxLoss', 'positionCount', 'withoutSL']), asyncHandler(async (req, res) => {
  const { tradeAccountToken, symbol, orderType, startRange, endRange, maxProfit, maxLoss, positionCount, withoutSL } = req.body;
  try {
    await tradeLib.closeTrades({ tradeAccountToken, symbol, orderType, startRange, endRange, maxProfit, maxLoss, positionCount, withoutSL });
    res.json(createResponse(true, null, 'Orders closed successfully'));
  } catch (error) {
    console.log('Error closing trades:', error?.message);
    res.status(500).json(createResponse(false, null, 'Error closing trades', error));
  }
}));

/**
 * @swagger
 * /calculateSupportResistance:
 *   post:
 *     summary: Calculate support and resistance levels
 *     description: Calculates support and resistance levels for a specified symbol and timeframe
 *     tags: [Market Analysis]
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             required:
 *               - symbol
 *               - timeframe
 *             properties:
 *               symbol:
 *                 type: string
 *                 description: Trading symbol (e.g., EURUSD)
 *               timeframe:
 *                 type: string
 *                 description: Chart timeframe (e.g., M1, M5, H1, D1)
 *     responses:
 *       200:
 *         description: Support and resistance levels calculated successfully
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                 data:
 *                   type: object
 *                   properties:
 *                     SRLevels:
 *                       type: array
 *                       items:
 *                         type: number
 *                       description: Array of support and resistance price levels
 *       500:
 *         description: Error calculating support and resistance levels
 */
app.post('/calculateSupportResistance', validateParams(['symbol', 'timeframe']), asyncHandler(async (req, res) => {
  const { symbol, timeframe } = req.body;
  try {
    console.log('Calculating support/resistance for:', { symbol, timeframe });
    const SRLevels = await findSupportResistanceLevels(symbol, timeframe);
    res.json(createResponse(true, { SRLevels }, 'Support resistance levels calculated successfully'));
  } catch (error) {
    console.error('Error calculating support resistance:', error?.message);
    console.error('Error stack:', error?.stack);
    res.status(500).json(createResponse(false, null, 'Error calculating support resistance', error?.message));
  }
}));

/**
 * @swagger
 * /calculateUnrealizedProfit:
 *   post:
 *     summary: Calculate unrealized profit for all open positions
 *     description: Calculates the total unrealized profit/loss for all open positions in an account
 *     tags: [Account]
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             required:
 *               - tradeAccountToken
 *             properties:
 *               tradeAccountToken:
 *                 type: string
 *                 description: Connection ID or token
 *     responses:
 *       200:
 *         description: Unrealized profit calculated successfully
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                 data:
 *                   type: object
 *                   properties:
 *                     profit:
 *                       type: number
 *                       description: Total unrealized profit/loss
 *       500:
 *         description: Error calculating unrealized profit
 */
app.post('/calculateUnrealizedProfit', validateParams(['tradeAccountToken']), asyncHandler(async (req, res) => {
  const { tradeAccountToken } = req.body;
  try {
    const profit = await tradeLib.calculateUnrealizedProfit({ tradeAccountToken });
    res.json(createResponse(true, { profit }, 'Unrealized profit calculated successfully'));
  } catch (error) {
    console.log('Error calculating unrealized profit:', error?.message);
    res.status(500).json(createResponse(false, null, 'Error calculating unrealized profit', error?.message));
  }
}));

/**
 * @swagger
 * /calculateUnrealizedProfitBySymbol:
 *   post:
 *     summary: Calculate unrealized profit for a specific symbol
 *     description: Calculates the total unrealized profit/loss for all open positions of a specific symbol
 *     tags: [Account]
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             required:
 *               - tradeAccountToken
 *               - symbol
 *             properties:
 *               tradeAccountToken:
 *                 type: string
 *                 description: Connection ID or token
 *               symbol:
 *                 type: string
 *                 description: Trading symbol (e.g., EURUSD)
 *     responses:
 *       200:
 *         description: Unrealized profit calculated successfully
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                 data:
 *                   type: object
 *                   properties:
 *                     profit:
 *                       type: number
 *                       description: Total unrealized profit/loss for the symbol
 *       500:
 *         description: Error calculating unrealized profit
 */
app.post('/calculateUnrealizedProfitBySymbol', validateParams(['tradeAccountToken', 'symbol']), asyncHandler(async (req, res) => {
  const { tradeAccountToken, symbol } = req.body;
  try {
    const profit = await tradeLib.calculateUnrealizedProfitBySymbol({ tradeAccountToken, symbol });
    res.json(createResponse(true, { profit }, 'Unrealized profit calculated successfully'));
  } catch (error) {
    console.log('Error calculating unrealized profit:', error?.message);
    res.status(500).json(createResponse(false, null, 'Error calculating unrealized profit', error?.message));
  }
}));


/**
 * @swagger
 * /createGridAi:
 *   post:
 *     summary: Create a Grid AI
 *     description: Create a Grid AI
 *     tags: [Grid AI]
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             properties:
 *               id:
 *                 type: string
 *               tradeAccountToken:
 *                 type: string
 *               user_id:
 *                 type: string
 *               account_id:
 *                 type: string
 *               symbol:
 *                 type: string
 *               take_profit:
 *                 type: number
 *               trade_mode:
 *                 type: string
 *               volume:
 *                 type: number
 *               start_price:
 *                 type: number
 *               end_price:
 *                 type: number
 *               gap_amount:
 *                 type: number
 *               positions_per_grid:
 *                 type: number
 *               order_types:
 *                 type: string
 *               stop_loss:
 *                 type: number
 *               secure_trades:
 *                 type: number
 *               secure_amount:
 *                 type: number
 *               execution_amount:
 *                 type: number
 *               is_active:
 *                 type: boolean
 *               grid_type:
 *                 type: string
 */
app.post('/createGridAi', validateParams(['tradeAccountToken', 'user_id', 'account_id', 'symbol', 'take_profit', 'volume', 'start_price', 'end_price', 'gap_amount', 'positions_per_grid', 'order_types']), asyncHandler(async (req, res) => {
  const { id, tradeAccountToken, user_id, account_id, symbol, take_profit, trade_mode, volume, start_price, end_price, gap_amount, positions_per_grid, order_types, stop_loss, secure_trades, secure_amount, execution_amount, is_active, grid_type } = req.body;
  
  try {
    const grid_ai = await tradeLib.createGridAi({
      id,
      tradeAccountToken,
      user_id,
      account_id,
      symbol,
      take_profit,
      trade_mode,
      volume,
      start_price,
      end_price,
      gap_amount,
      positions_per_grid,
      stop_loss,
      secure_trades,
      secure_amount,
      execution_amount,
      order_types,
      is_active,
      grid_type
    });
    
    res.json(createResponse(true, grid_ai, 'Grid AI created successfully'));
  } catch (error) {
    console.error('Error creating grid AI:', error?.message);
    res.status(500).json(createResponse(false, null, 'Error creating grid AI', error?.message));
  }
}));


/**
 * @swagger
 * /createHedgeGridAi:
 *   post:
 *     description: Create hedge grid AI
 *     parameters:
 *       - in: body
 *         name: body
 *         schema:
 *           type: object
 *           required:
 *             - tradeAccountToken
 *             - user_id
 *             - account_id
 *             - symbol
 *             - take_profit
 *             - trade_mode
 *             - volume
 *             - gap_amount
 *             - position_count
 *             - positions_per_grid
 *             - startBuyPrice
 *             - endBuyPrice
 *             - startSellPrice
 *             - endSellPrice
 *         description: The hedge grid AI to create
 *     responses:
 *       200:
 *         description: The hedge grid AI was created
 *       400:
 *         description: Invalid hedge grid AI
 *       500:
 *         description: Some error
 */
app.post(
  '/createHedgeGridAi',
  validateParams([
    'tradeAccountToken',
    'user_id',
    'account_id',
    'symbol',
    'take_profit',
    'volume',
    'gap_amount',
    'position_count',
    'positions_per_grid',
    'startBuyPrice',
    'endBuyPrice',
    'startSellPrice',
    'endSellPrice'
  ]),
  asyncHandler(async (req, res) => {
    const {
      tradeAccountToken,
      user_id,
      account_id,
      symbol,
      take_profit,
      trade_mode,
      volume,
      gap_amount,
      position_count,
      positions_per_grid,
      stop_loss,
      secure_trades,
      secure_amount,
      execution_amount,
      is_active,
      grid_type,
      startBuyPrice,
      endBuyPrice,
      startSellPrice,
      endSellPrice,
      buy,
      sell,
      close_unsecured_positions,
      auto_secure,
      full_grid,
      take_profit_price,
      stop_loss_price,
    } = req.body;

    try {
      await tradeLib.createHedgeGridAi({
        tradeAccountToken,
        user_id,
        account_id,
        symbol,
        take_profit,
        trade_mode,
        volume,
        gap_amount,
        position_count,
        positions_per_grid,
        stop_loss,
        secure_trades,
        secure_amount,
        execution_amount,
        is_active,
        grid_type,
        startBuyPrice,
        endBuyPrice,
        startSellPrice,
        endSellPrice,
        buy,
        sell,
        close_unsecured_positions,
        auto_secure,
        full_grid,
        take_profit_price,
        stop_loss_price,
      });

      res.json(createResponse(true, null, 'Hedge grid AI created successfully'));
    } catch (error) {
      console.error('Error creating grid AI:', error.message);
      res.status(500).json(createResponse(false, null, 'Error creating grid AI', error.message));
    }
  })
);
/**
 * @swagger
 * /updateGridAi:
 *   post:
 *     summary: Update grid AI
 *     description: Update grid AI
 *     tags: [Grid AI]
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             properties:
 *               id:
 *                 type: string
 *               tradeAccountToken:
 *                 type: string
 *               user_id:
 *                 type: string
 *               account_id:
 *                 type: string
 *               symbol:
 *                 type: string
 *               take_profit:
 *                 type: number
 *               trade_mode:
 *                 type: string
 *               volume:
 *                 type: number
 *               start_price:
 *                 type: number
 *               end_price:
 *                 type: number
 *               gap_amount:
 *                 type: number
 *               positions_per_grid:
 *                 type: number
 *               stop_loss:
 *                 type: number
 *               secure_trades:
 *                 type: number
 *               secure_amount:
 *                 type: number
 *               execution_amount:
 *                 type: number
 *               is_active:
 *                 type: boolean
 *               grid_type:
 *                 type: string
 */
app.post('/updateGridAi', validateParams(['id']), asyncHandler(async (req, res) => {
  try {
    const grid_ai = await tradeLib.updateGridAi(req.body);
    res.json(createResponse(true, grid_ai, 'Grid AI updated successfully'));
  } catch (error) {
    console.error('Error updating grid AI:', error.message);
    res.status(500).json(createResponse(false, null, 'Error updating grid AI', error.message));
  }
}));


/**
 * @swagger
 * /disableGridAiById:
 *   post:
 *     summary: Disable a grid AI by ID
 *     description: Disables a grid AI by its ID
 *     tags: [Grid AI]
 *     parameters:
 *       - in: query
 *         name: id
 *         required: true
 *         schema:
 *           type: string
 *         description: ID of the grid AI to disable
 *     responses:
 *       200:
 *         description: Grid AI disabled successfully
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                 data:
 *                   type: object
 *                 message:
 *                   type: string
 *                 error:
 *                   type: string
 *       500:
 *         description: Error disabling grid AI
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                 data:
 *                   type: null
 *                 message:
 *                   type: string
 *                 error:
 *                   type: string
 */
app.post('/disableGridAiById', validateParams(['id']), asyncHandler(async (req, res) => {
  try {
    const grid_ai = await tradeLib.disableGridAiById(req.body.id);
    res.json(createResponse(true, grid_ai, 'Grid AI disabled successfully'));
  } catch (error) {
    console.error('Error disabling grid AI:', error.message);
    res.status(500).json(createResponse(false, null, 'Error disabling grid AI', error.message));
  }
}));


/**
 * @swagger
 * /placeSnrPendingOrder:
 *   post:
 *     summary: Place pending orders at support and resistance levels
 *     description: Places multiple pending orders at calculated support and resistance levels
 *     tags: [Trading]
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             required:
 *               - symbol
 *               - tradeAccountToken
 *               - profit
 *               - loss
 *               - positionCount
 *               - orderType
 *               - volume
 *               - gapAmount
 *               - positionsPerGrid
 *               - isDynamic
 *               - openPrice
 *               - endPrice
 *               - comment
 *             properties:
 *               symbol:
 *                 type: string
 *                 description: Trading symbol (e.g., EURUSD)
 *               tradeAccountToken:
 *                 type: string
 *                 description: Connection ID or token
 *               profit:
 *                 type: number
 *                 description: Profit target level
 *               loss:
 *                 type: number
 *                 description: Stop loss level
 *               positionCount:
 *                 type: integer
 *                 description: Number of positions to open
 *               orderType:
 *                 type: string
 *                 description: Type of order to place
 *               volume:
 *                 type: number
 *                 description: Order volume/lot size
 *               gapAmount:
 *                 type: number
 *                 description: Gap amount between orders
 *               positionsPerGrid:
 *                 type: integer
 *                 description: Number of positions per grid
 *               isDynamic:
 *                 type: boolean
 *                 description: Whether to use dynamic pricing
 *               openPrice:
 *                 type: number
 *                 description: Opening price for the first order
 *               endPrice:
 *                 type: number
 *                 description: Ending price for the last order
 *               comment:
 *                 type: string
 *                 description: Order comment
 *     responses:
 *       200:
 *         description: SNR pending orders placed successfully
 *       500:
 *         description: Error placing SNR pending order
 */
app.post('/placeSnrPendingOrder', validateParams(['symbol', 'tradeAccountToken', 'profit', 'loss', 'positionCount', 'orderType', 'volume', 'gapAmount', 'positionsPerGrid', 'isDynamic', 'openPrice', 'endPrice', 'comment']), asyncHandler(async (req, res) => {
  try {
    await tradeLib.placeSnrPendingOrder(req.body);
    res.json(createResponse(true, null, 'SNR pending orders placed successfully'));
  } catch (error) {
    console.error('Error placing SNR pending order:', error.message);
    res.status(500).json(createResponse(false, null, 'Error placing SNR pending order', error.message));
  }
}));


/**
 * @swagger
 * /updateTPAndSL:
 *   post:
 *     summary: Update take profit and stop loss levels
 *     description: Updates take profit and/or stop loss levels for multiple orders at once
 *     tags: [Orders]
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             required:
 *               - tradeAccountToken
 *               - orderList
 *               - toAmount
 *               - modifyType
 *             properties:
 *               tradeAccountToken:
 *                 type: string
 *                 description: Connection ID or token
 *               orderList:
 *                 type: array
 *                 description: List of order tickets to modify
 *                 items:
 *                   type: string
 *               toAmount:
 *                 type: number
 *                 description: New TP or SL level value
 *               modifyType:
 *                 type: string
 *                 description: Type of modification (TP or SL)
 *                 enum: [TP, SL]
 *     responses:
 *       200:
 *         description: TP/SL updated successfully
 *       500:
 *         description: Error updating TP/SL
 */
app.post('/updateTPAndSL', validateParams(['tradeAccountToken', 'orderList', 'toAmount', 'modifyType']), asyncHandler(async (req, res) => {
  try {
    await tradeLib.updateTPAndSL(req.body);
    res.json(createResponse(true, null, 'TP/SL updated successfully'));
  } catch (error) {
    console.error('Error updating TP/SL:', error.message);
    res.status(500).json(createResponse(false, null, 'Error updating TP/SL', error.message));
  }
}));


/**
 * @swagger
 * /calculateProfitLossAmount:
 *   post:
 *     summary: Calculate profit and loss
 *     description: Calculates profit and loss for the specified trading account
 *     tags: [Analysis]
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             required:
 *               - tradeAccountToken
 *             properties:
 *               tradeAccountToken:
 *                 type: string
 *                 description: Trading account token
 *     responses:
 *       200:
 *         description: Profit/loss amount calculated successfully
 *       500:
 *         description: Error calculating profit/loss amount
 */
app.post('/calculateProfitLossAmount', validateParams(['tradeAccountToken']), asyncHandler(async (req, res) => {
  const { tradeAccountToken } = req.body;
  try {
    const response = await tradeLib.calculateProfitLossAmount({ tradeAccountToken });
    res.json(createResponse(true, response, 'Profit/loss amount calculated successfully'));
  } catch (error) {
    console.error('Error calculating profit/loss amount:', error.message);
    checkAndBlacklist(tradeAccountToken);
    res.status(500).json(createResponse(false, null, 'Error calculating profit/loss amount', error.message));
  }
}));

/**
 * @swagger
 * /calculatePnL:
 *   post:
 *     summary: Calculate profit and loss
 *     description: Calculates profit and loss for the specified trading account
 *     tags: [Analysis]
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             required:
 *               - tradeAccountToken
 *             properties:
 *               tradeAccountToken:
 *                 type: string
 *                 description: Trading account token
 *     responses:
 *       200:
 *         description: PnL calculated successfully
 *       500:
 *         description: Error calculating PnL
 */
app.post('/calculatePnL', validateParams(['tradeAccountToken']), asyncHandler(async (req, res) => {
  const { tradeAccountToken } = req.body;
  try {
    const response = await tradeLib.calculatePnL({ tradeAccountToken });
    res.json(createResponse(true, response, 'PnL calculated successfully'));
  } catch (error) {
    console.error('Error calculating PnL:', error.message);
    checkAndBlacklist(tradeAccountToken);
    res.status(500).json(createResponse(false, null, 'Error calculating PnL', error.message));
  }
}));


/**
 * @swagger
 * /getGridAiByAccountNumberAndSymbol:
 *   post:
 *     summary: Get grid AI by account number and symbol
 *     description: Retrieves grid AI configuration by account number and symbol
 *     tags: [Grid AI]
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             required:
 *               - account_number
 *               - symbol
 *             properties:
 *               account_number:
 *                 type: string
 *                 description: Account number
 *               symbol:
 *                 type: string
 *                 description: Trading symbol (e.g., EURUSD)
 *     responses:
 *       200:
 *         description: Grid AI retrieved successfully
 *       500:
 *         description: Error retrieving grid AI
 */
app.post('/getGridAiByAccountNumberAndSymbol', validateParams(['account_number', 'symbol']), asyncHandler(async (req, res) => {
  const { account_number, symbol } = req.body;
  try {
    const response = await tradeLib.getGridAiByAccountNumberAndSymbol(account_number, symbol);
    //console.log('Grid AI retrieved successfully:', response);
    res.json(createResponse(true, response, 'Grid AI retrieved successfully'));
  } catch (error) {
    console.error('Error retrieving grid AI:', error.message);
    res.status(500).json(createResponse(false, null, 'Error retrieving grid AI', error.message));
  }
}));


/**
 * @swagger
 * /getGridAndPositionAiByAccountNumberAndSymbol:
 *   post:
 *     summary: Get grid AI and position AI by account number and symbol
 *     description: Retrieves grid AI and position AI configuration by account number and symbol
 *     tags: [Grid AI, Position AI]
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             required:
 *               - account_number
 *               - symbol
 *             properties:
 *               account_number:
 *                 type: string
 *                 description: Account number
 *               symbol:
 *                 type: string
 *                 description: Trading symbol (e.g., EURUSD)
 *     responses:
 *       200:
 *         description: Grid AI and position AI retrieved successfully
 *       500:
 *         description: Error retrieving grid AI and position AI
 */
app.post('/getGridAndPositionAiByAccountNumberAndSymbol', validateParams(['account_number', 'symbol']), asyncHandler(async (req, res) => {
  const { account_number, symbol } = req.body;
  try {
    const response = await tradeLib.getGridAndPositionAiByAccountNumberAndSymbol(account_number, symbol);
    res.json(createResponse(true, response, 'Grid AI and position AI retrieved successfully'));
  } catch (error) {
    console.error('Error retrieving grid AI and position AI:', error.message);
    res.status(500).json(createResponse(false, null, 'Error retrieving grid AI and position AI', error.message));
  }
}));



/**
 * @swagger
 * /createPositionAi:
 *   post:
 *     summary: Create position AI
 *     description: Creates a new position AI configuration
 *     tags: [Position AI]
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             required:
 *               - symbol
 *               - take_profit
 *               - stop_loss
 *               - order_type
 *               - volume
 *               - position_count
 *               - secure_trade
 *               - secure_amount
 *               - execution_amount
 *               - price
 *             properties:
 *               symbol:
 *                 type: string
 *                 description: Trading symbol (e.g., EURUSD)
 *               take_profit:
 *                 type: number
 *                 description: Take profit level
 *               stop_loss:
 *                 type: number
 *                 description: Stop loss level
 *               order_type:
 *                 type: string
 *                 description: Type of order (BUY, SELL, etc.)
 *               volume:
 *                 type: number
 *                 description: Order volume/lot size
 *               position_count:
 *                 type: integer
 *                 description: Number of positions to open
 *               secure_trade:
 *                 type: boolean
 *                 description: Whether to secure the trade
 *               secure_amount:
 *                 type: number
 *                 description: Amount to secure the trade
 *               execution_amount:
 *                 type: number
 *                 description: Execution amount
 *               price:
 *                 type: number
 *                 description: Price level
 *     responses:
 *       200:
 *         description: Position AI created successfully
 *       500:
 *         description: Error creating position AI
 */
app.post('/createPositionAi', validateParams(['symbol', 'take_profit', 'stop_loss', 'order_type', 'volume', 'position_count', 'secure_trade', 'secure_amount', 'execution_amount', 'user_id', 'price']), asyncHandler(async (req, res) => {
  const { symbol, take_profit, stop_loss, order_type, volume, position_count, secure_trade, secure_amount, execution_amount, price, account_id, user_id } = req.body;
  try {
    await tradeLib.createPositionAi({ symbol, take_profit, stop_loss, order_type, volume, position_count, secure_trade, secure_amount, execution_amount, price, account_id, user_id });
    res.json(createResponse(true, null, 'Position AI created successfully'));
  } catch (error) {
    console.error('Error creating position AI:', error.message);
    res.status(500).json(createResponse(false, null, 'Error creating position AI', error.message));
  }
}));

// Error handling middleware
app.use((err, req, res, next) => {
  console.error('Unhandled error:', {
    error: err.message,
    stack: err.stack,
    path: req.path,
    method: req.method,
    timestamp: new Date().toISOString()
  });
  
  res.status(500).json(createResponse(false, null, 'Internal server error', err));
});

// Start server
const port = process.env.PORT || DEFAULT_PORT;
app.listen(port, () => {
  console.log(`MT5 Server running on port ${port}`);
  console.log(`Environment: ${process.env.NODE_ENV || 'development'}`);
  console.log(`Swagger documentation available at http://localhost:${port}/api-docs`);
});

module.exports = app;