/**
 * Calculates the tick value and tick size for a currency pair based on price difference
 * @param {number} priceDifference - The difference in price (e.g., 1.3001 - 1.3000 = 0.0001)
 * @param {number} usdValue - The USD value per point
 * @param {number} lotSize - The lot size for the trade
 * @returns {Object} Object containing tick value, tick size, and lot size
 */
function calculateTickValues(priceDifference, usdValue, lotSize) {
    if (typeof priceDifference !== 'number' || typeof usdValue !== 'number' || typeof lotSize !== 'number') {
        throw new Error('Price difference, USD value, and lot size must be numbers');
    }

    if (priceDifference <= 0 || usdValue <= 0 || lotSize <= 0) {
        throw new Error('Price difference, USD value, and lot size must be positive numbers');
    }

    // Calculate tick value based on price difference
    const tickValue = priceDifference * usdValue * lotSize;
    const tickSize = priceDifference;

    return {
        tickValue,
        tickSize,
        lotSize
    };
}

module.exports = {
    calculateTickValues
};

function main() {
    // Example: Price moved from 1.3000 to 1.3001 (difference of 0.0001)
    const tickValues = calculateTickValues(9632, 60.43, 0.01);
    console.log('Tick Value:', tickValues.tickValue);    // Value in USD
    console.log('Tick Size:', tickValues.tickSize);      // Price difference
    console.log('Lot Size:', tickValues.lotSize);
}

main();