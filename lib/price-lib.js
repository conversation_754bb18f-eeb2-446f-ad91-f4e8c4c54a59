
const supabase = require('./supabaseClient');

const tick_data = [];

const initializeTickData = async () => {
  if (tick_data.length > 0) return;
  const { data, error } = await supabase
    .from('trade_symbol_amount')
    .select('*');
  if (error) throw error;
  tick_data.push(...data);
}

const getTickValueAndSize = async ({ symbol }) => {
  initializeTickData();
  const symbolData = tick_data.find(data => data.symbol === symbol);
  if (!symbolData) return null;
  return { tickValue: symbolData.tick_value, tickSize: symbolData.tick_size };
};


const calculatePriceChange = async params => {
  const { lotSize, symbol, amount } = params;
  const tick_params = await getTickValueAndSize({ symbol });
  if (tick_params === null) return null;
  const { tickValue, tickSize } = tick_params;
  return calculatePriceChangeAmountImpl(amount, tickSize, tickValue, lotSize);
};



const calculatePriceChangeAmount = async params => {
  const {lotSize, symbol, priceChange } = params;
  const tick_params = await getTickValueAndSize({ symbol });
  if (tick_params === null) return null;
  let { tickValue, tickSize } = tick_params;
  return calculatePriceChangeAmountImpl(
    priceChange,
    tickSize,
    tickValue,
    lotSize
  );
};


const calculatePriceChangeAmountImpl = (priceChange, tickSize, tickValue, lotSize) => {
  let priceChangeAmount = (priceChange * lotSize * tickValue) / tickSize;
  return priceChangeAmount;
}

module.exports = {
  calculatePriceChange,
  calculatePriceChangeAmount
};