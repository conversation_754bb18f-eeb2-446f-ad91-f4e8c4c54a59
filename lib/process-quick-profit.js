const { orderClose } = require('../mt5-service');

setInterval(() => {
  failedOrdersTickets = [];
}, 1000 * 60);

let failedOrdersTickets = [];
const processQuickProfit = async (token, openOrders) => {
    const filteredOrders = openOrders.filter(
      order =>
        order.takeProfit === 0 &&
        order.comment.includes('QT') &&
        (order.orderType === 'Buy' || order.orderType === 'Sell')
    );

    for (let order of filteredOrders) {
      const trade_info = order.comment.split('-');
      const tp = Number(trade_info[1]);
      const sl = Number(trade_info[2]);

      if (tp !== 0 && order.profit >= tp) {
        try {
          if(!failedOrdersTickets.includes(order.ticket)) {
            await orderClose(token, order.ticket, order.lots, 0, 10000);
          }
        } catch (error) {
          failedOrdersTickets.push(order.ticket);
          console.log('Error closing trade:', error?.message);
        }
      }

      if (sl !== 0 && order.profit <= sl * -1) {
        try {
          if(!failedOrdersTickets.includes(order.ticket)) {
            await orderClose(token, order.ticket, order.lots, 0, 10000);
          }
        } catch (error) {
          failedOrdersTickets.push(order.ticket);
          console.log('Error closing trade:', error?.message);
        }
      }
    } 
};

module.exports = { processQuickProfit };