const { priceHistory, priceHistoryToday } = require('../mt5-service');

class OHLCV {
  constructor(data, symbol) {
    this.time = data.time;
    this.open = data.openPrice;
    this.high = data.highPrice;
    this.low = data.lowPrice;
    this.close = data.closePrice;
    this.volume = data.volume || 1;
    this.symbol = symbol;
  }

  get bodySize() {
    return Math.abs(this.close - this.open);
  }

  get range() {
    return this.high - this.low;
  }

  get upperWick() {
    return this.high - Math.max(this.open, this.close);
  }

  get lowerWick() {
    return Math.min(this.open, this.close) - this.low;
  }

  get isBullish() {
    return this.close > this.open;
  }
}

// ---------------- Data Acquisition ----------------
async function fetchHistoricalDataImpl(
  id = '10zb1o13-kzud-gyjx-bfpy-8ltb0hs2ex6n',
  symbol = 'XAUUSDm',
  from = '2025-01-01T00:00:00',
  to = '2025-02-14T00:00:00',
  timeframe = 60
) {
  try {
    const data = await priceHistory(id, symbol, from, to, timeframe);
    if (!data || !Array.isArray(data) || data.length === 0) {
      throw new Error('Invalid or empty historical data received');
    }
    console.log('Received historical data:', {
      length: data.length,
      firstItem: data[0],
      lastItem: data[data.length - 1]
    });
    const ohlcvData = data.map(d => {
      if (!d || typeof d.openPrice === 'undefined') {
        console.error('Invalid data item:', d);
        throw new Error('Invalid data format received');
      }
      return new OHLCV(d, symbol);
    });
    return ohlcvData;
  } catch (error) {
    console.error('Error fetching historical data:', error);
    throw error;
  }
}


async function fetchTodayDataImpl(symbol = 'XAUUSDm', timeframe = 60) {
  try {
    const response = await priceHistoryToday('10zb1o13-kzud-gyjx-bfpy-8ltb0hs2ex6n', symbol, timeframe);
    const data = await response.json();
    console.log('Received today data:', data);
    if (!data || !Array.isArray(data) || data.length === 0) {
      throw new Error('Invalid or empty historical data received');
    }
    console.log('Received historical data:', {
      length: data.length,
      firstItem: data[0],
      lastItem: data[data.length - 1]
    });
    const ohlcvData = data.map(d => {
      if (!d || typeof d.openPrice === 'undefined') {
        console.error('Invalid data item:', d);
        throw new Error('Invalid data format received');
      }
      return new OHLCV(d, symbol);
    });
    return ohlcvData;
  } catch (error) {
    console.error('Error fetching historical data:', error);
    throw error;
  }
}

async function fetchTestingData(symbol) {
  return await fetchHistoricalDataImpl(
    '10zb1o13-kzud-gyjx-bfpy-8ltb0hs2ex6n',
    symbol,
    '2025-01-10T00:00:00',
    '2025-02-14T00:00:00',
    60
  );
}


async function fetchSyntheticTestingData(symbol) {
  return await fetchHistoricalDataImpl(
    'gzne2g9m-ab4p-r5pn-q47m-yy9nippb6zu4',
    symbol,
    '2025-01-10T00:00:00',
    '2025-02-14T00:00:00',
    5
  );
}


async function fetchTodayTestingData(symbol) {
  return await fetchTodayDataImpl(symbol, 3);
}


/**
 * Calculates the Average True Range (ATR) for volatility-based stop loss
 * @param {Array} candles - Array of candles
 * @param {number} period - Period for ATR calculation
 * @returns {number} ATR value
 */
function calculateATR(candles, period = 14) {
  if (candles.length < period) return 0;

  const trueRanges = candles.map((candle, i) => {
    if (i === 0) return candle.high - candle.low;
    const previousClose = candles[i - 1].close;
    return Math.max(
      candle.high - candle.low,
      Math.abs(candle.high - previousClose),
      Math.abs(candle.low - previousClose)
    );
  });

  const atr = trueRanges.slice(0, period).reduce((sum, tr) => sum + tr, 0) / period;
  return atr;
}


module.exports = { 
  fetchHistoricalDataImpl, 
  fetchTestingData, 
  fetchSyntheticTestingData, 
  fetchTodayTestingData, 
  calculateATR 
};