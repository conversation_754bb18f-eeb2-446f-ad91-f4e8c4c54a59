// mq-mqtt.js – singleton MQTT helper
const mqtt = require('mqtt');
const { v4: uuid } = require('uuid');

// rabbitmq-pc…cloud exposes MQTT on 1883; "%2Ftrade:" encodes the vhost
const MQTT_URI =
  'mqtt://tradebot:s3cr3t!' +
  '@rabbitmq-pc8c0woscocgg8044c08wk0k.trademanager.cloud:1883';

const TOPIC_ROOT = '%2F'; // same semantic as old exchange
const KEEPALIVE = 60; // seconds – matches broker default  [oai_citation:8‡RabbitMQ](https://www.rabbitmq.com/docs/heartbeats?utm_source=chatgpt.com)
const RECONNECT = 1000; // ms   – 1 s back-off  [oai_citation:9‡GitHub](https://github.com/mqttjs/MQTT.js/blob/main/README.md?utm_source=chatgpt.com)

const client = mqtt.connect(MQTT_URI, {
  clientId: `nodePub_${uuid().slice(0, 8)}`, // ≤23 chars per spec  [oai_citation:10‡RabbitMQ](https://www.rabbitmq.com/docs/mqtt?utm_source=chatgpt.com)
  clean: true,
  keepalive: KEEPALIVE,
  reconnectPeriod: RECONNECT
});

client.on('connect', () => console.log('✔ MQTT_CONNECTED'));
client.on('reconnect', () => console.log('… reconnecting'));
client.on('error', err => console.error('❌ MQTT error', err.message));

/** Publish one trade signal (symbol + account-scoped) */
function publishTrade(evt) {
  return new Promise((res, rej) => {
    const topic = `${TOPIC_ROOT}/${evt.symbol}/${evt.account_number}`; // slash → dot in AMQP  [oai_citation:11‡RabbitMQ](https://www.rabbitmq.com/docs/mqtt?utm_source=chatgpt.com)
    const payload = JSON.stringify(evt);
    console.log(evt);
    client.publish(
      topic,
      payload,
      {
        qos: 0, // fire-and-forget, lowest latency  [oai_citation:12‡HiveMQ](https://www.hivemq.com/blog/mqtt-essentials-part-6-mqtt-quality-of-service-levels/?utm_source=chatgpt.com)
        retain: false,
        properties: { messageId: uuid() } // for audit/debug
      },
      err => (err ? rej(err) : res())
    );
  });
}

module.exports = { publishTrade };
