// orderBlockDetector.js
class Candlestick {
  constructor(open, high, low, close) {
    this.open = open;
    this.high = high;
    this.low = low;
    this.close = close;
  }

  isBullish() {
    return this.close > this.open;
  }

  isBearish() {
    return this.close < this.open;
  }

  bodySize() {
    return Math.abs(this.close - this.open);
  }
}

class OrderBlockDetector {
  constructor(candles, config = {}) {
    this.candles = candles.map(
      c => new Candlestick(c.open, c.high, c.low, c.close)
    );
    this.config = {
      minBodyRatio: config.minBodyRatio || 0.5,
      minBreakoutStrength: config.minBreakoutStrength || 1.5,
      ...config
    };
  }

  detectBullishOrderBlock(index) {
    if (index < 1 || index >= this.candles.length - 1) return false;

    const prevCandle = this.candles[index - 1];
    const currentCandle = this.candles[index];
    const nextCandle = this.candles[index + 1];

    return (
      prevCandle.isBearish() &&
      currentCandle.bodySize() <
        prevCandle.bodySize() * this.config.minBodyRatio &&
      nextCandle.isBullish() &&
      nextCandle.bodySize() >
        currentCandle.bodySize() * this.config.minBreakoutStrength &&
      nextCandle.close > prevCandle.high
    );
  }

  detectBearishOrderBlock(index) {
    if (index < 1 || index >= this.candles.length - 1) return false;

    const prevCandle = this.candles[index - 1];
    const currentCandle = this.candles[index];
    const nextCandle = this.candles[index + 1];

    return (
      prevCandle.isBullish() &&
      currentCandle.bodySize() <
        prevCandle.bodySize() * this.config.minBodyRatio &&
      nextCandle.isBearish() &&
      nextCandle.bodySize() >
        currentCandle.bodySize() * this.config.minBreakoutStrength &&
      nextCandle.close < prevCandle.low
    );
  }

  // Check if an order block has been mitigated
  isOrderBlockMitigated(obIndex, obType) {
    const obCandle = this.candles[obIndex];
    // For bullish OB: mitigated if price drops below the low
    // For bearish OB: mitigated if price rises above the high
    const mitigationLevel = obType === 'bullish' ? obCandle.low : obCandle.high;

    // Check all candles after the order block
    for (let i = obIndex + 1; i < this.candles.length; i++) {
      if (obType === 'bullish' && this.candles[i].close <= mitigationLevel) {
        return true;
      }
      if (obType === 'bearish' && this.candles[i].close >= mitigationLevel) {
        return true;
      }
    }
    return false;
  }

  analyze() {
    const orderBlocks = this.candles
      .map((candle, index) => {
        let type = null;
        if (this.detectBullishOrderBlock(index)) {
          type = 'bullish';
        } else if (this.detectBearishOrderBlock(index)) {
          type = 'bearish';
        }
        if (type) {
          return {
            candle: { ...candle },
            isOrderBlock: true,
            type: type,
            index: index,
            mitigated: this.isOrderBlockMitigated(index, type)
          };
        }
        return null;
      })
      .filter(result => result !== null);

    // Return only unmitigated order blocks
    return orderBlocks.filter(ob => !ob.mitigated);
  }

  // Get only unmitigated order blocks (same as analyze now)
  getOrderBlocks() {
    return this.analyze();
  }
}

// Export for different module systems
if (typeof module !== 'undefined' && typeof module.exports !== 'undefined') {
  module.exports = OrderBlockDetector;
} else if (typeof window !== 'undefined') {
  window.OrderBlockDetector = OrderBlockDetector;
}
