// alphaToken.js
const crypto = require('node:crypto');

/**
 * Generate a cryptographically secure token using only A–Z and a–z.
 * @param {number} length - Desired token length (default: 32).
 * @returns {string}
 */
function alphaToken(length = 32) {
  if (!Number.isInteger(length) || length < 1) {
    throw new TypeError('length must be a positive integer');
  }

  const alphabet = 'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz';
  const base = alphabet.length; // 52
  const maxUnbiased = 256 - (256 % base); // rejection threshold

  let out = '';
  // Generate a bit more than needed per batch to reduce iterations
  const batchSize = Math.max(64, Math.ceil(length * 1.25));

  while (out.length < length) {
    const bytes = crypto.randomBytes(batchSize);
    for (let i = 0; i < bytes.length && out.length < length; i++) {
      const b = bytes[i];
      if (b < maxUnbiased) {
        out += alphabet[b % base];
      }
    }
  }
  return out;
}

module.exports = { alphaToken };
