const { isEven, generateQuickTradeComment } = require('./util');
const { orderSend, getQuote } = require('../mt5-service');

const sendBotOrder = async (bot, tradeQuote, mt5_token) => {
  let finalModel = {
    symbol: bot.symbol,
    operation: bot.type,
    volume: bot.volume,
    price: 0,
    slippage: 1000,
    stoploss: 0,
    takeprofit: 0
  };

  let position_count = bot.position_count;
  if (
    bot.trade_mode === 'GRID' &&
    (bot.type === 'BuyStop' ||
      bot.type === 'SellStop' ||
      bot.type === 'BuyLimit' ||
      bot.type === 'SellLimit')
  ) {
    position_count = bot.position_count * 2;
  }

  if (
    bot.trade_mode === 'GRID_PLUS' &&
    (bot.type === 'BuyStop' ||
      bot.type === 'SellStop' ||
      bot.type === 'BuyLimit' ||
      bot.type === 'SellLimit')
  ) {
    position_count = bot.position_count * 3;
  }

  if (
    bot.positions_per_grid === 0 &&
    bot.trade_mode === 'GRID' &&
    (bot.type === 'BuyStop' ||
      bot.type === 'SellStop' ||
      bot.type === 'BuyLimit' ||
      bot.type === 'SellLimit')
  ) {
    bot.positions_per_grid = 1;
  }

  for (let i = 0; i < position_count; i++) {
    if (bot.trade_mode === 'NORMAL') {
      finalModel = initializeTradingMode(bot, tradeQuote, finalModel, 1);
      try {
        if (bot.secure_trades) {
          finalModel.takeprofit = 0;
          finalModel.comment = bot.comment || generateQuickTradeComment(bot);
        }
        sendOrderImpl(mt5_token, finalModel);
      } catch (error) {
        console.log('Error in normal mode:', error.message);
      }
    } else if (bot.trade_mode === 'ADVANCED') {
      finalModel = initializeTradingMode(bot, tradeQuote, finalModel, i);
      try {
        for (let j = 0; j < bot.positions_per_grid; j++) {
          if (bot.secure_trades) {
            finalModel.takeprofit = 0;
            finalModel.comment = bot.comment || generateQuickTradeComment(bot);
          }
          sendOrderImpl(mt5_token, finalModel);
        }
      } catch (error) {
        console.log('Error in advanced mode:', error.message);
      }
    } else if (bot.trade_mode === 'GRID') {
      if (isEven(i)) {
        finalModel = initializeTradingMode(bot, tradeQuote, finalModel, i);
        try {
          for (let j = 0; j < bot.positions_per_grid; j++) {
            if (bot.secure_trades) {
              finalModel.takeprofit = 0;
              finalModel.comment = bot.comment || generateQuickTradeComment(bot);
            }
            sendOrderImpl(mt5_token, finalModel);
          }
        } catch (error) {
          console.log('Error in grid mode:', error.message);
        }
      }
    }  else if (bot.trade_mode === 'GRID_PLUS') {
      if (i % 3 === 0) {
        finalModel = initializeTradingMode(bot, tradeQuote, finalModel, i);
        try {
          for (let j = 0; j < bot.positions_per_grid; j++) {
            if (bot.secure_trades) {
              finalModel.takeprofit = 0;
              finalModel.comment = bot.comment || generateQuickTradeComment(bot);
            }
            sendOrderImpl(mt5_token, finalModel);
          }
        } catch (error) {
          console.log('Error in grid mode:', error.message);
        }
      }
    }
  }
};

const sendOrderImpl = async (mt5_token, finalModel) => {
  console.log('Sending order:', finalModel);
  try {
    await orderSend(
      mt5_token,
      finalModel.symbol,
      finalModel.operation,
      finalModel.volume,
      finalModel.price,
      finalModel.slippage,
      finalModel.stoploss,
      finalModel.takeprofit,
      finalModel.comment || '',
      0,
      0,
      'Manually'
    );
  } catch (error) {
    console.error('Error sending order:', error.message);
  }
};

const initializeTradingMode = (
  bot,
  tradeQuote,
  finalModel,
  price_position_index,
) => {
  let spread = Math.abs(tradeQuote.ask - tradeQuote.bid);
  finalModel.price =
    bot.type == 'BuyStop' || bot.type === 'SellLimit' || bot.type === 'Buy'
      ? tradeQuote.ask + (bot.start_distance * bot.grid_size) + (price_position_index * bot.grid_size) + (spread*1.1)
      : tradeQuote.bid - (bot.start_distance * bot.grid_size) - (price_position_index * bot.grid_size) - (spread*1.1);

  if (bot.use_take_profit) {
    finalModel.takeprofit =
      bot.type == 'BuyStop' || bot.type === 'SellLimit' || bot.type === 'Buy'
        ? finalModel.price + ((bot.take_profit_grid_size * bot.grid_size))
        : finalModel.price - ((bot.take_profit_grid_size * bot.grid_size));
  }

  if (bot.use_stop_loss) {
    finalModel.stoploss =
      bot.type == 'BuyStop' || bot.type === 'SellLimit' || bot.type === 'Buy'
        ? finalModel.price - (bot.stop_loss_grid_size || 1) * bot.grid_size
        : finalModel.price + (bot.stop_loss_grid_size || 1) * bot.grid_size;
  }
  return finalModel;
};


  const _onPlaceOrderByType = async (type, bot, token) => {
    let orderModel = {
      type,
      use_take_profit: bot.use_take_profit,
      use_stop_loss: bot.use_stop_loss,
      grid_size: bot.grid_size,
      start_distance: bot.start_distance,
      take_profit_grid_size: bot.take_profit_grid_size,
      stop_loss_grid_size: bot.stop_loss_grid_size,
      volume: bot.volume,
      position_count: bot.position_count,
      symbol: bot.symbol,
      secure_grid_size: bot.secure_grid_size,
      trade_mode: bot.trade_mode,
      positions_per_grid: bot.positions_per_grid || 1,
      secure_trades: bot.secure_trades
    };
    
    try {
      const quote = await getQuote(token, symbol);
      if(quote !== null) {
          await sendBotOrder(orderModel, quote, id);
      }
    } catch (error) {
      console.log('Error sending order:', error?.message);
    }
  };

  const _onSecureTrades = async (bot, token) => {
    try {
      ApiClient.tmSecureTrades(token, bot.id);
    } catch (error) {
      console.log('Error setting secure trades:', error?.message);
    }
  };

// function main() {

//   let bot = {
//     id: 1,
//     created_at: '2024-07-10T04:41:56.990887+00:00',
//     type: 'Buy',
//     use_dynamic_mode: false,
//     use_take_profit: true,
//     use_stop_loss: true,
//     grid_size: 0.5,
//     start_distance: 1,
//     take_profit_grid_size: 1,
//     stop_loss_grid_size: 0,
//     volume: 5,
//     position_count: 5,
//     user_account_id: 4,
//     trade_account_id: 4,
//     symbol: 'Step Index',
//     updated_at: '2024-07-11T19:02:31.234+00:00',
//     is_active: true,
//     secure_grid_size: 0,
//     trade_account_login: '********',
//     bot_mt5_token: 'fw0zbfqs-n19e-cv0l-hg2a-fyxgni7ifkcr',
//     description: '',
//     trade_mode: 'GRID'
//   };

//   let tradeQuote = {
//     bid: 10.1,
//     ask: 10.2
//   }

//   let finalModel = {
//     symbol: bot.symbol,
//     operation: bot.type == 'buy' ? 'BuyStop' : 'SellStop',
//     volume: bot.volume,
//     price: 0,
//     slippage: 1000,
//     stoploss: 0,
//     takeprofit: 0
//   };

//   let position_count = bot.position_count;
//   if (bot.trade_mode === 'GRID') {
//     position_count = bot.position_count * 2;
//   }

//   for (let i = 0; i < position_count; i++) {
//     if (bot?.trade_mode === 'NORMAL') {
//       finalModel = initializeTradingMode(bot, tradeQuote, finalModel, 0, 1);
//       console.log('Normal Mode:', finalModel);
//       //sendOrderImpl(mt5_token, finalModel);
//     } else if (bot.trade_mode === 'ADVANCED') {
//       finalModel = initializeTradingMode(bot, tradeQuote, finalModel, i, i + 1);
//       console.log('Advanced Mode:', finalModel);
//       //sendOrderImpl(mt5_token, finalModel);
//     } else if (bot.trade_mode === 'GRID') {
//       if (isEven(i)) {
//         finalModel = initializeTradingMode(bot,tradeQuote, finalModel, i, i + 1);
//         console.log('Grid Mode:', finalModel);
//         if (bot.grid_mode_position_count !== null && bot.grid_mode_position_count > 1) {
//           for (let j = 1; j < bot.grid_mode_position_count; j++) {
//             //sendOrderImpl(mt5_token, finalModel);
//           }
//         }
//       }
//     }
//   }
// }

// main();

module.exports = {
  sendBotOrder
};
