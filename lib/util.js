const delay = ms => new Promise(resolve => setTimeout(resolve, ms));

const currencyFormat = (currencySymbol, num) => {
  return (
    `${currencySymbol} ` +
    num.toFixed(2).replace(/(\d)(?=(\d{3})+(?!\d))/g, '$1,')
  );
};

const isEven = n => {
  return n % 2 === 0;
};

const endsWith = (str, searchString, length) => {
  if (length === undefined || length > str.length) {
    length = str.length;
  }
  return str.substring(length - searchString.length, length) === searchString;
};

const roundNumber = (num, precision) => {
  const factor = Math.pow(10, precision);
  return Math.round(num * factor) / factor;
};

const generateSecuredTradeComment = (
  finalOrderModel,
  order_type,
  grid_size,
  hedgeComment = '',
  secure_grid_size = 1
) => {
  let price = 0;
  let sl_price = 0;
  let exec_price = 0;
  let hedge_price = 0;

  if (order_type === 'BuyStop' || order_type === 'Buy') {
    price = finalOrderModel.price;
    sl_price = price + grid_size * 1;
    exec_price = price + grid_size * (1 + 1.7);
    hedge_price = price - grid_size * (1 + 1.5);
  }

  if (order_type === 'SellStop' || order_type === 'Sell') {
    price = finalOrderModel.price;
    sl_price = price - grid_size * 1;
    exec_price = price - grid_size * (1 + 1.7);
    hedge_price = price + grid_size * (1 + 1.5);
  }

  if (order_type === 'SellLimit') {
    price = finalOrderModel.price;
    sl_price = price - grid_size * 1;
    exec_price = price - grid_size * (1 + 1.7);
    hedge_price = price + grid_size * (1 + 1.5);
  }

  if (order_type === 'BuyLimit') {
    price = finalOrderModel.price;
    sl_price = price + grid_size * 1;
    exec_price =
      price +
      grid_size *
        (1 +
          (secure_grid_size === 0 || secure_grid_size === 1
            ? 1.7
            : secure_grid_size));
    hedge_price = price - grid_size * (1 + 1.5);
  }
  return hedgeComment !== ''
    ? `STH-${customRound(sl_price)}-${customRound(exec_price)}-${customRound(
        hedge_price
      )}-${hedgeComment}`
    : `STH-${customRound(sl_price)}-${customRound(exec_price)}-${customRound(
        hedge_price
      )}`;
};


const generateQuickTradeComment = (bot) => {
  return bot.quick_profit_enabled ? `QT-${customRound(bot.quick_profit_amount)}-${customRound(bot.quick_loss_amount)}` : '';
}

function customRound(number) {
  // Convert the number to an absolute value to handle negative numbers
  const absoluteNumber = Math.abs(number);

  // Extract the integer part (before the decimal)
  const integerPart = Math.floor(absoluteNumber);

  // Determine the number of digits before the decimal
  const digitsBeforeDecimal = integerPart.toString().length;

  // Determine the number of decimal places based on the digits before the decimal
  const decimalPlaces =
    digitsBeforeDecimal >= 5
      ? 0
      : digitsBeforeDecimal >= 4
      ? 1
      : digitsBeforeDecimal >= 3
      ? 3
      : integerPart === 0
      ? 5
      : 4;

  // Round the number to the determined decimal places
  const factor = Math.pow(10, decimalPlaces);
  return Math.round(number * factor) / factor;
}

function getLastFourDigits(number) {
  // Convert the number to a string
  const numStr = number.toString();

  // Get the last 4 characters of the string
  const lastFourDigits = numStr.slice(-4);

  // Convert the result back to a number
  return parseInt(lastFourDigits, 10);
}

function getLastThreeDigits(number) {
  // Convert the number to a string
  const numStr = number.toString();

  // Get the last 4 characters of the string
  const lastFourDigits = numStr.slice(-3);

  // Convert the result back to a number
  return parseInt(lastFourDigits, 10);
}

/**
 * Check if a price is between two bounds.
 * @param {number} price - The price to check.
 * @param {number} lowerBound - The lower bound.
 * @param {number} upperBound - The upper bound.
 * @returns {boolean} - True if the price is between the bounds, false otherwise.
 */
function priceIsBetween(price, lowerBound, upperBound) {
  const min = Math.min(lowerBound, upperBound);
  const max = Math.max(lowerBound, upperBound);
  return price >= min && price <= max;
}

/**
 * Get a list of prices between a start and end price with a given count.
 * @param {number} start - The starting price.
 * @param {number} end - The ending price.
 * @param {number} count - The number of prices to generate.
 * @returns {number[]} - An array of prices.
 */
function getPriceList(start, end, count) {
  if (count < 2) {
    return [start];
  }

  const step = Math.abs(end - start) / (count - 1);
  const priceList = [];

  for (let i = 0; i < count; i++) {
    const price = start + (step * i);
    priceList.push(customRound(price));
  }

  return priceList;
}


function calculatePriceChangeImpl(lotSize, tickValue, tickSize, profitTarget) {
  // Calculate the number of ticks needed to reach the profit target
  const ticksNeeded = profitTarget / (lotSize * tickValue);

  // Calculate the price change
  const priceChange = ticksNeeded * tickSize;
  return priceChange;
}


function calculateStopLossImpl(openPrice, lotSize, tickValue, tickSize, profitTarget, orderType, spread) {
  // Validate order type
  if (orderType !== 'Buy' && orderType !== 'Sell' && orderType !== 'BuyLimit' && orderType !== 'SellLimit' && orderType !== 'BuyStop' && orderType !== 'SellStop') {
    throw new Error("Order type must be either 'Buy', 'Sell', 'BuyLimit', 'SellLimit', 'BuyStop', 'SellStop'");
  }

  // Convert spread to price units
  const spreadInPrice = spread * tickSize;

  // Calculate the actual entry price considering the spread
  let entryPrice;
  if (orderType === 'Buy' || orderType === 'BuyLimit' || orderType === 'BuyStop') {
    entryPrice = openPrice + spreadInPrice;
  } else if(orderType === 'Sell' || orderType === 'SellLimit' || orderType === 'SellStop') {
    entryPrice = openPrice - spreadInPrice;
  }

  // Calculate the number of ticks needed to reach the profit target
  const ticksNeeded = profitTarget / (lotSize * tickValue);
  
  // Calculate the price change
  const priceChange = ticksNeeded * tickSize;
  
  // Calculate the take profit price based on order type
  let stopLossPrice;
  if (orderType === 'Buy' || orderType === 'BuyLimit' || orderType === 'BuyStop') {
    stopLossPrice = entryPrice - priceChange;
  } else if(orderType === 'Sell' || orderType === 'SellLimit' || orderType === 'SellStop') {
    stopLossPrice = entryPrice + priceChange;
  }
  
  // Round to 5 decimal places, which is common in Forex
  return Number(stopLossPrice.toFixed(5));
}


function calculateTakeProfitImpl(openPrice, lotSize, tickValue, tickSize, profitTarget, orderType, spread, multiplier = 1) {
  // Validate order type
  if (!['Buy', 'Sell', 'BuyStop', 'SellStop', 'BuyLimit', 'SellLimit'].includes(orderType)) {
    throw new Error("Order type must be one of: 'Buy', 'Sell', 'BuyStop', 'SellStop', 'BuyLimit', 'SellLimit'");
  }

  if(tickSize === 0 && tickValue !== 0) {
    tickSize = tickValue;
  }

  // Convert spread to price units
  const spreadInPrice = spread * tickSize;

  // Calculate the actual entry price considering the spread
  let entryPrice;
  if (orderType === 'Buy' || orderType === 'SellLimit' || orderType === 'BuyStop') {
    entryPrice = openPrice + spreadInPrice;
  } else if(orderType === 'Sell' || orderType === 'BuyLimit' || orderType === 'SellStop') {
    entryPrice = openPrice - spreadInPrice;
  }

  // Calculate the number of ticks needed to reach the profit target
  const ticksNeeded = profitTarget / (lotSize * tickValue);
  
  // Calculate the price change
  const priceChange = ticksNeeded * tickSize;
  
  // Calculate the take profit price based on order type
  let takeProfitPrice;
  if (orderType === 'Buy' || orderType === 'BuyStop' || orderType === 'BuyLimit') {
    takeProfitPrice = entryPrice + (priceChange * multiplier);
  } else if (orderType === 'Sell' || orderType === 'SellStop' || orderType === 'SellLimit') {
    takeProfitPrice = entryPrice - priceChange * multiplier;
  }
  
  // Round to 5 decimal places, which is common in Forex
  return Number(takeProfitPrice.toFixed(5));
}


const calculatePriceChangeAmountImpl = (priceChange, tickSize, tickValue, lotSize) => {
  let priceChangeAmount = (priceChange * lotSize * tickValue) / tickSize;
  return priceChangeAmount;
}

const minifyOrder = order => {
  return {
    ticket: order.ticket,
    profit: order.profit,
    swap: order.swap,
    commission: order.commission,
    openPrice: order.openPrice,
    lots: order.lots,
    orderType: order.orderType,
    symbol: order.symbol,
    comment: order.comment,
    stopLoss: order.stopLoss,
    takeProfit: order.takeProfit,
    openTime: order.openTime,
  };
}


const calculateSwingPoints = (startPrice, endPrice) => {
  const movement = Math.abs(endPrice - startPrice);
  const direction = endPrice > startPrice ? 'up' : 'down';
  
  // Calculate Fibonacci levels
  const level618 = direction === 'up' 
    ? endPrice - (movement * 0.618)
    : endPrice + (movement * 0.618);
    
  const level796 = direction === 'up'
    ? endPrice - (movement * 0.796)
    : endPrice + (movement * 0.796);

  return {
    level618: Number(level618.toFixed(5)),
    level796: Number(level796.toFixed(5))
  };
}


module.exports = {
  delay,
  currencyFormat,
  isEven,
  endsWith,
  roundNumber,
  generateSecuredTradeComment,
  customRound,
  getLastFourDigits,
  getLastThreeDigits,
  priceIsBetween,
  getPriceList,
  calculateTakeProfitImpl,
  calculateStopLossImpl,
  calculatePriceChangeImpl,
  calculatePriceChangeAmountImpl,
  generateQuickTradeComment,
  minifyOrder,
  calculateSwingPoints
};
