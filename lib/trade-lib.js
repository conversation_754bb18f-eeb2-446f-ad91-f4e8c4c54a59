const { isEven, customRound } = require('./util');
const { mapSymbol } = require('./symbol-mapper.js');
const { orderSend, orderModify, orderClose, openedOrders, getQuote, symbolParams } = require('./mt5-service');
const {
  findBotById,
  findIndicatorValuesBySymbol,
  saveGridAi,
  findGridAiBySymbolUserIdAndAccountIdAndSymbol,
  findTickSizeValue,
  findGridAiByAccountNumberAndSymbol,
  findPositionAiByAccountNumberAndSymbol,
  findAccountNumberByAccountId,
  savePositionAi,
} = require('./supabaseService');
const {
  getPriceList,
  calculateTakeProfitImpl,
  calculateStopLossImpl,
  calculatePriceChangeImpl,
  calculatePriceChangeAmountImpl,
  priceIsBetween,
  calculateSwingPoints,
} = require('./util');
const NodeCache = require('node-cache');

const publishTrade = require('./lib/publisher');

// Constants
const DEFAULT_SLIPPAGE = 1000;
const CACHE_TTL = 5; // 5 seconds
const ORDER_TYPES = Object.freeze({
  BUY: 'Buy',
  SELL: 'Sell',
  BUY_STOP: 'BuyStop',
  SELL_STOP: 'SellStop',
  BUY_LIMIT: 'BuyLimit',
  SELL_LIMIT: 'SellLimit'
});

const POSITION_OPEN_STYLE = Object.freeze({
   NEW: 'New',
   INCREMENT: 'Increment',
});

const POSITION_OPEN_DIRECTION = Object.freeze({
   FAR: 'Far',
   NEAR: 'Near',
});

// Cache configuration
const cache = new NodeCache({
  stdTTL: CACHE_TTL,
  checkperiod: 120,
  useClones: false,
});

// Custom error class
class TradeError extends Error {
  constructor(message, code, originalError = null) {
    super(message);
    this.name = 'TradeError';
    this.code = code;
    this.originalError = originalError;
  }
}

/**
 * Validate required parameters
 * @param {Object} params - Parameters to validate
 * @param {Array} required - List of required parameter names
 * @throws {TradeError} If required parameters are missing
 */
const validateParams = (params, required) => {
  const missing = required.filter(param => params[param] === undefined || params[param] === null);
  if (missing.length > 0) {
    throw new TradeError(`Missing required parameters: ${missing.join(', ')}`, 'INVALID_PARAMS');
  }
};

/**
 * Helper functions for statistics calculations
 */
const getCountByType = (orders, orderType) => orders.filter(order => order.orderType === orderType).length;
const getCountBySymbol = (orders, symbol) => orders.filter(order => order.symbol === symbol).length;
const getUniqueSymbols = (orders) => [...new Set(orders.map(order => order.symbol))];

/**
 * Cache wrapper for expensive operations
 * @param {string} key - Cache key
 * @param {Function} fn - Function to execute if not cached
 * @param {number} ttl - Time to live in seconds
 * @returns {Promise} Cached or fresh result
 */
const withCache = async (key, fn, ttl = CACHE_TTL) => {
  const cached = cache.get(key);
  if (cached !== undefined) return cached;

  const result = await fn();
  cache.set(key, result, ttl);
  return result;
};

/**
 * Create standardized order model
 * @param {Object} finalModel - Order parameters
 * @returns {Object} Standardized order model
 */
const createOrderModel = (finalModel) => ({
  symbol: finalModel.symbol,
  actionType: finalModel.actionType,
  volume: finalModel.volume,
  openPrice: finalModel.openPrice,
  slippage: finalModel.slippage || DEFAULT_SLIPPAGE,
  stoploss: finalModel.stopLoss || 0,
  takeprofit: finalModel.takeProfit || 0,
  comment: finalModel.comment || '',
  expertID: finalModel.expertID || 0,
  stopLimitPrice: finalModel.stopLimitPrice || 0,
  placedType: finalModel.placedType || "Manually"
});

const _sendOrderImpl = async (finalModel, tradeAccountToken) => {
  validateParams({ ...finalModel, tradeAccountToken }, [
    'symbol',
    'actionType',
    'volume',
    'openPrice',
    'tradeAccountToken'
  ]);

  try {
    const orderModel = createOrderModel(finalModel);

    return await orderSend(
      tradeAccountToken,
      orderModel.symbol,
      orderModel.actionType,
      orderModel.volume,
      orderModel.openPrice,
      orderModel.slippage,
      orderModel.stoploss,
      orderModel.takeprofit,
      orderModel.comment,
      orderModel.expertID,
      orderModel.stopLimitPrice,
      orderModel.placedType
    );
  } catch (error) {
    console.error('Error sending order:', {
      error: error.message,
      model: finalModel,
      timestamp: new Date().toISOString()
    });
    throw new TradeError('Failed to send order', 'SEND_ORDER_ERROR', error);
  }
};

const _modifyOrderImpl = async (finalModel, tradeAccountToken) => {
  validateParams({ ...finalModel, tradeAccountToken }, ['ticket', 'tradeAccountToken']);

  try {
    return await orderModify(
      tradeAccountToken,
      finalModel.ticket,
      finalModel?.stopLoss,
      finalModel?.takeProfit
    );
  } catch (error) {
    console.error('Error modifying order:', {
      error: error.message,
      model: finalModel,
      timestamp: new Date().toISOString()
    });
    throw new TradeError('Failed to modify order', 'MODIFY_ORDER_ERROR', error);
  }
};

const _closeOrderImpl = async (finalModel, tradeAccountToken) => {
  validateParams({ ...finalModel, tradeAccountToken }, ['ticket', 'lots', 'tradeAccountToken']);

  try {
    return await orderClose(
      tradeAccountToken,
      finalModel.ticket,
      finalModel.lots,
      finalModel?.price,
      DEFAULT_SLIPPAGE
    );
  } catch (error) {
    console.error('Error closing order:', {
      error: error.message,
      model: finalModel,
      timestamp: new Date().toISOString()
    });
    throw new TradeError('Failed to close order', 'CLOSE_ORDER_ERROR', error);
  }
};

/**
 * Get open price with validation
 * @param {Object} orderModel - Order model
 * @param {Object} quote - Quote object
 * @returns {number} Open price
 */
const _getOpenPrice = (orderModel, quote) => {
  if (!quote) return 0;
  if (orderModel.openPrice !== 0) return orderModel.openPrice;
  
  const isBuyOrder = orderModel.actionType.toLowerCase().includes('buy');
  return isBuyOrder ? quote.ask : quote.bid;
};

/**
 * Update order with validation and error handling
 * @param {Object} params - Update parameters
 * @returns {Promise} Update result
 */
const _onUpdateOrder = async params => {
  validateParams(params, ['orderModel', 'tradeSymbol', 'openOrders', 'modifyOrder', 'tradeAccountToken']);

  try {
    const { orderModel, tradeSymbol, openOrders, modifyOrder, tradeAccountToken } = params;
    
    const symbolOrders = openOrders.filter(
      order => order.symbol === tradeSymbol.currency &&
      [ORDER_TYPES.BUY, ORDER_TYPES.SELL].includes(order.orderType)
    );

    const updatePromises = symbolOrders.map(order => {
      const finalModel = {
        ticket: order.ticket,
        takeProfit: orderModel.takeProfit === 0 ? order.takeProfit : orderModel.takeProfit,
        stopLoss: orderModel.stopLoss === 0 ? order.stopLoss : orderModel.stopLoss,
        positionId: order.id
      };

      return modifyOrder(
        tradeAccountToken,
        finalModel.ticket,
        finalModel.stopLoss,
        finalModel.takeProfit
      );
    });

    await Promise.all(updatePromises);
    return symbolOrders.length;
  } catch (error) {
    console.error('Error updating orders:', {
      error: error.message,
      params,
      timestamp: new Date().toISOString()
    });
    throw new TradeError('Failed to update orders', 'UPDATE_ORDER_ERROR', error);
  }
};

/**
 * Place quick pending trade with validation and error handling
 * @param {Object} params - Trade parameters
 * @returns {Promise} Trade result
 */
const _onPlaceQuickPendingTrade = async params => {
  validateParams(params, ['orderModel', 'tradeAccountToken', 'tradeQuote']);

  try {
    const { orderModel, tradeAccountToken, tradeQuote } = params;

    const finalModel = {
      symbol: orderModel.symbol,
      actionType: orderModel.type?.toLowerCase() === 'buy' ? ORDER_TYPES.BUY_STOP : ORDER_TYPES.SELL_STOP,
      takeProfit: orderModel.takeProfit,
      stopLoss: orderModel.stopLoss,
      volume: orderModel.volume
    };

    const currPrice = _getOpenPrice(orderModel, tradeQuote);
    const isBuyOrder = orderModel.type?.toLowerCase() === 'buy';

    finalModel.openPrice = isBuyOrder
      ? currPrice + orderModel.startDistance * orderModel.gridSize
      : currPrice - orderModel.startDistance * orderModel.gridSize;

    if (orderModel.useTakeProfit) {
      finalModel.takeProfit = isBuyOrder
        ? finalModel.openPrice + orderModel.takeProfit * orderModel.gridSize
        : finalModel.openPrice - orderModel.takeProfit * orderModel.gridSize;
    }

    if (orderModel.useStopLoss) {
      finalModel.stopLoss = isBuyOrder
        ? finalModel.openPrice - orderModel.stopLoss * orderModel.gridSize
        : finalModel.openPrice + orderModel.stopLoss * orderModel.gridSize;
    }

    const positionCount = orderModel.useDynamicMode ? orderModel.takeProfit : orderModel.positionCount;
    const orderPromises = [];

    for (let j = 0; j < positionCount; j++) {
      if (orderModel.useDynamicMode) {
        finalModel.takeProfit = isBuyOrder
          ? finalModel.openPrice + (j + 1) * orderModel.gridSize
          : finalModel.openPrice - (j + 1) * orderModel.gridSize;
      }
      orderPromises.push(_sendOrderImpl(finalModel, tradeAccountToken));
    }

    await Promise.all(orderPromises);
  } catch (error) {
    console.error('Error placing quick pending trade:', {
      error: error.message,
      params,
      timestamp: new Date().toISOString()
    });
    throw new TradeError('Failed to place quick pending trade', 'QUICK_PENDING_TRADE_ERROR', error);
  }
};

/**
 * Place quick trade with validation and error handling
 * @param {Object} params - Trade parameters
 * @returns {Promise} Trade result
 */
const _onPlaceQuickTrade = async params => {
  validateParams(params, ['orderModel', 'tradeAccountToken', 'tradeQuote']);

  try {
    const { orderModel, tradeAccountToken, tradeQuote } = params;

    const finalModel = {
      symbol: orderModel.symbol,
      actionType: orderModel.actionType,
      volume: orderModel.volume,
      openPrice: _getOpenPrice(orderModel, tradeQuote),
      takeProfit: orderModel.takeProfit,
      stopLoss: orderModel.stopLoss
    };

    await _sendOrderImpl(finalModel, tradeAccountToken);
  } catch (error) {
    console.error('Error placing quick trade:', {
      error: error.message,
      params,
      timestamp: new Date().toISOString()
    });
    throw new TradeError('Failed to place quick trade', 'QUICK_TRADE_ERROR', error);
  }
};

const _onPlaceOrder = async params => {
  const {
    orderModel,
    tradeAccount,
    tradeSymbol,
    tradeQuote,
    sendOrderImpl
  } = params;

  if (tradeAccount === undefined) {
    return;
  }
  try {
    const curr_price = _getOpenPrice(orderModel, tradeQuote);
    const distance =
      curr_price > orderModel.takeProfit
        ? curr_price - orderModel.takeProfit
        : orderModel.takeProfit - curr_price;
    const step = distance / orderModel.positionCount;
    const prices = [];
    const tpPrices = [];
    for (let i = 0; i < orderModel.positionCount; i++) {
      if (orderModel.actionType.toLowerCase().includes('buy')) {
        prices.push(curr_price + (i + 1) * step);
        tpPrices.push(curr_price + (i + 1) * (step * 0.98));
      } else {
        prices.push(curr_price - (i + 1) * step);
        tpPrices.push(curr_price - (i + 1) * (step * 0.98));
      }
    }

    const finalModel = {
      symbol: tradeSymbol.currency,
      actionType: orderModel.actionType,
      takeProfit: orderModel.takeProfit,
      stopLoss: orderModel.stopLoss,
      volume: orderModel.volume,
      price: 0
    };

    if (orderModel.executionStyle === 'pending') {
      finalModel['openPrice'] = _getOpenPrice(orderModel, tradeQuote);
      finalModel['price'] = finalModel.openPrice;
    }

    for (let j = 0; j < orderModel.positionCount; j++) {
      if (
        orderModel.tradingMode === 'normal' ||
        orderModel.executionStyle === 'instant'
      ) {
        if (orderModel.tpStyle === 'fixed') {
          sendOrderImpl(finalModel);
        } else if (orderModel.tpStyle === 'dynamic') {
          finalModel.takeProfit = tpPrices[j];
          sendOrderImpl(finalModel);
        }
      }

      if (orderModel.tradingMode === 'advanced') {
        finalModel.openPrice = j == 0 ? finalModel.openPrice : prices[j - 1];
        if (orderModel.tpStyle === 'fixed') {
          sendOrderImpl(finalModel);
        } else if (orderModel.tpStyle === 'dynamic') {
          finalModel.takeProfit = tpPrices[j];
          sendOrderImpl(finalModel);
        }
      }

      if (orderModel.tradingMode === 'grid') {
        finalModel.openPrice = j == 0 ? finalModel.openPrice : prices[j - 1];
        if (orderModel.tpStyle === 'fixed') {
          if (!isEven(j + 1)) {
            finalModel.takeProfit = prices[j];
            sendOrderImpl(finalModel);
          }
        } else if (orderModel.tpStyle === 'dynamic') {
          finalModel.takeProfit = prices[j];
          sendOrderImpl(finalModel);
        }
      }
      
    }
  } catch (error) {
    console.error(error);
  }
};

const getHedgedOrder = async params => {
  const { id, order } = params;

  const finalModel = {
    id,
    volume: order.lots,
    stopLoss: order.openPrice,
    slippage: 15000,
    actionType: order.orderType === 'Buy' ? 'Sell' : 'Buy',
    symbol: order.symbol,
    comment: 'HT-'+order.ticket,
    openPrice: 0,
    takeProfit: 0
  };

  const _finalModel = {
      symbol: finalModel.symbol,
      actionType: finalModel.actionType,
      volume: finalModel.volume,
      openPrice: finalModel.openPrice,
      slippage: 1000,
      stopLoss: finalModel?.stopLoss,
      takeProfit: finalModel?.takeProfit,
      comment: finalModel.comment,
      expertID: 0,
      stopLimitPrice: 0,
      placedType: 'Manually'
  };

  return _finalModel;
}



const secureTrades = async params => {
  const {
    id,
    bot_id
  } = params;

  try {
      const bot = await findBotById(bot_id);
      //const bot = data;
      //console.log('Bot found:', bot);
      const ordersData = await openedOrders(id);
      //console.log('Opened orders:', ordersData[0]);
      let trades = ordersData;
      //console.log('Securing trades:', trades.length);
      if(!trades || trades.length === 0) return;
      const symbolTrades = trades.filter(trade => trade.symbol === bot.symbol);
      //console.log('Securing trades for symbol:', bot.symbol, ':', symbolTrades.length);
      for(let trade of symbolTrades) {
          if(trade.profit > 0 && trade.stopLoss === 0) {
            if(bot.secure_grid_size === 0) bot.secure_grid_size = 1;
            let delta = bot.secure_grid_size * bot.grid_size;
            let sl = 0;
              if(trade.orderType === 'Buy') {
                  sl = trade.openPrice + delta;
              } else {
                  sl = trade.openPrice - delta;
              }
              try {
                //console.log('Open Price ', trade.openPrice, ' SL ', sl, 'TP ', trade.takeProfit, ' Ticket ', trade.ticket, ' Order Type ', trade.orderType);
                await orderModify(id, trade.ticket, sl, trade.takeProfit, 0);
              } catch (error) {
                  console.log('Error modifying order:', error.message);
              }
          }
      }
  } catch (error) {
    console.log('Error securing trades:', error.message);
  }

}


const openRangePendingOrder = async params => {
  const {symbol, orderType, openPrice, endPrice, positionCount, tradeAccountToken, volume} = params.order;
  let prices = getPriceList(openPrice, endPrice, positionCount);
  console.log('Prices ', prices);
  const requests = [];
  try {
      for(let price of prices) {
        requests.push(orderSend(tradeAccountToken, symbol, orderType, volume, price, 10000, 0, 0, '', 0, 0, 'Manually' ));
      }
      await Promise.all(requests);
  } catch (error) {
    console.error('Error sending order:', error.message);
  }
}


/**
 * Get tick parameters for an order
 * @param {Object} order - Order object
 * @param {string} tradeAccountToken - Trade account token
 * @returns {Promise<Object|null>} Tick parameters or null
 */
const getOrderTickParams = async (order, tradeAccountToken) => {
  // Try cached tick values first
  let tickParams = await getTickValueAndSize({ symbol: order.symbol });

  if (tickParams && tickParams.tickValue > 0 && tickParams.tickSize > 0) {
    return tickParams;
  }

  // Fallback to symbol params
  const symbolParamsData = await symbolParams(tradeAccountToken, order.symbol);
  if (!symbolParamsData?.symbolInfo) return null;

  const { symbolInfo } = symbolParamsData;
  return {
    tickValue: symbolInfo.tickValue,
    tickSize: symbolInfo.tickSize
  };
};

const updateOrderTakeProfitByTicket = async params => {
  const { ticket, tradeAccountToken, profit } = params;
  try {
    const openOrders = await openedOrders(tradeAccountToken);
    const order = openOrders.find(order => order.ticket === ticket);
    if (!order) return;

    const tickParams = await getOrderTickParams(order, tradeAccountToken);
    if (!tickParams) return;

    const quote = await getQuote(tradeAccountToken, order.symbol);
    if (!quote) return;

    const spread = Math.abs(quote.ask - quote.bid);
    const takeProfit = calculateTakeProfitImpl(
      order.openPrice,
      order.lots,
      tickParams.tickValue,
      tickParams.tickSize,
      profit,
      order.orderType,
      spread
    );

    await orderModify(tradeAccountToken, ticket, order.stopLoss, takeProfit, 0);
  } catch (error) {
    console.log('Error updating order take profit:', error.message);
  }
};


const updateOrderStopLossByTicket = async params => {
  const { ticket, tradeAccountToken, loss } = params;
  try {
    const openOrders = await openedOrders(tradeAccountToken);
    const order = openOrders.find(order => order.ticket === ticket);
    if (!order) return;

    const tickParams = await getOrderTickParams(order, tradeAccountToken);
    if (!tickParams) return;

    const quote = await getQuote(tradeAccountToken, order.symbol);
    if (!quote) return;

    const spread = Math.abs(quote.ask - quote.bid);
    const stopLoss = calculateStopLossImpl(
      order.openPrice,
      order.lots,
      tickParams.tickValue,
      tickParams.tickSize,
      loss,
      order.orderType,
      spread
    );

    await orderModify(tradeAccountToken, ticket, stopLoss, order.takeProfit, 0);
  } catch (error) {
    console.log('Error updating order stop loss:', error.message);
  }
};


const secureOrderTakeProfitByTicket = async params => {
  const { ticket, tradeAccountToken, profit } = params;
  try {
    const openOrders = await openedOrders(tradeAccountToken);
    const order = openOrders.find(order => order.ticket === ticket);
    if (!order) return;

    const quote = await getQuote(tradeAccountToken, order.symbol);
    if (!quote) return;

    const tickParams = await getOrderTickParams(order, tradeAccountToken);
    if (!tickParams || tickParams.tickValue === 0 || tickParams.tickSize === 0) return;

    const spread = Math.abs(quote.ask - quote.bid);
    const stopLoss = calculateTakeProfitImpl(
      order.openPrice,
      order.lots,
      tickParams.tickValue,
      tickParams.tickSize,
      profit,
      order.orderType,
      spread
    );

    if (isNaN(stopLoss)) return;
    await orderModify(tradeAccountToken, ticket, stopLoss, order.takeProfit, 0);
  } catch (error) {
    console.log('Error updating order take profit:', error.message);
  }
};


const updateOrderTakeProfitBySymbol = async params => {
  const {symbol, tradeAccountToken, profit, startRange, endRange, forceUpdate, orderType} = params;
  try {
    const openOrders = await openedOrders(tradeAccountToken);
    let orders = [];
    
    if(startRange === 0 || endRange === 0) {
      orders = openOrders.filter(order => order.symbol === symbol && (forceUpdate || order.takeProfit === 0) && order.orderType === orderType);
    } else {
      orders = openOrders.filter(order => order.symbol === symbol && priceIsBetween(order.openPrice, startRange, endRange) && (forceUpdate || order.takeProfit === 0) && 
      order.orderType === orderType);
    }
    const requests = [];
    for(let order of orders) {
      requests.push(updateOrderTakeProfitByTicket({ticket: order.ticket, tradeAccountToken, profit}).catch(error => {
        console.log('Error updating order take profit by ticket:', error.message);
      }));
    }
    await Promise.all(requests);
  } catch (error) {
    console.log('Error updating order take profit by symbol:', error.message);
    throw new Error('Error updating order take profit by symbol:', error.message);
  }
}


const updateOrderStopLossBySymbol = async params => {
  const {symbol, tradeAccountToken, loss, startRange, endRange, forceUpdate, orderType} = params;
  try {
    const openOrders = await openedOrders(tradeAccountToken);
    const orders = [];
    if(startRange === 0 || endRange === 0) {
      orders = openOrders.filter(order => order.symbol === symbol && (forceUpdate || order.stopLoss === 0) && order.orderType === orderType);
    } else {
      orders = openOrders.filter(order => order.symbol === symbol && priceIsBetween(order.openPrice, startRange, endRange) && (forceUpdate || order.stopLoss === 0) && 
      order.orderType === orderType);
    }
    const requests = [];
    for(let order of orders) {
      requests.push(updateOrderStopLossByTicket({ticket: order.ticket, tradeAccountToken, loss}));
    }
  } catch (error) {
    console.log('Error updating order stop loss by symbol:', error.message);
  }
} 


const secureOrderTakeProfitBySymbol = async params => {
  const {symbol, tradeAccountToken, profit, executionProfit, orderType} = params;
  try {
    const openOrders = await openedOrders(tradeAccountToken);
    const orders = openOrders.filter(order => order.symbol === symbol && (executionProfit > profit ?  order.profit >= executionProfit : order.profit >= profit) && order.orderType === orderType);
    const requests = [];
    for(let order of orders) {
      requests.push(secureOrderTakeProfitByTicket({ticket: order.ticket, tradeAccountToken, profit}));
    }
    await Promise.all(requests);
  } catch (error) {
    console.log('Error updating order take profit by symbol:', error.message);
  }
}


const placeOrderWithProfitTarget = async params => {
    const {symbol, tradeAccountToken, profit, loss, positionCount, positionsPerGrid, orderType, volume, tpStyle, comment, isQuick, useDynamicLot} = params;
    let quote = null;
    let tickValueAndSize = null;
    try {
      quote = await getQuote(tradeAccountToken, symbol);
      tickValueAndSize = await findTickSizeValue(symbol);
    } catch (error) {
      console.log('Error getting quote:', error.message);
      throw new Error('Failed to get quote or tick value');
    }

    if(!quote) throw new Error('Quote not available');
    if (!tickValueAndSize || tickValueAndSize.length === 0) throw new Error('Tick value and size not available');

    let openPrice = 0;
    if(orderType === 'Buy') {
      openPrice = quote.ask;
    } else {
      openPrice = quote.bid;
    }

    const { tick_value, tick_size } = tickValueAndSize[0];
    const spread = Math.abs(quote.ask - quote.bid);

    const requests = [];
    const results = {
      successful: [],
      failed: []
    };

    for(let i = 0; i < positionCount; i++) {
      let multiplier = tpStyle === 'fixed' ? 1 : i+1;
      let takeProfit = calculateTakeProfitImpl(openPrice, volume, tick_value, tick_size, profit, orderType, spread, multiplier);
      let stopLoss = loss > 0 ? calculateStopLossImpl(openPrice, volume, tick_value, tick_size, loss, orderType, spread) : 0;

      let finalTP = isQuick ? 0 : takeProfit;
      let finalSL = isQuick ? 0 : stopLoss;

      let _volume = volume;

      if(useDynamicLot){
        _volume *= (i+1);
      }
      
      for(let j = 0; j < positionsPerGrid; j++) {
        const orderRequest = {
          index: i * positionsPerGrid + j,
          price: openPrice,
          takeProfit: finalTP,
          stopLoss: finalSL,
          promise: orderSend(tradeAccountToken, symbol, orderType, _volume, 0, 10000, finalSL, finalTP, comment, 0, 0, 'Manually')
        };
        requests.push(orderRequest);
      }
    }

    await Promise.all(
      requests.map(async (request) => {
        try {
          const result = await request.promise;
          results.successful.push({
            index: request.index,
            price: request.price,
            takeProfit: request.takeProfit,
            stopLoss: request.stopLoss,
            result
          });
        } catch (error) {
          results.failed.push({
            index: request.index,
            price: request.price,
            takeProfit: request.takeProfit,
            stopLoss: request.stopLoss,
            error: error.message || 'Unknown error'
          });
        }
      })
    );

    return {
      ...results,
      message: results.failed.length === 0 ? 
        'All orders placed successfully' : 
        `${results.successful.length} orders placed successfully, ${results.failed.length} orders failed`
    };
}


const placePendingOrderWithProfitTarget = async params => {
  const {symbol, tradeAccountToken, profit, loss, spreadMultiplier, positionCount, positionsPerGrid, orderType, volume, useDynamicLot} = params;
  let quote = null;
  let tickValueAndSize = null;
  try {
    quote = await getQuote(tradeAccountToken, symbol);
    tickValueAndSize = await findTickSizeValue(symbol);
  } catch (error) {
    console.log('Error getting quote:', error.message);
    throw new Error('Error getting quote:', 'Unsupported symbol');
  }

  if (!quote) throw new Error('Error getting quote:', 'Network Error, Please try again later');
  if (!tickValueAndSize || tickValueAndSize.length === 0) throw new Error('Error getting indicator values:', 'Unsupported symbol');

  const { tick_value, tick_size } = tickValueAndSize[0];
  const spread = Math.abs(quote.ask - quote.bid);

  let openPrice = 0;
  if ((orderType === 'Buy' || orderType === 'BuyStop')) {
    openPrice = quote.ask + (spread * spreadMultiplier);
  } else if(openPrice === 0 && (orderType === 'Sell' || orderType === 'SellStop')) {
    openPrice = quote.bid - (spread * spreadMultiplier);
  }

  if (orderType === 'BuyLimit') {
    openPrice = quote.ask - (spread * spreadMultiplier);
  } else if (orderType === 'SellLimit') {
    openPrice = quote.bid + (spread * spreadMultiplier);
  }

  let tpPriceChange = await calculatePriceChange({tradeAccountToken, lotSize: volume, symbol, amount: profit}); 
  //calculateTakeProfitImpl(openPrice, volume, tick_value, tick_size, profit, orderType, spread);
  let slPriceChange = loss > 0 ? await calculatePriceChange({tradeAccountToken, lotSize: volume, symbol, amount: loss}) : 0; 
  //calculateStopLossImpl(openPrice, volume, tick_value, tick_size, loss, orderType, spread) : 0;


  let tp = orderType === 'Buy' || orderType === 'BuyStop' || orderType === 'BuyLimit' ? openPrice + tpPriceChange : openPrice - tpPriceChange;
  let sl = orderType === 'Sell' || orderType === 'SellStop' || orderType === 'SellLimit' ? openPrice + slPriceChange : openPrice - slPriceChange;

  const requests = [];
  for(let i = 0; i < positionCount; i++) {
    let _volume = volume;
    if (useDynamicLot) {
      _volume *= i + 1;
    }
    for(let j = 0; j < positionsPerGrid; j++) {
      requests.push(orderSend(tradeAccountToken, symbol, orderType, _volume, openPrice, 10000, sl, tp, '', 0, 0, 'Manually'));
    }
  }
  await Promise.all(requests);
}


const placeGapPendingOrderWithProfitTarget = async params => {
  const {symbol, tradeAccountToken, profit, loss, 
    positionCount, orderType, volume, gapAmount, positionsPerGrid, 
    isDynamic, openPrice, endPrice, comment, positionStyle, positionDirection, useDynamicLot, isSwingTrade} = params;

  if(isDynamic) return await placeDynamicGapPendingOrderWithProfitTarget(params);

  let _openPrice = openPrice;
  let _endPrice = endPrice;

  let quote = null;
  let openOrders = null;
  try {
    quote = await getQuote(tradeAccountToken, symbol);
    openOrders = await openedOrders(tradeAccountToken);
  } catch (error) {
    console.log('Error getting quote:', error.message);
    throw new Error('Error getting quote:', 'Unsupported symbol');
  }

  if (!quote) throw new Error('Error getting quote:', 'Network Error, Please try again later');

  const spread = Math.abs(quote.ask - quote.bid);

  if (isSwingTrade) {
    const swingPoints = calculateSwingPoints(_openPrice, _endPrice);
    _openPrice = swingPoints.level618;
    _endPrice = swingPoints.level796;
  }

  const gap = _endPrice === 0 ? await calculatePriceChange({tradeAccountToken, lotSize: volume, symbol, amount: gapAmount}) : Math.abs(_endPrice - _openPrice)/positionCount;
  const profitChange = await calculatePriceChange({tradeAccountToken, lotSize: volume, symbol, amount: profit});
  const lossChange = loss > 0 ? await calculatePriceChange({tradeAccountToken, lotSize: volume, symbol, amount: loss}) : 0;

  const distanceOrder = (positionDirection === POSITION_OPEN_DIRECTION.NEAR
    ? findClosestOrder(quote, openOrders, orderType)
    : findFarthestOrder(quote, openOrders, orderType));

    if(positionStyle === POSITION_OPEN_STYLE.INCREMENT && distanceOrder === null) {
      throw new Error('Error placing order:', 'You need an existing order to increment order');
    }

  const currentPrice = positionStyle === POSITION_OPEN_STYLE.NEW ? _openPrice : distanceOrder.openPrice;
  const isNear = positionDirection === POSITION_OPEN_DIRECTION.NEAR;

  let prices = [];
  let takeProfits = [];
  if (orderType === 'Buy' || orderType === 'BuyStop') {
    for(let i = 0; i < positionCount; i++) {
      let gapDelta = i===0 ? 1 : 0;
      let op = currentPrice === 0 ? quote.ask : currentPrice;
      let p = !isNear ? op - (gap * (i+gapDelta)) - spread : op - (gap * (i+gapDelta)) - spread;
      prices.push(p);
      takeProfits.push(p + profitChange);
    }
  } else if(orderType === 'Sell' || orderType === 'SellStop') {
    for(let i = 0; i < positionCount; i++) {
      let gapDelta = i===0 ? 1 : 0;
      let op = currentPrice === 0 ? quote.bid : currentPrice;
      let p = !isNear ? op - (gap * (i+gapDelta)) - spread : op + (gap * (i+gapDelta)) + spread;
      prices.push(p);
      takeProfits.push(p - profitChange);
    }
  }

  if (orderType === 'BuyLimit') {
    for(let i = 0; i < positionCount; i++) {
      let gapDelta = i===0 ? 1 : 0;
      let op = currentPrice === 0 ? quote.ask - spread : currentPrice;
      let p = !isNear ? op - (gap * (i+gapDelta)) : op - (gap * (i+gapDelta));
      prices.push(p);
      takeProfits.push(p + profitChange);
    }
  } else if(orderType === 'SellLimit') {
    for(let i = 0; i < positionCount; i++) {
      let gapDelta = i===0 ? 1 : 0;
      let op = currentPrice === 0 ? quote.bid + spread : currentPrice;
      let p = !isNear ? op - (gap * (i+gapDelta)) : op - (gap * (i+gapDelta));
      prices.push(p);
      takeProfits.push(p - profitChange);
    }
  }

  const requests = [];
  const results = {
    successful: [],
    failed: []
  };


  let _volume = volume;
  let _fixedTP = 0;

  for(let i = 0; i < positionCount; i++) {
    let stopLoss = 0;
    if(lossChange > 0 && (orderType === 'Buy' || orderType === 'BuyStop' || orderType === 'BuyLimit')) {
      stopLoss = prices[i] - lossChange;
    } else if(lossChange > 0 && (orderType === 'Sell' || orderType === 'SellStop' || orderType === 'SellLimit')) {
      stopLoss = prices[i] + lossChange;
    }

    if(useDynamicLot) {
      _volume = volume * (i+1);
    }

    if(isSwingTrade) {
      if(orderType === 'Buy' || orderType === 'BuyStop' || orderType === 'BuyLimit') {
          _fixedTP = Math.max(openPrice, endPrice);
      } else if(orderType === 'Sell' || orderType === 'SellStop' || orderType === 'SellLimit') {
          _fixedTP = Math.min(openPrice, endPrice);
      }
    }

    for(let j = 0; j < positionsPerGrid; j++) {
      const orderRequest = {
        index: i * positionsPerGrid + j,
        price: prices[i],
        takeProfit: takeProfits[i],
        stopLoss,
        promise: orderSend(tradeAccountToken, symbol, orderType, _volume, prices[i], 10000, stopLoss, isSwingTrade ? _fixedTP : takeProfits[i], comment, 0, 0, 'Manually')
      };
      requests.push(orderRequest);
    }
  }

  await Promise.all(
    requests.map(async (request) => {
      try {
        const result = await request.promise;
        results.successful.push({
          index: request.index,
          price: request.price,
          takeProfit: request.takeProfit,
          stopLoss: request.stopLoss,
          result
        });
      } catch (error) {
        results.failed.push({
          index: request.index,
          price: request.price,
          takeProfit: request.takeProfit,
          stopLoss: request.stopLoss,
          error: error.message || 'Unknown error'
        });
      }
    })
  );

  return results;
}


const placeDynamicGapPendingOrderWithProfitTarget = async params => {
  const {
    symbol,
    tradeAccountToken,
    profit,
    loss,
    positionCount,
    orderType,
    volume,
    gapAmount,
    positionsPerGrid,
    openPrice,
    endPrice,
    comment,
    positionStyle,
    positionDirection,
    useDynamicLot
  } = params;
  let quote = null;
  let openOrders = null;
  try {
    quote = await getQuote(tradeAccountToken, symbol);
    openOrders = await openedOrders(tradeAccountToken);
  } catch (error) {
    console.log('Error getting quote:', error.message);
    throw new Error('Error getting quote:', 'Unsupported symbol');
  }

  if (!quote) throw new Error('Error getting quote:', 'Network Error, Please try again later');

  let _openPrice = openPrice;
  let _endPrice = endPrice;

  if (isSwingTrade) {
    const swingPoints = calculateSwingPoints(_openPrice, _endPrice);
    _openPrice = swingPoints.level618;
    _endPrice = swingPoints.level796;
  }

  const spread = Math.abs(quote.ask - quote.bid);

  const gap =  _endPrice === 0 ? await calculatePriceChange({tradeAccountToken, lotSize: volume, symbol, amount: gapAmount}) : Math.abs(_endPrice - _openPrice)/positionCount;
  const profitChange = await calculatePriceChange({tradeAccountToken, lotSize: volume, symbol, amount: profit});
  const lossChange = loss > 0 ? await calculatePriceChange({tradeAccountToken, lotSize: volume, symbol, amount: loss}) : 0;
  let _positionCount = positionCount === 0 ? Math.round(profitChange / gap) : positionCount;

  const distanceOrder = (positionDirection === POSITION_OPEN_DIRECTION.NEAR
    ? findClosestOrder(quote, openOrders, orderType)
    : findFarthestOrder(quote, openOrders, orderType));

    if(positionStyle === POSITION_OPEN_STYLE.INCREMENT && distanceOrder === null) {
      throw new Error('Error placing order:', 'You need an existing order to increment order');
    }
  _openPrice = positionStyle === POSITION_OPEN_STYLE.NEW ? _openPrice : distanceOrder.openPrice;
  const isNear = positionDirection === POSITION_OPEN_DIRECTION.NEAR;

  let prices = [];
  let takeProfits = [];
  let currentPrice = _openPrice === 0 ? (orderType === 'BuyStop' || orderType === 'BuyLimit' ? quote.ask : quote.bid) : distanceOrder;
  
  if (orderType === 'Buy' || orderType === 'BuyStop') {
    for(let i = 0; i < _positionCount; i++) {
      let gapDelta = i===0 ? 1 : 0;
      let p = currentPrice - (gap * (i+gapDelta)) - spread;
      let tp = p + (profitChange  * i);
      prices.push(p);
      takeProfits.push(tp);
    }
  } else if(orderType === 'Sell' || orderType === 'SellStop') {
    for(let i = 0; i < _positionCount; i++) {
      let gapDelta = i===0 ? 1 : 0;
      let p = currentPrice  - (gap * (i+gapDelta)) - spread;
      let tp = p - (profitChange  * i);
      prices.push(p);
      takeProfits.push(tp);
    }
  }

  if (orderType === 'BuyLimit') {
    for(let i = 0; i < _positionCount; i++) {
      let gapDelta = i===0 ? 1 : 0;
      let p = currentPrice - (gap * (i+gapDelta)) - spread;
      let tp = p + (profitChange  * i);
      prices.push(p);
      takeProfits.push(tp);
    }
  } else if(orderType === 'SellLimit') {
    for(let i = 0; i < _positionCount; i++) {
      let gapDelta = i===0 ? 1 : 0;
      let p = currentPrice - (gap * (i+gapDelta)) - spread;
      let tp = p - (profitChange  * i);
      prices.push(p);
      takeProfits.push(tp);
    }
  }

  const requests = [];
  const results = {
    successful: [],
    failed: []
  };

  let _volume = volume;
  let _fixedTP = 0;

  for(let i = 0; i < _positionCount; i++) {
    let stopLoss = 0;
    if(lossChange > 0 && (orderType === 'Buy' || orderType === 'BuyStop' || orderType === 'BuyLimit')) {
      stopLoss = prices[i] - lossChange;
    } else if(lossChange > 0 && (orderType === 'Sell' || orderType === 'SellStop' || orderType === 'SellLimit')) {
      stopLoss = prices[i] + lossChange;
    }

    if(useDynamicLot) {
      _volume *= (i+1);
    }

    if(isSwingTrade) {
      if(orderType === 'Buy' || orderType === 'BuyStop' || orderType === 'BuyLimit') {
          _fixedTP = Math.max(openPrice, endPrice);
      } else if(orderType === 'Sell' || orderType === 'SellStop' || orderType === 'SellLimit') {
          _fixedTP = Math.min(openPrice, endPrice);
      }
    }

    for(let j = 0; j < positionsPerGrid; j++) {
      const orderRequest = {
        index: i * positionsPerGrid + j,
        price: prices[i],
        takeProfit: takeProfits[i],
        stopLoss,
        promise: orderSend(tradeAccountToken, symbol, orderType, _volume, prices[i], 10000, stopLoss, isSwingTrade ? _fixedTP : takeProfits[i], comment, 0, 0, 'Manually')
      };
      requests.push(orderRequest);
    }
  }

  await Promise.all(
    requests.map(async (request) => {
      try {
        const result = await request.promise;
        results.successful.push({
          index: request.index,
          price: request.price,
          takeProfit: request.takeProfit,
          stopLoss: request.stopLoss,
          result
        });
      } catch (error) {
        results.failed.push({
          index: request.index,
          price: request.price,
          takeProfit: request.takeProfit,
          stopLoss: request.stopLoss,
          error: error.message || 'Unknown error'
        });
      }
    })
  );

  return results;
}


const placeQuickGapPendingOrderWithProfitTarget = async params => {
  const {symbol, tradeAccountToken, profit, loss, positionCount, orderType, volume, gapAmount, positionsPerGrid, isDynamic, openPrice, endPrice, comment, useDynamicLot, isSwingTrade} = params;
  if(isDynamic) return await placeQuickDynamicGapPendingOrderWithProfitTarget(params);

  let quote = null;
  try {
    quote = await getQuote(tradeAccountToken, symbol);
  } catch (error) {
    console.log('Error getting quote:', error.message);
    throw new Error('Error getting quote:', 'Unsupported symbol');
  }

  if (!quote) throw new Error('Error getting quote:', 'Network Error, Please try again later');

  const spread = Math.abs(quote.ask - quote.bid);

  const gap = await calculatePriceChange({tradeAccountToken, lotSize: volume, symbol, amount: gapAmount});
  const profitChange = endPrice === 0 ? await calculatePriceChange({tradeAccountToken, lotSize: volume, symbol, amount: profit}) : Math.abs(endPrice - openPrice)/positionCount;
  const lossChange = loss > 0 ? await calculatePriceChange({tradeAccountToken, lotSize: volume, symbol, amount: loss}) : 0;
  const profitAmount = await calculatePriceChangeAmount({tradeAccountToken, lotSize: volume, symbol, priceChange: profitChange});
  let _comment = comment;
 

  let prices = [];
  if (orderType === 'Buy' || orderType === 'BuyStop') {
    for(let i = 0; i < positionCount; i++) {
      let gapDelta = i===0 ? 1 : 0;
      let op = openPrice === 0 ? quote.ask : openPrice;
      let p = op + spread + (gap * (i+gapDelta));
      prices.push(p);
    }
  } else if(orderType === 'Sell' || orderType === 'SellStop') {
    for(let i = 0; i < positionCount; i++) {
      let gapDelta = i===0 ? 1 : 0;
      let op = openPrice === 0 ? quote.bid : openPrice;
      let p = op - spread - (gap * (i+gapDelta));
      prices.push(p);
    }
  }

  if (orderType === 'BuyLimit') {
    for(let i = 0; i < positionCount; i++) {
      let gapDelta = i===0 ? 1 : 0;
      let op = openPrice === 0 ? quote.ask - spread : openPrice;
      let p = op - (gap * (i+gapDelta));
      prices.push(p);
    }
  } else if(orderType === 'SellLimit') {
    for(let i = 0; i < positionCount; i++) {
      let gapDelta = i===0 ? 1 : 0;
      let op = openPrice === 0 ? quote.bid + spread : openPrice;
      let p = op + (gap * (i+gapDelta));
      prices.push(p);
    }
  }

  const requests = [];
  const results = {
    successful: [],
    failed: []
  };

  let _volume = volume;

  for(let i = 0; i < positionCount; i++) {
    if (useDynamicLot) {
      _volume = volume * i;
    }
    
    if (useDynamicLot) {
       _comment = `QT-${profitAmount * (i + 1)}-0`;
    } else {
       _comment = `QT-${profitAmount}-0`;
    }

    for(let j = 0; j < positionsPerGrid; j++) {
      let stopLoss = 0;
      if(lossChange > 0 && (orderType === 'Buy' || orderType === 'BuyStop' || orderType === 'BuyLimit')) {
        stopLoss = prices[i] - lossChange;
      } else if(lossChange > 0 && (orderType === 'Sell' || orderType === 'SellStop' || orderType === 'SellLimit')) {
        stopLoss = prices[i] + lossChange;
      }
      
      const orderRequest = {
        index: i * positionsPerGrid + j,
        price: prices[i],
        takeProfit: 0,
        stopLoss,
        promise: orderSend(tradeAccountToken, symbol, orderType, _volume, prices[i], 10000, stopLoss, 0, _comment, 0, 0, 'Manually')
      };
      requests.push(orderRequest);
    }
  }

  await Promise.all(
    requests.map(async (request) => {
      try {
        const result = await request.promise;
        results.successful.push({
          index: request.index,
          price: request.price,
          takeProfit: request.takeProfit,
          stopLoss: request.stopLoss,
          result
        });
      } catch (error) {
        results.failed.push({
          index: request.index,
          price: request.price,
          takeProfit: request.takeProfit,
          stopLoss: request.stopLoss,
          error: error.message || 'Unknown error'
        });
      }
    })
  );

  return results;
}


const placeQuickDynamicGapPendingOrderWithProfitTarget = async params => {
  const {symbol, tradeAccountToken, profit, loss, positionCount, orderType, volume, gapAmount, positionsPerGrid, openPrice, endPrice, comment, useDynamicLot} = params;
  let quote = null;
  try {
    quote = await getQuote(tradeAccountToken, symbol);
  } catch (error) {
    console.log('Error getting quote:', error.message);
    throw new Error('Error getting quote:', 'Unsupported symbol');
  }

  if (!quote) throw new Error('Error getting quote:', 'Network Error, Please try again later');

  const spread = Math.abs(quote.ask - quote.bid);

  const gap = endPrice === 0 ? await calculatePriceChange({tradeAccountToken, lotSize: volume, symbol, amount: gapAmount}) : Math.abs(endPrice - openPrice)/positionCount;
  const lossChange = loss > 0 ? await calculatePriceChange({tradeAccountToken, lotSize: volume, symbol, amount: loss}) : 0;
  const profitChange = await calculatePriceChange({tradeAccountToken, lotSize: volume, symbol, amount: profit});
  let _positionCount = positionCount === 0 ? Math.round(profitChange / gap) : positionCount;

  let prices = [];
  let comments = [];
  let currentPrice = openPrice === 0 ? (orderType === 'BuyStop' || orderType === 'BuyLimit' ? quote.ask : quote.bid) : openPrice;
  
  if (orderType === 'Buy' || orderType === 'BuyStop') {
    for(let i = 0; i < _positionCount; i++) {
      let gapDelta = i===0 ? 1 : 0;
      let p = currentPrice + spread + (gap * gapDelta);
      let priceChange = spread + (gap * i);
      let profitAmount = await calculatePriceChangeAmount({tradeAccountToken, lotSize: volume, symbol, priceChange});
      let _comment = `QT-${Math.round(useDynamicLot ? profitAmount * (i+1) : profitAmount)}-0`;
      if(comment === '' || comment === null || comment === undefined) {
        comments.push(_comment);
      } else {
        comments.push(comment);
      }
      prices.push(p);
    }
  } else if(orderType === 'Sell' || orderType === 'SellStop') {
    for(let i = 0; i < _positionCount; i++) {
      let gapDelta = i===0 ? 1 : 0;
      let p = currentPrice  - spread - (gap * (i+gapDelta));
      let priceChange = spread + (gap * i);
      let profitAmount = await calculatePriceChangeAmount({tradeAccountToken, lotSize: volume, symbol, priceChange});
      let _comment = `QT-${Math.round(useDynamicLot ? profitAmount * (i+1) : profitAmount)}-0`;
      if(comment === '' || comment === null || comment === undefined) {
        comments.push(_comment);
      } else {
        comments.push(comment);
      }
      prices.push(p);
    }
  }

  if (orderType === 'BuyLimit') {
    for(let i = 0; i < _positionCount; i++) {
      let gapDelta = i===0 ? 1 : 0;
      let p = currentPrice - spread - (gap * gapDelta);
      let priceChange = spread + (gap * i);
      let profitAmount = await calculatePriceChangeAmount({tradeAccountToken, lotSize: volume, symbol, priceChange});
      let _comment = `QT-${Math.round(useDynamicLot ? profitAmount * (i+1) : profitAmount)}-0`;
      if(comment === '' || comment === null || comment === undefined) {
        comments.push(_comment);
      } else {
        comments.push(comment);
      }
      prices.push(p);
    }
  } else if(orderType === 'SellLimit') {
    for(let i = 0; i < _positionCount; i++) {
      let gapDelta = i===0 ? 1 : 0;
      let p = currentPrice + spread + (gap * gapDelta);
      let priceChange = spread + (gap * i);
      let profitAmount = await calculatePriceChangeAmount({tradeAccountToken, lotSize: volume, symbol, priceChange});
      let _comment = `QT-${Math.round(useDynamicLot ? profitAmount * (i+1) : profitAmount)}-0`;
      if(comment === '' || comment === null || comment === undefined) {
        comments.push(_comment);
      } else {
        comments.push(comment);
      }
      prices.push(p);
    }
  }

  const requests = [];
  const results = {
    successful: [],
    failed: []
  };

  for(let i = 0; i < _positionCount; i++) {
    if (useDynamicLot) {
      volume *= (i+1);
    }
    for(let j = 0; j < positionsPerGrid; j++) {
      let stopLoss = 0;
      if(lossChange > 0 && (orderType === 'Buy' || orderType === 'BuyStop' || orderType === 'BuyLimit')) {
        stopLoss = prices[i] - lossChange;
      } else if(lossChange > 0 && (orderType === 'Sell' || orderType === 'SellStop' || orderType === 'SellLimit')) {
        stopLoss = prices[i] + lossChange;
      }
      
      const orderRequest = {
        index: i * positionsPerGrid + j,
        price: prices[i],
        takeProfit: 0,
        stopLoss,
        promise: orderSend(tradeAccountToken, symbol, orderType, volume, prices[i], 10000, stopLoss, 0, comments[i], 0, 0, 'Manually')
      };
      requests.push(orderRequest);
    }
  }

  await Promise.all(
    requests.map(async (request) => {
      try {
        const result = await request.promise;
        results.successful.push({
          index: request.index,
          price: request.price,
          takeProfit: request.takeProfit,
          stopLoss: request.stopLoss,
          result
        });
      } catch (error) {
        results.failed.push({
          index: request.index,
          price: request.price,
          takeProfit: request.takeProfit,
          stopLoss: request.stopLoss,
          error: error.message || 'Unknown error'
        });
      }
    })
  );

  return results;
}


const placeAdvancePendingOrderWithProfitTarget = async params => {
  const {symbol, tradeAccountToken, profitStart, profitEnd, loss, positionCount, orderType, volume, gapAmount, positionsPerGrid, comment} = params;
  let quote = null;
  try {
    quote = await getQuote(tradeAccountToken, symbol);
  } catch (error) {
    console.log('Error getting quote:', error.message);
    throw new Error('Error getting quote:', 'Unsupported symbol');
  }

  if (!quote) throw new Error('Error getting quote:', 'Network Error, Please try again later');

  const spread = Math.abs(quote.ask - quote.bid);

  const gap = await calculatePriceChange({tradeAccountToken, lotSize: volume, symbol, amount: gapAmount});
  const profitChange = await calculatePriceChange({tradeAccountToken, lotSize: volume, symbol, amount: profitStart});
  let stopLossChange = loss > 0 ? await calculatePriceChange({tradeAccountToken, lotSize: volume, symbol, amount: loss}) : 0;
  let _positionCount = positionCount === 0 ? Math.round(profitChange / gap) : positionCount;
  let profitGap = Math.abs(profitEnd - profitStart)/positionsPerGrid;
  const profitGapChange = await calculatePriceChange({tradeAccountToken, lotSize: volume, symbol, amount: profitGap});

  let prices = [];
  let currentPrice = (orderType === 'BuyStop' || orderType === 'BuyLimit' ? quote.ask : quote.bid);
  
  if (orderType === 'Buy' || orderType === 'BuyStop') {
    for(let i = 1; i < _positionCount; i++) {
      let p = currentPrice + spread + (gap * i);
      prices.push(p);
    }
  } else if(orderType === 'Sell' || orderType === 'SellStop') {
    for(let i = 1; i < _positionCount; i++) {
      let p = currentPrice - spread - (gap * i);
      prices.push(p);
    }
  }

  if (orderType === 'BuyLimit') {
    for(let i = 1; i < _positionCount; i++) {
      let p = currentPrice - spread - (gap * i);
      prices.push(p);
    }
  } else if(orderType === 'SellLimit') {
    for(let i = 1; i < _positionCount; i++) {
      let p = currentPrice + spread + (gap * i);
      prices.push(p);
    }
  }

  const requests = [];
  const results = {
    successful: [],
    failed: []
  };

  for(let i = 0; i < _positionCount; i++) {
    for(let j = 0; j < positionsPerGrid; j++) {
      let takeProfit = 0;
      let stopLoss = 0;
      if(stopLossChange > 0 && (orderType === 'Buy' || orderType === 'BuyStop' || orderType === 'BuyLimit')) {
        takeProfit = prices[i] + profitChange + (profitGapChange * (i));
        stopLoss = prices[i] - stopLossChange;
      } else if(stopLossChange > 0 && (orderType === 'Sell' || orderType === 'SellStop' || orderType === 'SellLimit')) {
        takeProfit = prices[i] - profitChange - (profitGapChange * (i));
        stopLoss = prices[i] + stopLossChange;
      }
      
      const orderRequest = {
        index: i * positionsPerGrid + j,
        price: prices[i],
        takeProfit,
        stopLoss,
        promise: orderSend(tradeAccountToken, symbol, orderType, volume, prices[i], 10000, stopLoss, takeProfit, comment, 0, 0, 'Manually')
      };
      requests.push(orderRequest);
    }
  }

  await Promise.all(
    requests.map(async (request) => {
      try {
        const result = await request.promise;
        results.successful.push({
          index: request.index,
          price: request.price,
          takeProfit: request.takeProfit,
          stopLoss: request.stopLoss,
          result
        });
      } catch (error) {
        results.failed.push({
          index: request.index,
          price: request.price,
          takeProfit: request.takeProfit,
          stopLoss: request.stopLoss,
          error: error.message || 'Unknown error'
        });
      }
    })
  );

  return results;
}

const findFarthestOrder = (tradeQuote, openOrders, orderType) => {
  let pendingOrders = openOrders.filter(isPendingOrder);
  let typeOrder = pendingOrders.filter(order => order.orderType === orderType);
  const currentPrice = orderType.includes('Buy')
    ? tradeQuote.ask
    : tradeQuote.bid;
  const distances = typeOrder.map(order =>
    Math.abs(order.openPrice - currentPrice)
  );
  return typeOrder[distances.indexOf(Math.max(...distances))];
};

const findClosestOrder = (tradeQuote, openOrders, orderType) => {
  let pendingOrders = openOrders.filter(isPendingOrder);
  let typeOrder = pendingOrders.filter(order => order.orderType === orderType);
  const currentPrice = orderType.includes('Buy')
    ? tradeQuote.ask
    : tradeQuote.bid;
  const distances = typeOrder.map(order =>
    Math.abs(order.openPrice - currentPrice)
  );
  return typeOrder[distances.indexOf(Math.min(...distances))];
};


/**
 * Check if order is a pending order
 * @param {Object} order - Order object
 * @returns {boolean} True if pending order
 */
const isPendingOrder = (order) => {
  const pendingTypes = [ORDER_TYPES.BUY_STOP, ORDER_TYPES.SELL_STOP, ORDER_TYPES.BUY_LIMIT, ORDER_TYPES.SELL_LIMIT];
  return pendingTypes.includes(order.orderType);
};



const calculateTakeProfit = async params => {
  const { tradeAccountToken, symbol, lotSize, profitTarget, orderType} = params;
  const quote = await getQuote(tradeAccountToken, symbol);
  if(!quote) return;
  const spread = Math.abs(quote.ask - quote.bid);
  const openPrice = orderType === 'Buy' ? quote.ask : quote.bid;
  const symbol_params = await symbolParams(tradeAccountToken, symbol);
  if(!symbol_params) return;
  const { symbolInfo } = symbol_params;
  if(!symbolInfo) return;
  const { tickSize, tickValue } = symbolInfo;
  if(tickValue === 0 || tickSize === 0) {
    const tick_params = await getTickValueAndSize({symbol: symbol});
    if(tick_params === null) return null;
    const {tickValue, tickSize} = tick_params;
    return calculateTakeProfitImpl(openPrice, lotSize, tickValue, tickSize, profitTarget, orderType, spread);
  }
  return calculateTakeProfitImpl(openPrice, lotSize, tickValue, tickSize, profitTarget, orderType, spread);
}


const calculateStopLoss = async params => {
  const { tradeAccountToken, symbol, lotSize, lossTarget, orderType} = params;
  const quote = await getQuote(tradeAccountToken, symbol);
  if(!quote) return;
  const spread = Math.abs(quote.ask - quote.bid);
  const openPrice = orderType === 'Buy' ? quote.ask : quote.bid;
  const symbol_params = await symbolParams(tradeAccountToken, symbol);
  if(!symbol_params) return;
  const { symbolInfo } = symbol_params;
  if(!symbolInfo) return;
  const { tickSize, tickValue } = symbolInfo;
  if(tickValue === 0 || tickSize === 0) {
    const tick_params = await getTickValueAndSize({symbol: symbol});
    if(tick_params === null) return null;
    const {tickValue, tickSize} = tick_params;
    return calculateStopLossImpl(openPrice, lotSize, tickValue, tickSize, lossTarget, orderType, spread);
  }
  return calculateStopLossImpl(openPrice, lotSize, tickValue, tickSize, lossTarget, orderType, spread);
}


const calculatePriceChange = async params => {
  const { lotSize, symbol, amount } = params;
  const tick_params = await getTickValueAndSize({ symbol });
  if (tick_params === null) return null;
  const { tickValue, tickSize } = tick_params;
  return calculatePriceChangeImpl(lotSize, tickValue, tickSize, amount);
};


const calculateStopLevel = async params => {
  const { symbol } = params;
  const stopLevelParams = await getStopLevels({symbol});
  if (stopLevelParams === null || stopLevelParams === 0) return null;
  const { stopLevel } = stopLevelParams;
  return stopLevel;
}

const calculatePriceChangeAmount = async params => {
  const { tradeAccountToken, lotSize, symbol, priceChange } = params;

  const tick_params = await getTickValueAndSize({symbol});
  if(tick_params === null) return null;
  let {tickValue, tickSize} = tick_params;
  let priceChangeAmount = calculatePriceChangeAmountImpl(priceChange, tickSize, tickValue, lotSize);
  if (priceChangeAmount !== null) return priceChangeAmount;

  const symbol_params = await symbolParams(tradeAccountToken, symbol);
  console.log('symbol_params ', symbol_params);
  if(symbol_params === null) return null;
  const { symbolInfo } = symbol_params;
  return calculatePriceChangeAmountImpl(priceChange, symbolInfo.tickSize, symbolInfo.tickValue, lotSize);
}


const calculateSpreadAmount = async params => {
  const { tradeAccountToken, symbol, lotSize } = params;
  let quote = null;
  try {
    quote = await getQuote(tradeAccountToken, symbol);
  } catch (error) {
    console.log('Error getting quote:', error.message);
    return 0;
  }
  if(quote === null) return 0;
  const spread = Math.abs(quote.ask - quote.bid);
  return calculatePriceChangeAmount({tradeAccountToken, symbol, lotSize, priceChange: spread});
}


const calculateStopLevelAmount = async params => {
  const { tradeAccountToken, symbol, lotSize } = params;
  let stopLevel = null;
  try {
    stopLevel  = await calculateStopLevel({tradeAccountToken, symbol});
  } catch (error) {
    console.log('Error getting quote:', error.message);
    return 0;
  }
  if(stopLevel === null || stopLevel === 0) return 0;
  // stopLevel = stopLevel/=100;
  return calculatePriceChangeAmount({tradeAccountToken, symbol, lotSize, priceChange: stopLevel});
}


/**
 * Get tick value and size with caching
 * @param {Object} params - Parameters containing symbol
 * @returns {Promise<Object|null>} Tick value and size or null
 */
const getTickValueAndSize = async params => {
  const { symbol } = params;
  const cacheKey = `tick_${symbol}`;

  return withCache(cacheKey, async () => {
    const tickValueAndSize = await findTickSizeValue(symbol);
    if (!tickValueAndSize || tickValueAndSize.length === 0) return null;
    const { tick_value, tick_size } = tickValueAndSize[0];
    return { tickValue: tick_value, tickSize: tick_size };
  }, 300); // Cache for 5 minutes since tick values rarely change
};


const getStopLevels = async params => {
  const {symbol} = params;
  const indicatorValues = await findIndicatorValuesBySymbol(symbol);
  if(!indicatorValues || indicatorValues.length === 0) return null;
  const {minimum_tpsl_distance} = indicatorValues[0];
  return { stopLevel: minimum_tpsl_distance };
}


/**
 * Filter orders by type and profit/loss conditions
 * @param {Array} orders - Array of orders
 * @param {string} orderType - Order type filter
 * @returns {Array} Filtered orders
 */
const filterOrdersByType = (orders, orderType) => {
  const type = orderType?.toLowerCase();

  const filterMap = {
    'all': () => true,
    'buy': order => order.orderType === ORDER_TYPES.BUY,
    'sell': order => order.orderType === ORDER_TYPES.SELL,
    'buylimit': order => order.orderType === ORDER_TYPES.BUY_LIMIT,
    'selllimit': order => order.orderType === ORDER_TYPES.SELL_LIMIT,
    'buystop': order => order.orderType === ORDER_TYPES.BUY_STOP,
    'sellstop': order => order.orderType === ORDER_TYPES.SELL_STOP,
    'buy_profit': order => order.orderType === ORDER_TYPES.BUY && order.profit > 0,
    'sell_profit': order => order.orderType === ORDER_TYPES.SELL && order.profit > 0,
    'buy_loss': order => order.orderType === ORDER_TYPES.BUY && order.profit < 0,
    'sell_loss': order => order.orderType === ORDER_TYPES.SELL && order.profit < 0
  };

  const filterFn = filterMap[type] || filterMap['all'];
  return orders.filter(filterFn);
};

const closeTrades = async params => {
  validateParams(params, ['tradeAccountToken', 'symbol']);
  const { tradeAccountToken, symbol, orderType, startRange, endRange, maxProfit, maxLoss, positionCount, withoutSL } = params;

  try {
    let openOrders = await openedOrders(tradeAccountToken);
    const _maxLoss = maxLoss * -1;

    // Apply all filters in a single pass for better performance
    const filteredOrders = openOrders.filter(order => {
      // Filter by symbol
      if (order.symbol !== symbol) return false;

      // Filter by profit/loss
      if (maxProfit > 0 && order.profit >= maxProfit) return false;
      if (maxLoss > 0 && order.profit <= _maxLoss) return false;

      // Filter by price range
      if (startRange > 0 && endRange > 0 && !priceIsBetween(order.openPrice, startRange, endRange)) return false;

      // Filter by stop loss requirement
      if (withoutSL && order.stopLoss !== 0) return false;

      return true;
    });

    // Filter by order type
    let typeOrders = filterOrdersByType(filteredOrders, orderType);

    // Limit position count
    if (positionCount > 0) {
      typeOrders = typeOrders.slice(0, Math.min(typeOrders.length, positionCount));
    }
    
    // Close orders in parallel
    const closePromises = typeOrders.map(order => 
      orderClose(tradeAccountToken, order.ticket, order.lots, 0, DEFAULT_SLIPPAGE)
        .catch(error => {
          console.error(`Failed to close order ${order.ticket}:`, error);
          return null;
        })
    );

    const results = await Promise.all(closePromises);
    const closedOrders = results.filter(result => result !== null);
    
    return {
      closedCount: closedOrders.length,
      totalCount: typeOrders.length,
      failedCount: typeOrders.length - closedOrders.length
    };
  } catch (error) {
    console.error('Error closing trades:', {
      error: error.message,
      params,
      timestamp: new Date().toISOString()
    });
    throw new TradeError('Failed to close trades', 'CLOSE_TRADES_ERROR', error);
  }
};


const closeTradesByTickets = async (tradeAccountToken, tickets) => {
  const openOrders = await openedOrders(tradeAccountToken);
  const filteredTicketsOrders = openOrders.filter(order => tickets.includes(order.ticket));
  const closePromises = filteredTicketsOrders.map(order =>
    orderClose(tradeAccountToken, order.ticket, order.lots, 0, DEFAULT_SLIPPAGE).catch(
      error => {
        console.error(`Failed to close order ${order.ticket}:`, error);
        return null;
      }
    )
  );

  const results = await Promise.all(closePromises);
  const closedOrders = results.filter(result => result !== null);
  
  return {
    closedCount: closedOrders.length,
    totalCount: tickets.length,
    failedCount: tickets.length - closedOrders.length,
    message: `${closedOrders.length} orders closed successfully, ${tickets.length - closedOrders.length} orders failed`
  };
};

/**
 * Get trade statistics with validation
 * @param {Object} params - Parameters for statistics
 * @returns {Object} Trade statistics
 */
const getTradeStatistics = async params => {
  validateParams(params, ['openOrders']);
  const { openOrders } = params;

  try {
    const statistics = {
      totalTrades: openOrders.length,
      buyTrades: getCountByType(openOrders, ORDER_TYPES.BUY),
      sellTrades: getCountByType(openOrders, ORDER_TYPES.SELL),
      totalProfit: await calculateTotalProfit(openOrders),
      uniqueSymbols: getUniqueSymbols(openOrders),
      symbolCounts: {},
      profitBySymbol: {}
    };

    for (const symbol of statistics.uniqueSymbols) {
      statistics.symbolCounts[symbol] = getCountBySymbol(openOrders, symbol);
      const symbolOrders = openOrders.filter(order => order.symbol === symbol);
      statistics.profitBySymbol[symbol] = await calculateTotalProfit(symbolOrders);
    }

    return statistics;
  } catch (error) {
    console.error('Error getting trade statistics:', {
      error: error.message,
      params,
      timestamp: new Date().toISOString()
    });
    throw new TradeError('Failed to get trade statistics', 'STATISTICS_ERROR', error);
  }
};

const calculateTotalProfit = async openOrders => {
  let totalProfit = 0;
  for (let order of openOrders) {
    totalProfit += order.profit - (order.swap + order.commission);
  }
  return totalProfit;
};

/**
 * Calculate grid AI prices
 * @param {Object} params - Grid AI parameters
 * @returns {Promise<Array>} Grid prices
 */
const calculateGridAiPrices = async params => {
  validateParams(params, ['symbol', 'tradeAccountToken', 'start_price', 'gap_amount', 'volume', 'position_count', 'order_type']);
  const { symbol, tradeAccountToken, start_price, end_price, gap_amount, volume, position_count, order_type } = params;

  try {
    const gap = end_price > 0 ? Math.abs(end_price - start_price)/position_count : await calculatePriceChange({
      tradeAccountToken,
      lotSize: volume,
      symbol: mapSymbol(symbol),
      amount: gap_amount
    });

    // console.log('gap ', gap);

    const grid_count = position_count;
    let start = start_price;
    const grid_prices = [];

    for (let i = 0; i < grid_count; i++) {
      const price = order_type.includes('Buy') ? (start - (gap * i)) : order_type.includes('Sell') ? (start - (gap * i)) : null;
      if (price) grid_prices.push(price);
    }

    return grid_prices;
  } catch (error) {
    console.error('Error calculating grid AI prices:', {
      error: error.message,
      params,
      timestamp: new Date().toISOString()
    });
    throw new TradeError('Failed to calculate grid AI prices', 'GRID_PRICE_ERROR', error);
  }
};


const calculateUnrealizedProfit = async params => {
  const { tradeAccountToken } = params;
  const openOrders = await openedOrders(tradeAccountToken);
  const orderWithTP = openOrders.filter(order => order.takeProfit !== 0 && (order.orderType === 'Buy' || order.orderType === 'Sell'));
  let totalProfit = 0;
  for(let order of orderWithTP) {
    const priceChange = Math.abs(order.takeProfit - order.openPrice);
    const profit = await calculatePriceChangeAmount({ tradeAccountToken, symbol: order.symbol, lotSize: order.lots, priceChange });
    if(profit !== null) {
      totalProfit += profit;
    }
  }
  return totalProfit;
};


const calculateUnrealizedProfitBySymbol = async params => {
  const { tradeAccountToken, symbol } = params;
  const openOrders = await openedOrders(tradeAccountToken);
  const orderWithTP = openOrders.filter(order => order.takeProfit !== 0 && (order.orderType === 'Buy' || order.orderType === 'Sell') && order.symbol === symbol);
  let totalProfit = 0;
  for(let order of orderWithTP) {
    const priceChange = Math.abs(order.takeProfit - order.openPrice);
    const profit = await calculatePriceChangeAmount({ tradeAccountToken, symbol, lotSize: order.lots, priceChange });
    if(profit !== null) {
      totalProfit += profit;
    }
  }
  return totalProfit;
};


const calculatePnL = async params => {
  const { tradeAccountToken } = params;
  const openOrders = await openedOrders(tradeAccountToken);
  let totalProfit = 0;
  totalProfit = openOrders.reduce((acc, order) => {
    return order.profit + acc;
  }, 0);
  return totalProfit;
};

/**
 * Create grid AI
 * @param {Object} params - Grid AI parameters
 * @returns {Promise<Object>} Grid AI configuration
 */
const createGridAi = async params => {
  validateParams(params, [
    'user_id', 'account_id', 'symbol', 'take_profit',
    'volume', 'start_price', 'end_price',
    'gap_amount', 'positions_per_grid', 'order_types',
  ]);

  params.order_type = params.order_types[0];

  try {
    const prices = await calculateGridAiPrices(params);
    if(!prices || prices.length === 0) throw new Error('Failed to calculate prices');
    const {
      id, user_id, account_id, symbol, take_profit, trade_mode,
      is_active, volume, start_price, end_price, gap_amount,
      positions_per_grid, stop_loss, secure_trades, secure_amount,
      execution_amount, order_types, grid_type
    } = params;

    let final_params = {
      id,
      user_id,
      account_id,
      symbol,
      gap_amount,
      is_active,
      take_profit,
      stop_loss,
      trade_mode,
      volume,
      start_price: prices[0],
      end_price: prices[prices.length - 1],
      positions_per_grid,
      secure_trades,
      secure_amount,
      execution_amount,
      prices,
      order_types,
      grid_type,
      position_count: prices.length
    };

    if(final_params.grid_type === 'MAIN') {
      let oldGridAi = await findGridAiBySymbolUserIdAndAccountIdAndSymbol(user_id, account_id, symbol);
      if(oldGridAi && oldGridAi.id) {
        final_params.id = oldGridAi.id;
      }
    }

    if(final_params.id === null) delete final_params.id;

    // console.log('Grid Ai Params: ', final_params);
    await saveGridAi(final_params);
    let returnedGridAi = await findGridAiBySymbolUserIdAndAccountIdAndSymbol(user_id, account_id, symbol);
    return returnedGridAi;
  } catch (error) {
    console.error('Error creating grid AI:', {
      error: error.message,
      params,
      timestamp: new Date().toISOString()
    });
    throw new TradeError('Failed to create grid AI', 'GRID_CREATE_ERROR', error);
  }
};


const updateGridAi = async params => {
  await saveGridAi(params);
  return params;
};

/**
 * Missing helper functions that are referenced in the code
 */
const getTradeBrokerAccountByLogin = async (accountId) => {
  // This function should be implemented based on your database schema
  // For now, returning empty array to prevent errors
  console.warn('getTradeBrokerAccountByLogin not implemented');
  return [];
};

const findGridAiById = async (id) => {
  // This function should be implemented based on your database schema
  // For now, returning null to prevent errors
  console.warn('findGridAiById not implemented');
  return null;
};


/**
 * Create grid AI
 * @param {Object} params - Grid AI parameters
 * @returns {Promise<Object>} Grid AI configuration
 */
const createHedgeGridAi = async params => {
  validateParams(params, [
    'user_id', 'account_id', 'symbol', 'take_profit',
    'volume',  'gap_amount', 'positions_per_grid', 'position_count',
    'startBuyPrice', 'endBuyPrice', 'startSellPrice', 'endSellPrice',
  ]);

  try {

    const {
      account_id, symbol, volume, gap_amount, tradeAccountToken, full_grid,
      position_count, startBuyPrice, endBuyPrice, startSellPrice, endSellPrice
    } = params;

    const {buy, sell} = params;

    let quote = await getQuote(tradeAccountToken, symbol);
    if(!quote) throw new Error('Failed to get quote');

    let start_buy_price = startBuyPrice;//quote.ask;
    let start_sell_price = startSellPrice;//quote.bid;

    const buyPrices = full_grid ? [] :
     await calculateGridAiPrices({symbol, tradeAccountToken, start_price: start_buy_price, end_price: endBuyPrice, gap_amount, volume, position_count, order_type: 'BuyStop'});
    
    const sellPrices = full_grid ? [] : 
    await calculateGridAiPrices({symbol, tradeAccountToken, start_price: start_sell_price, end_price: endSellPrice, gap_amount, volume, position_count, order_type: 'SellStop'});

    if(!full_grid && (!buyPrices || buyPrices.length === 0)) throw new Error('Failed to calculate buy prices');
    if(!full_grid && (!sellPrices || sellPrices.length === 0)) throw new Error('Failed to calculate sell prices');

    const requests = [];
    const buyOrderTypes = ['BuyStop'];
    const sellOrderTypes = ['SellStop'];
    if(buy) {
      requests.push(await placeGridAIOrderImpl(params, buyOrderTypes, buyPrices));
    }
    if(sell) {
      requests.push(await placeGridAIOrderImpl(params, sellOrderTypes, sellPrices))
    }

    await Promise.all(requests);

    // Publish all the gridAi available on this symbol
    const trade_accounts = await getTradeBrokerAccountByLogin(account_id);
    if(trade_accounts.length > 0) {
      const account_number = trade_accounts[0].login;
      const gridAis = await getGridAiByAccountNumberAndSymbol(account_number, symbol);
      if(gridAis.length > 0) {
       const evt = { symbol, account_number, data: gridAis };
       publishTrade(evt);
      }
    }
    
  } catch (error) {
    console.error('Error creating grid AI:', {
      error: error.message,
      error: error.message,
      params,
      timestamp: new Date().toISOString()
    });
    throw new TradeError('Failed to create grid AI '+error.message, 'GRID_HEDGE_CREATE_ERROR', error);
  }
};


const placeGridAIOrderImpl = async (params, order_types, prices) => {
  const {
      user_id, account_id, symbol, take_profit, trade_mode,
      is_active, volume, gap_amount, positions_per_grid, stop_loss, secure_trades, secure_amount, take_profit_price, stop_loss_price,
      execution_amount, grid_type, close_unsecured_positions, auto_secure, full_grid, position_count, entry_interval,
  } = params;

  const accountNumber = await findAccountNumberByAccountId(account_id);
  if(!accountNumber || accountNumber.length === 0) throw new Error('Failed to find account number');

  const final_params = {
    user_id,
    account_id,
    account_number: accountNumber[0].login,
    symbol,
    gap_amount,
    is_active,
    take_profit,
    stop_loss,
    trade_mode,
    volume,
    start_price: prices[0],
    end_price: prices[prices.length - 1],
    positions_per_grid,
    secure_trades,
    secure_amount,
    execution_amount,
    prices,
    order_types,
    grid_type,
    position_count,
    close_unsecured_positions,
    auto_secure,
    full_grid,
    entry_interval,
    take_profit_price,
    stop_loss_price,
  };
    await saveGridAi(final_params);
}


const disableGridAiById = async (id) => {
  const grid_ai = await findGridAiById(id);
  if(!grid_ai) throw new Error('Grid AI not found');
  grid_ai.is_active = false;
  await saveGridAi(grid_ai);
}


const createPositionAi = async params => {
  validateParams(params, [
    'symbol', 'take_profit', 'stop_loss', 'order_type', 'volume', 'position_count',
    'secure_trade', 'secure_amount', 'execution_amount', 'price', 'account_id'
  ]);
  const { account_id } = params;
  const accountNumber = await findAccountNumberByAccountId(account_id);
  if (!accountNumber || accountNumber.length === 0) throw new Error('Failed to find account number');
  params.account_number = accountNumber[0].login;
  await savePositionAi(params);
}

const getGridAiByAccountNumberAndSymbol = async (account_number, symbol) => {
  return await findGridAiByAccountNumberAndSymbol(account_number, symbol);
}

const getGridAndPositionAiByAccountNumberAndSymbol = async (account_number, symbol) => {
  let grid_ai = await findGridAiByAccountNumberAndSymbol(account_number, symbol);
  let position_ai = await findPositionAiByAccountNumberAndSymbol(account_number, symbol);
  return { grid_ai, position_ai };
};

/**
 * Calculate profit/loss amount
 * @param {Object} params - Parameters for calculation
 * @returns {Promise<Object>} Profit/loss amounts
 */
const calculateProfitLossAmount = async params => {
  validateParams(params, ['tradeAccountToken']);
  const { tradeAccountToken } = params;

  try {
    let openOrders = await openedOrders(tradeAccountToken);
    openOrders = openOrders.filter(order => 
      order.orderType === ORDER_TYPES.BUY || order.orderType === ORDER_TYPES.SELL
    );

    const result = {};
    for (let order of openOrders) {
      const profitChange = order.takeProfit !== 0 ? Math.abs(order.takeProfit - order.openPrice) : 0;
      const lossChange = order.stopLoss !== 0 ? Math.abs(order.stopLoss - order.openPrice) : 0;

      const profit = profitChange !== 0 ? 
        await calculatePriceChangeAmount({
          tradeAccountToken,
          symbol: order.symbol,
          lotSize: order.lots,
          priceChange: profitChange
        }) : 0;

      const loss = lossChange !== 0 ? 
        await calculatePriceChangeAmount({
          tradeAccountToken,
          symbol: order.symbol,
          lotSize: order.lots,
          priceChange: lossChange
        }) : 0;

      let _loss = loss;
      if(order.orderType === ORDER_TYPES.BUY) {
        _loss = order.stopLoss < order.openPrice ? -1 * loss : loss;
      } else if(order.orderType === ORDER_TYPES.SELL) {
        _loss = order.stopLoss > order.openPrice ? -1 * loss : loss;
      }
        
      result[`${tradeAccountToken}=${order.symbol}=${order.ticket}=profit`] = profit;
      result[`${tradeAccountToken}=${order.symbol}=${order.ticket}=loss`] = _loss;
      result[`${tradeAccountToken}=${order.symbol}=${order.ticket}=currentProfit`] = order.profit;
    }

    return result;
  } catch (error) {
    console.error('Error calculating profit/loss amount:', {
      error: error.message,
      params,
      timestamp: new Date().toISOString()
    });
    throw new TradeError('Failed to calculate profit/loss amount', 'PROFIT_LOSS_ERROR', error);
  }
};


const updateTPAndSL = async params => {
  const { tradeAccountToken, toAmount, modifyType, orderList } = params;

  let openOrders = await openedOrders(tradeAccountToken);
  openOrders = openOrders.filter(order => order.orderType === 'Buy' || order.orderType === 'Sell');
  let mainOrderList = openOrders.filter(order => orderList.includes(order.ticket));

  try {
    // Prepare all modification requests
    const modificationRequests = mainOrderList.filter(order => order.orderType === 'Buy' || order.orderType === 'Sell').map( async order => {
      const toPriceChange = await calculatePriceChange({tradeAccountToken, lotSize: order.lots, symbol: order.symbol, amount: toAmount});
      const newTP = order.orderType === 'Buy' ? 
        order.openPrice + toPriceChange : 
        order.openPrice - toPriceChange;
      
      const newSL = order.orderType === 'Sell' ? 
        order.openPrice + toPriceChange : 
        order.openPrice - toPriceChange;

      const secureSL = order.orderType === 'Buy' ? 
        order.openPrice + toPriceChange : 
        order.openPrice - toPriceChange;

      return orderModify(
        tradeAccountToken,
        order.ticket,
        modifyType === 'SL' ? newSL : modifyType === 'Secure' ? secureSL : order.stopLoss,
        modifyType === 'TP' ? newTP : order.takeProfit,
        0
      ).catch(error => {
        console.error(`Error modifying order ${order.ticket}:`, error.message);
        return null; // Return null for failed modifications
      });
    });

    // Execute all modifications in parallel
    const results = await Promise.all(modificationRequests);
    
    // Return number of successful modifications
    return results.filter(result => result !== null).length;

  } catch (error) {
    console.error('Error in updateTpSl:', error.message);
    throw error;
  }
};




// Export functions
module.exports = {
  _sendOrderImpl,
  _modifyOrderImpl,
  _closeOrderImpl,
  _getOpenPrice,
  _onUpdateOrder,
  _onPlaceOrder,
  _onPlaceQuickPendingTrade,
  _onPlaceQuickTrade,
  secureTrades,
  openRangePendingOrder,
  updateOrderTakeProfitBySymbol,
  placePendingOrderWithProfitTarget,
  placeGapPendingOrderWithProfitTarget,
  placeQuickGapPendingOrderWithProfitTarget,
  placeAdvancePendingOrderWithProfitTarget,
  placeOrderWithProfitTarget,
  getHedgedOrder,
  secureOrderTakeProfitBySymbol,
  secureOrderTakeProfitByTicket,
  updateOrderTakeProfitByTicket,
  updateOrderStopLossBySymbol,
  updateOrderStopLossByTicket,
  calculateTakeProfit,
  calculateStopLoss,
  calculatePriceChange,
  calculatePriceChangeAmount,
  calculateSpreadAmount,
  calculateStopLevelAmount,
  calculateStopLevel,
  closeTrades,
  closeTradesByTickets,
  getTradeStatistics,
  calculateGridAiPrices,
  createGridAi,
  createHedgeGridAi,
  calculateProfitLossAmount,
  calculateUnrealizedProfit,
  calculateUnrealizedProfitBySymbol,
  calculatePnL,
  updateTPAndSL,
  findClosestOrder,
  findFarthestOrder,
  getGridAiByAccountNumberAndSymbol,
  getGridAndPositionAiByAccountNumberAndSymbol,
  TradeError,
  updateGridAi,
  disableGridAiById,
  createPositionAi,
  // Helper functions
  validateParams,
  getCountByType,
  getCountBySymbol,
  getUniqueSymbols,
  withCache,
  createOrderModel,
  getOrderTickParams,
  filterOrdersByType,
  isPendingOrder,
  getTickValueAndSize,
};
