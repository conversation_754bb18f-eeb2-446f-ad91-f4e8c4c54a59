// lib/websocket-server.js
/**
 * WebSocket Server implementation for Trade Manager
 * Provides real-time communication between clients and the trading server
 * Supports client identification, message broadcasting, and real-time data services
 * @module WebSocketServer
 */
const WebSocket = require('ws');

/**
 * WebSocketServer class for managing WebSocket connections and communication
 * @class WebSocketServer
 */
class WebSocketServer {
  /**
   * Creates a new WebSocketServer instance
   * @param {Object} options - Configuration options
   * @param {number} [options.port=3000] - Port to listen on if no server is provided
   * @param {Object} [options.server] - Existing HTTP/HTTPS server to attach to
   * @param {Function} [options.onMessage] - Handler for incoming messages
   * @param {Function} [options.onConnection] - Handler for new connections
   * @param {Function} [options.onDisconnection] - Handler for client disconnections
   * @param {Function} [options.onError] - Handler for WebSocket errors
   * @param {Function} [options.validateClientId] - Function to validate client IDs
   */
  constructor(options = {}) {
    this.clients = new Map(); // Map of connected clients with clientId as key
    this.wss = null; // WebSocket server instance
    this.intervals = new Map(); // Store interval references for real-time services
    this.options = {
      port: options.port || 3000,
      server: options.server,
      onMessage: options.onMessage || this.defaultMessageHandler,
      onConnection: options.onConnection || this.defaultConnectionHandler,
      onDisconnection:
        options.onDisconnection || this.defaultDisconnectionHandler,
      onError: options.onError || this.defaultErrorHandler,
      validateClientId: options.validateClientId
    };
  }

  /**
   * Starts the WebSocket server
   * @returns {WebSocketServer} - Returns this instance for method chaining
   */
  start() {
    // Create WebSocket server using provided HTTP server or on specified port
    this.wss = this.options.server
      ? new WebSocket.Server({ server: this.options.server })
      : new WebSocket.Server({ port: this.options.port });

    // Handle new connections
    this.wss.on('connection', ws => {
      // Wait for initial identification message
      ws.once('message', message => {
        try {
          const data = JSON.parse(message);
          if (data.type === 'identify' && data.clientId) {
            const clientId = data.clientId.toString();

            // Check if client ID is already in use
            if (this.clients.has(clientId)) {
              ws.send(
                JSON.stringify({
                  type: 'error',
                  message: 'Client ID already in use'
                })
              );
              ws.close();
              return;
            }

            // Validate client ID if validator function is provided
            if (
              this.options.validateClientId &&
              !this.options.validateClientId(clientId)
            ) {
              ws.send(
                JSON.stringify({
                  type: 'error',
                  message: 'Invalid client ID'
                })
              );
              ws.close();
              return;
            }

            // Store client connection and set up event handlers
            this.clients.set(clientId, ws);
            this.options.onConnection(ws, clientId, this);

            ws.on('message', msg => {
              this.options.onMessage(ws, clientId, msg, this);
            });

            ws.on('close', () => {
              this.clients.delete(clientId);
              this.options.onDisconnection(ws, clientId, this);
            });

            ws.on('error', error => {
              this.options.onError(ws, clientId, error, this);
            });
          } else {
            // Reject connection if identification is missing
            ws.send(
              JSON.stringify({
                type: 'error',
                message: 'Initial message must be identification'
              })
            );
            ws.close();
          }
        } catch (error) {
          // Handle invalid message format
          ws.send(
            JSON.stringify({
              type: 'error',
              message: 'Invalid identification format'
            })
          );
          ws.close();
        }
      });
    });

    return this;
  }

  /**
   * Starts a real-time service that periodically sends data to all connected clients
   * @param {string} serviceName - Unique identifier for the service
   * @param {number} intervalMs - Interval in milliseconds between data broadcasts
   * @param {Function} dataGenerator - Function that generates data to send
   * @returns {WebSocketServer} - Returns this instance for method chaining
   * @throws {Error} If service with the same name is already running
   */
  startRealtimeService(serviceName, intervalMs, dataGenerator) {
    if (this.intervals.has(serviceName)) {
      throw new Error(`Service ${serviceName} already running`);
    }

    const interval = setInterval(() => {
      const data = dataGenerator(this);
      this.clients.forEach((client, clientId) => {
        if (client.readyState === WebSocket.OPEN) {
          client.send(
            JSON.stringify({
              type: 'realtime',
              service: serviceName,
              data,
              timestamp: new Date().toISOString()
            })
          );
        }
      });
    }, intervalMs);

    this.intervals.set(serviceName, interval);
    return this;
  }

  /**
   * Stops a specific real-time service
   * @param {string} serviceName - Name of the service to stop
   */
  stopRealtimeService(serviceName) {
    const interval = this.intervals.get(serviceName);
    if (interval) {
      clearInterval(interval);
      this.intervals.delete(serviceName);
    }
  }

  /**
   * Default handler for new client connections
   * @param {WebSocket} ws - WebSocket connection
   * @param {string} clientId - Client identifier
   * @param {WebSocketServer} server - WebSocketServer instance
   */
  defaultConnectionHandler(ws, clientId, server) {
    ws.send(
      JSON.stringify({
        type: 'connection',
        clientId,
        message: 'Connected to WebSocket server'
      })
    );
  }

  /**
   * Default handler for incoming messages
   * Broadcasts received messages to all other clients
   * @param {WebSocket} ws - WebSocket connection
   * @param {string} clientId - Client identifier
   * @param {string} message - Raw message received
   * @param {WebSocketServer} server - WebSocketServer instance
   */
  defaultMessageHandler(ws, clientId, message, server) {
    try {
      const data = JSON.parse(message);
      server.broadcast(data, clientId);
    } catch (error) {
      console.error('Error parsing message:', error);
    }
  }

  /**
   * Default handler for client disconnections
   * Broadcasts disconnection event to all other clients
   * @param {WebSocket} ws - WebSocket connection
   * @param {string} clientId - Client identifier
   * @param {WebSocketServer} server - WebSocketServer instance
   */
  defaultDisconnectionHandler(ws, clientId, server) {
    server.broadcast(
      {
        type: 'disconnection',
        clientId,
        message: 'User disconnected'
      },
      clientId
    );
  }

  /**
   * Default handler for WebSocket errors
   * @param {WebSocket} ws - WebSocket connection
   * @param {string} clientId - Client identifier
   * @param {Error} error - Error object
   * @param {WebSocketServer} server - WebSocketServer instance
   */
  defaultErrorHandler(ws, clientId, error, server) {
    console.error('WebSocket error:', error);
  }

  /**
   * Broadcasts a message to all clients except the sender
   * @param {Object} data - Data to broadcast
   * @param {string} senderId - ID of the sender to exclude
   */
  broadcast(data, senderId) {
    this.clients.forEach((client, id) => {
      if (id !== senderId && client.readyState === WebSocket.OPEN) {
        client.send(JSON.stringify(data));
      }
    });
  }

  /**
   * Sends a message to a specific client
   * @param {string} clientId - ID of the client to send to
   * @param {Object} data - Data to send
   */
  sendToClient(clientId, data) {
    const client = this.clients.get(clientId);
    if (client && client.readyState === WebSocket.OPEN) {
      client.send(JSON.stringify(data));
    }
  }

  /**
   * Gets the current number of connected clients
   * @returns {number} - Number of connected clients
   */
  getClientCount() {
    return this.clients.size;
  }

  /**
   * Stops the WebSocket server and cleans up resources
   */
  stop() {
    if (this.wss) {
      this.intervals.forEach(interval => clearInterval(interval));
      this.intervals.clear();
      this.wss.close();
      this.clients.clear();
    }
  }
}

module.exports = WebSocketServer;
