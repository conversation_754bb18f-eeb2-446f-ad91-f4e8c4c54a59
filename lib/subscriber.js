// mq-subscriber.js (unchanged from your last version)
const mqtt = require('mqtt');
const { v4: uuid } = require('uuid');

const MQTT_URI =
  'mqtt://tradebot:s3cr3t!' +
  '@rabbitmq-pc8c0woscocgg8044c08wk0k.trademanager.cloud:1883';

const TOPIC_ROOT = '%2F';
const SYMBOL = 'XAUUSD';
const ACCT = '12345678';
const TOPIC = `${TOPIC_ROOT}/${SYMBOL}/${ACCT}`;
const KEEPALIVE = 60;
const RECONNECT = 1000;

const client = mqtt.connect(MQTT_URI, {
  clientId: `nodeSub_${uuid().slice(0, 8)}`,
  clean: true,
  keepalive: KEEPALIVE,
  reconnectPeriod: RECONNECT
});

client.on('connect', () => {
  console.log('✔ MQTT_CONNECTED (vhost /)');
  client.subscribe(TOPIC, { qos: 0 }, (err, granted) => {
    if (err) {
      console.error(
        '❌ SUBSCRIBE failed:',
        err.message,
        JSON.stringify(err, null, 2)
      );
      console.error('Granted:', JSON.stringify(granted, null, 2));
    } else {
      console.log('✅ Subscribed to', TOPIC, 'QoS:', granted[0]?.qos);
    }
  });
});

client.on('message', (topic, payload) => {
  try {
    const message = JSON.parse(payload.toString());
    console.log('📥 Received:', topic, message);
  } catch (e) {
    console.error('❌ Parse error:', e.message);
  }
});

client.on('error', err => {
  console.error('❌ MQTT Error:', JSON.stringify(err, null, 2));
});

client.on('close', () => {
  console.warn('⚠ MQTT closed');
});

client.on('reconnect', () => {
  console.log('… reconnecting');
});
