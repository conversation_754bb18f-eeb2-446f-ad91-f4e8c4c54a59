
const { fetchTestingData, fetchSyntheticTestingData, calculateATR } = require('../trade-system-lib');
const { SMA, RSI, MACD } = require('technicalindicators');
const fs = require('fs');
const path = require('path');

/**
 * Calculate momentum indicators
 */
function calculateIndicators(candles) {
  // Fast and slow EMAs for trend direction
  const fastEMA = SMA.calculate({
    period: 5,
    values: candles.map(c => c.close)
  });
  
  const slowEMA = SMA.calculate({
    period: 13,
    values: candles.map(c => c.close)
  });
  
  // RSI for overbought/oversold
  const rsi = RSI.calculate({
    period: 7,
    values: candles.map(c => c.close)
  });
  
  // MACD for momentum
  const macdInput = {
    values: candles.map(c => c.close),
    fastPeriod: 8,
    slowPeriod: 21,
    signalPeriod: 5,
    SimpleMAOscillator: false,
    SimpleMASignal: false
  };
  
  const macd = MACD.calculate(macdInput);
  
  return candles.map((candle, i) => {
    const offset = candles.length - Math.min(fastEMA.length, slowEMA.length, rsi.length, macd.length);
    const idx = i - offset;
    
    if (idx < 0) return null;
    
    return {
      time: candle.time,
      fastEMA: fastEMA[idx],
      slowEMA: slowEMA[idx],
      rsi: rsi[idx],
      macd: macd[idx]
    };
  }).filter(i => i !== null);
}

/**
 * Detect high-frequency trading opportunities
 */
function detectHFTSignals(candles, indicators, atr) {
  const signals = [];
  const lookback = 3; // Number of candles to look back
  
  for (let i = lookback; i < candles.length; i++) {
    const currentCandle = candles[i];
    const prevCandle = candles[i - 1];
    const ind = indicators[i - lookback];
    
    if (!ind) continue;
    
    // Volatility threshold
    const volatilityThreshold = atr[i] * 0.5;
    
    // Price movement check
    const priceChange = Math.abs(currentCandle.close - prevCandle.close);
    const isSignificantMove = priceChange > volatilityThreshold;
    
    // Momentum conditions
    const isBullishMomentum = ind.macd.MACD > ind.macd.signal && ind.rsi < 70;
    const isBearishMomentum = ind.macd.MACD < ind.macd.signal && ind.rsi > 30;
    
    // Trend alignment
    const isBullishTrend = ind.fastEMA > ind.slowEMA;
    const isBearishTrend = ind.fastEMA < ind.slowEMA;
    
    // Signal generation with hedging logic
    if (isSignificantMove) {
      // Long signal
      if (isBullishMomentum && isBullishTrend) {
        signals.push({
          type: 'buy',
          time: currentCandle.time,
          price: currentCandle.close,
          stopLoss: currentCandle.close - volatilityThreshold,
          takeProfit: currentCandle.close + (volatilityThreshold * 1.5),
          score: calculateSignalScore(ind, 'buy'),
          hedgePrice: currentCandle.close - (volatilityThreshold * 0.5),
          hedgeStopLoss: currentCandle.close + (volatilityThreshold * 0.75),
          hedgeTakeProfit: currentCandle.close - volatilityThreshold,
          candle: currentCandle,
          index: i
        });
      }
      // Short signal
      else if (isBearishMomentum && isBearishTrend) {
        signals.push({
          type: 'sell',
          time: currentCandle.time,
          price: currentCandle.close,
          stopLoss: currentCandle.close + volatilityThreshold,
          takeProfit: currentCandle.close - (volatilityThreshold * 1.5),
          score: calculateSignalScore(ind, 'sell'),
          hedgePrice: currentCandle.close + (volatilityThreshold * 0.5),
          hedgeStopLoss: currentCandle.close - (volatilityThreshold * 0.75),
          hedgeTakeProfit: currentCandle.close + volatilityThreshold,
          candle: currentCandle,
          index: i
        });
      }
    }
  }
  
  return signals;
}

/**
 * Calculate signal score based on indicator alignment
 */
function calculateSignalScore(indicators, type) {
  let score = 0;
  
  if (type === 'buy') {
    // Trend strength
    if (indicators.fastEMA > indicators.slowEMA) score += 1;
    
    // Momentum strength
    if (indicators.macd.MACD > 0) score += 1;
    if (indicators.macd.MACD > indicators.macd.signal) score += 1;
    
    // RSI conditions
    if (indicators.rsi < 70 && indicators.rsi > 40) score += 1;
    if (indicators.rsi > 50) score += 1;
  } else {
    // Trend strength
    if (indicators.fastEMA < indicators.slowEMA) score += 1;
    
    // Momentum strength
    if (indicators.macd.MACD < 0) score += 1;
    if (indicators.macd.MACD < indicators.macd.signal) score += 1;
    
    // RSI conditions
    if (indicators.rsi > 30 && indicators.rsi < 60) score += 1;
    if (indicators.rsi < 50) score += 1;
  }
  
  return score / 5; // Normalize to 0-1 range
}

/**
 * Test HFT signals with hedging
 */
function testHFTSignal(signal, futureCandles) {
  let mainResult = 'OPEN';
  let hedgeResult = 'OPEN';
  let mainExitPrice = null;
  let hedgeExitPrice = null;
  let mainExitTime = null;
  let hedgeExitTime = null;
  let mainPnL = 0;
  let hedgePnL = 0;
  let holdingPeriod = 0;
  let maxDrawdown = 0;
  
  const mainEntry = signal.price;
  const hedgeEntry = signal.hedgePrice;
  
  for (const candle of futureCandles) {
    holdingPeriod++;
    
    // Main position
    if (signal.type === 'buy') {
      if (candle.low <= signal.stopLoss && mainResult === 'OPEN') {
        mainResult = 'LOSS';
        mainExitPrice = signal.stopLoss;
        mainExitTime = candle.time;
        mainPnL = ((signal.stopLoss - mainEntry) / mainEntry) * 100;
      } else if (candle.high >= signal.takeProfit && mainResult === 'OPEN') {
        mainResult = 'WIN';
        mainExitPrice = signal.takeProfit;
        mainExitTime = candle.time;
        mainPnL = ((signal.takeProfit - mainEntry) / mainEntry) * 100;
      }
    } else {
      if (candle.high >= signal.stopLoss && mainResult === 'OPEN') {
        mainResult = 'LOSS';
        mainExitPrice = signal.stopLoss;
        mainExitTime = candle.time;
        mainPnL = ((mainEntry - signal.stopLoss) / mainEntry) * 100;
      } else if (candle.low <= signal.takeProfit && mainResult === 'OPEN') {
        mainResult = 'WIN';
        mainExitPrice = signal.takeProfit;
        mainExitTime = candle.time;
        mainPnL = ((mainEntry - signal.takeProfit) / mainEntry) * 100;
      }
    }
    
    // Hedge position (opposite direction)
    if (signal.type === 'buy') {
      if (candle.high >= signal.hedgeStopLoss && hedgeResult === 'OPEN') {
        hedgeResult = 'LOSS';
        hedgeExitPrice = signal.hedgeStopLoss;
        hedgeExitTime = candle.time;
        hedgePnL = ((hedgeEntry - signal.hedgeStopLoss) / hedgeEntry) * 100;
      } else if (candle.low <= signal.hedgeTakeProfit && hedgeResult === 'OPEN') {
        hedgeResult = 'WIN';
        hedgeExitPrice = signal.hedgeTakeProfit;
        hedgeExitTime = candle.time;
        hedgePnL = ((hedgeEntry - signal.hedgeTakeProfit) / hedgeEntry) * 100;
      }
    } else {
      if (candle.low <= signal.hedgeStopLoss && hedgeResult === 'OPEN') {
        hedgeResult = 'LOSS';
        hedgeExitPrice = signal.hedgeStopLoss;
        hedgeExitTime = candle.time;
        hedgePnL = ((signal.hedgeStopLoss - hedgeEntry) / hedgeEntry) * 100;
      } else if (candle.high >= signal.hedgeTakeProfit && hedgeResult === 'OPEN') {
        hedgeResult = 'WIN';
        hedgeExitPrice = signal.hedgeTakeProfit;
        hedgeExitTime = candle.time;
        hedgePnL = ((signal.hedgeTakeProfit - hedgeEntry) / hedgeEntry) * 100;
      }
    }
    
    // Track drawdown
    const currentMainPnL = signal.type === 'buy' 
      ? ((candle.close - mainEntry) / mainEntry) * 100
      : ((mainEntry - candle.close) / mainEntry) * 100;
    
    const currentHedgePnL = signal.type === 'buy'
      ? ((hedgeEntry - candle.close) / hedgeEntry) * 100
      : ((candle.close - hedgeEntry) / hedgeEntry) * 100;
    
    const totalPnL = currentMainPnL + currentHedgePnL;
    maxDrawdown = Math.min(maxDrawdown, totalPnL);
    
    // Exit if both positions are closed
    if (mainResult !== 'OPEN' && hedgeResult !== 'OPEN') break;
  }
  
  return {
    mainResult,
    hedgeResult,
    mainEntryTime: new Date(signal.time).toLocaleString(),
    hedgeEntryTime: new Date(signal.time).toLocaleString(),
    mainExitTime: mainExitTime ? new Date(mainExitTime).toLocaleString() : 'Still Open',
    hedgeExitTime: hedgeExitTime ? new Date(hedgeExitTime).toLocaleString() : 'Still Open',
    mainEntryPrice: mainEntry,
    hedgeEntryPrice: hedgeEntry,
    mainExitPrice,
    hedgeExitPrice,
    mainPnL,
    hedgePnL,
    totalPnL: mainPnL + hedgePnL,
    holdingPeriod,
    maxDrawdown
  };
}

/**
 * Display HFT trading results
 */
function displayHFTResults(signals, testResults, initialCapital, symbol) {
  console.log('\n' + '═'.repeat(80));
  console.log('║' + ' '.repeat(20) + `HFT HEDGING SYSTEM - ${symbol}` + ' '.repeat(20) + '║');
  console.log('═'.repeat(80));
  
  // Calculate statistics
  let totalTrades = testResults.length;
  let mainWins = testResults.filter(r => r.mainResult === 'WIN').length;
  let hedgeWins = testResults.filter(r => r.hedgeResult === 'WIN').length;
  let totalPnL = testResults.reduce((sum, r) => sum + r.totalPnL, 0);
  let avgPnL = totalPnL / totalTrades;
  let avgHoldingPeriod = testResults.reduce((sum, r) => sum + r.holdingPeriod, 0) / totalTrades;
  let maxDrawdown = Math.min(...testResults.map(r => r.maxDrawdown));
  
  // Get start and end dates
  const startDate = signals.length > 0 ? new Date(signals[0].time) : new Date();
  const endDate = signals.length > 0 ? new Date(signals[signals.length - 1].time) : new Date();
  
  // Display summary
  console.log('\n╔══ PERFORMANCE SUMMARY ' + '═'.repeat(57) + '╗');
  console.log('║                                                                            ║');
  console.log(`║  Symbol: ${symbol.padEnd(20)}  Period: ${startDate.toLocaleDateString()} - ${endDate.toLocaleDateString()}  ║`);
  console.log(`║  Duration: ${Math.ceil((endDate - startDate) / (1000 * 60 * 60 * 24))} days                                                              ║`);
  console.log(`║  Total Trades: ${totalTrades.toString().padEnd(8)}                                                    ║`);
  console.log(`║  Main Positions: Win Rate: ${((mainWins / totalTrades) * 100).toFixed(1)}%  Success: ${mainWins}/${totalTrades}                    ║`);
  console.log(`║  Hedge Positions: Win Rate: ${((hedgeWins / totalTrades) * 100).toFixed(1)}%  Success: ${hedgeWins}/${totalTrades}                    ║`);
  console.log(`║  Total P&L: ${totalPnL.toFixed(2).padEnd(10)}%  Avg P&L: ${avgPnL.toFixed(2).padEnd(6)}%  Max DD: ${maxDrawdown.toFixed(2)}%      ║`);
  console.log(`║  Avg Hold Time: ${avgHoldingPeriod.toFixed(1).padEnd(6)} candles                                          ║`);
  console.log('║                                                                            ║');
  console.log('╚' + '═'.repeat(76) + '╝\n');
  
  // Display recent trades
  console.log('Recent Trades:');
  console.log('═'.repeat(100));
  testResults.slice(-5).forEach((result, i) => {
    console.log(`Trade ${testResults.length - 4 + i}:`);
    console.log(`Main Position: ${result.mainResult} | Entry: ${result.mainEntryPrice} | Exit: ${result.mainExitPrice || 'Open'} | P&L: ${result.mainPnL.toFixed(2)}%`);
    console.log(`Hedge Position: ${result.hedgeResult} | Entry: ${result.hedgeEntryPrice} | Exit: ${result.hedgeExitPrice || 'Open'} | P&L: ${result.hedgePnL.toFixed(2)}%`);
    console.log(`Combined P&L: ${result.totalPnL.toFixed(2)}% | Hold Time: ${result.holdingPeriod} candles`);
    console.log('─'.repeat(100));
  });
}

/**
 * Save HFT simulation results to log file
 */
function saveHFTSimulationLog(signals, testResults, initialCapital, symbol) {
  const timestamp = new Date().toISOString().replace(/:/g, '-');
  const logDir = path.join(__dirname, 'simulation_logs');
  const logFile = path.join(logDir, `${symbol}_hft_${timestamp}.log`);
  
  if (!fs.existsSync(logDir)) {
    fs.mkdirSync(logDir, { recursive: true });
  }
  
  // Prepare log data
  const logData = {
    symbol,
    timestamp,
    initialCapital,
    testingPeriod: {
      start: signals.length > 0 ? new Date(signals[0].time).toISOString() : new Date().toISOString(),
      end: signals.length > 0 ? new Date(signals[signals.length - 1].time).toISOString() : new Date().toISOString(),
      durationDays: signals.length > 0 ? 
        Math.ceil((new Date(signals[signals.length - 1].time) - new Date(signals[0].time)) / (1000 * 60 * 60 * 24)) : 0
    },
    summary: {
      totalTrades: testResults.length,
      mainWins: testResults.filter(r => r.mainResult === 'WIN').length,
      hedgeWins: testResults.filter(r => r.hedgeResult === 'WIN').length,
      totalPnL: testResults.reduce((sum, r) => sum + r.totalPnL, 0),
      maxDrawdown: Math.min(...testResults.map(r => r.maxDrawdown)),
      averageHoldingPeriod: testResults.reduce((sum, r) => sum + r.holdingPeriod, 0) / testResults.length
    },
    trades: signals.map((signal, i) => ({
      signal: {
        type: signal.type,
        time: new Date(signal.time).toISOString(),
        price: signal.price,
        score: signal.score
      },
      result: testResults[i]
    }))
  };
  
  // Save to file
  fs.writeFileSync(logFile, JSON.stringify(logData, null, 2));
  console.log(`\nSimulation log saved to: ${logFile}`);
}

/**
 * Start trading function
 */
async function startHFTHedging() {
  try {
    // Clear old logs
    const logDir = path.join(__dirname, 'simulation_logs');
    if (!fs.existsSync(logDir)) {
      fs.mkdirSync(logDir, { recursive: true });
    }
    
    // Initialize with synthetic index for higher volatility
    const symbol = 'Volatility 75 Index';
    const data = await fetchSyntheticTestingData(symbol);
    
    // Calculate indicators
    const atr = calculateATR(data);
    const indicators = calculateIndicators(data);
    
    // Detect signals
    const signals = detectHFTSignals(data, indicators, atr);
    
    // Test signals
    const testResults = signals.map(signal => {
      const futureCandles = data.slice(signal.index + 1);
      return testHFTSignal(signal, futureCandles);
    });
    
    // Display and save results
    displayHFTResults(signals, testResults, 500, symbol);
    saveHFTSimulationLog(signals, testResults, 500, symbol);
    
  } catch (error) {
    console.error('Error:', error.stack);
  }
}

// module.exports = { startHFTHedging };

startHFTHedging();