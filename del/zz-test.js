function calculateTakeProfit(
  openPrice,
  lotSize,
  tickValue,
  tickSize,
  profitTarget,
  orderType,
  spread
) {
  // Validate order type
  if (orderType !== 'Buy' && orderType !== 'Sell') {
    throw new Error("Order type must be either 'Buy' or 'Sell'");
  }

  // Convert spread to price units
  const spreadInPrice = spread * tickSize;

  // Calculate the actual entry price considering the spread
  let entryPrice;
  if (orderType === 'Buy') {
    entryPrice = openPrice + spreadInPrice;
  } else {
    entryPrice = openPrice;
  }

  // Calculate the number of ticks needed to reach the profit target
  const ticksNeeded = profitTarget / (lotSize * tickValue);

  // Calculate the price change
  const priceChange = ticksNeeded * tickSize;

  // Calculate the take profit price based on order type
  let takeProfitPrice;
  if (orderType === 'Buy') {
    takeProfitPrice = entryPrice + priceChange;
  } else {
    takeProfitPrice = entryPrice - priceChange;
  }

  // Round to 5 decimal places, which is common in forex
  return Number(takeProfitPrice.toFixed(5));
}

// Example usage:
const openPrice = 1.10000;
const lotSize = 1;
const tickValue = 1; // Assuming 1 pip = $1 for simplicity
const tickSize = 0.00001; // Typical for many currency pairs
const profitTarget = 50; // $50 profit target
const orderType = 'Buy'; // or 'Sell'
const spread = 2; // 2 pips spread

const takeProfitPrice = calculateTakeProfit(openPrice, lotSize, tickValue, tickSize, profitTarget, orderType, spread);
console.log(`Take Profit Price: ${takeProfitPrice}`);
