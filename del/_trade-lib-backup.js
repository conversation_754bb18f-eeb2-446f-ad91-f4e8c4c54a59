const { isEven, customRound } = require('../util.js');
const { mapSymbol } = require('../symbol-mapper.js');
const { orderSend, orderModify, orderClose, openedOrders, getQuote, symbolParams } = require('../mt5-service.js');
const {
  findBotById,
  findIndicatorValuesBySymbol,
  saveGridAi
} = require('../supabaseService.js');
const {
  getPriceList,
  calculateTakeProfitImpl,
  calculateStopLossImpl,
  calculatePriceChangeImpl,
  calculatePriceChangeAmountImpl,
  priceIsBetween
} = require('../util.js');

const _sendOrderImpl = async (finalModel, tradeAccountToken) => {
  const _finalModel = {
    symbol: finalModel.symbol,
    actionType: finalModel.actionType,
    volume: finalModel.volume,
    openPrice: finalModel.openPrice,
    slippage: 1000,
    stoploss: finalModel?.stopLoss,
    takeprofit: finalModel?.takeProfit,
    comment: finalModel?.comment || '',
    expertID: 0,
    stopLimitPrice: 0,
    placedType: "Manually"
  }
  await orderSend(
    tradeAccountToken,
    _finalModel.symbol,
    _finalModel.actionType,
    _finalModel.volume,
    _finalModel.openPrice,
    _finalModel.slippage,
    _finalModel.stoploss,
    _finalModel.takeprofit,
    _finalModel.comment,
    _finalModel.expertID,
    _finalModel.stopLimitPrice,
    _finalModel.placedType
  );
};

const _modifyOrderImpl = async (finalModel, tradeAccountToken) => {
  const _finalModel = {
    id: tradeAccountToken,
    ticket: finalModel.ticket,
    price: finalModel.price,
    stoploss: finalModel?.stopLoss,
    takeprofit: finalModel?.takeProfit,
  };
  await orderModify({
    tradeAccountToken,
    _finalModel
  });
};

const _closeOrderImpl = async (finalModel, tradeAccountToken) => {
  const _finalModel = {
      id: tradeAccountToken,
      ticket: finalModel.ticket,
      lots: finalModel.lots,
      price: finalModel.price,
      slippage: 1000,
  };
  await orderClose({
    tradeAccountToken,
    _finalModel
  });
};

const _getOpenPrice = (orderModel, quote) => {
  if (quote === undefined) return 0;
  if (orderModel.openPrice !== 0) return orderModel.openPrice;
  return orderModel.actionType.toLowerCase().includes('buy')
    ? quote?.ask
    : quote?.bid;
};

const _onUpdateOrder = async params => {
  const {
    orderModel,
    showMessage,
    tradeSymbol,
    openOrders,
    modifyOrder,
    tradeAccountToken
  } = params;

  
  let symbolOrders = openOrders.filter(
    order => order.symbol === tradeSymbol.currency
  );
  symbolOrders.forEach(async order => {
    if (
      order.symbol === tradeSymbol.currency &&
      (order.orderType === 'Buy' || order.orderType === 'Sell')
    ) {
      const finalModel = {
        ticket: order.ticket,
        takeProfit:
          orderModel.takeProfit === 0
            ? order.takeProfit
            : orderModel.takeProfit,
        stopLoss:
          orderModel.stopLoss === 0 ? order.stopLoss : orderModel.stopLoss,
        positionId: order.id
      };
      modifyOrder(
        tradeAccountToken,
        finalModel.ticket,
        finalModel.stopLoss,
        finalModel.takeProfit
      );
    }
  });
  
  showMessage('Success', `Modified ${symbolOrders.length} successfully`);
};

const _onPlaceQuickPendingTrade = async params => {
  const {
    orderModel,
    tradeAccountToken,
    tradeQuote,
  } = params;

  if (!tradeAccountToken) {
    console.log('Trade Account token not found');
    return;
  }


  try {
    const finalModel = {
      symbol: orderModel.symbol,
      actionType: orderModel.actionType,
      takeProfit: orderModel.takeProfit,
      stopLoss: orderModel.stopLoss,
      volume: orderModel.volume
    };

    orderModel.openPrice = 0;
    if (orderModel.type === 'buy' || orderModel.type === 'Buy') {
      finalModel.actionType = 'BuyStop';
      orderModel.actionType = 'SellStop';
    } else if (orderModel.type === 'sell' || orderModel.type === 'Sell') {
      finalModel.actionType = 'SellStop';
      orderModel.actionType = 'SellStop';
    }

    let curr_price = _getOpenPrice(orderModel, tradeQuote);
    

    finalModel.openPrice =
      orderModel.type == 'buy' || orderModel.type === 'Buy'
        ? curr_price + orderModel.startDistance * orderModel.gridSize
        : curr_price - orderModel.startDistance * orderModel.gridSize;

    if (orderModel.useTakeProfit) {
      finalModel.takeProfit =
        orderModel.type == 'buy' || orderModel.type === 'Buy'
          ? finalModel.openPrice + orderModel.takeProfit * orderModel.gridSize
          : finalModel.openPrice - orderModel.takeProfit * orderModel.gridSize;
    }

    if (orderModel.useStopLoss) {
      finalModel.stopLoss =
        orderModel.type == 'buy' || orderModel.type === 'Buy'
          ? finalModel.openPrice - orderModel.stopLoss * orderModel.gridSize
          : finalModel.openPrice + orderModel.stopLoss * orderModel.gridSize;
    }

    if (orderModel.useDynamicMode) {
      orderModel.positionCount = orderModel.takeProfit;
    }
    
    for (let j = 0; j < orderModel.positionCount; j++) {
      if (orderModel.useDynamicMode) {
        finalModel.takeProfit =
          orderModel.type === 'buy' || orderModel.type === 'Buy'
            ? finalModel.openPrice + (j + 1) * orderModel.gridSize
            : finalModel.openPrice - (j + 1) * orderModel.gridSize;
      }
      try {
         await _sendOrderImpl(finalModel, tradeAccountToken);
      } catch (error) {
        console.error('Error placing order:', error.message);
      }
    }
  } catch (error) {
    console.error(error);
  }
};

const _onPlaceQuickTrade = async params => {
  const {
    orderModel,
    tradeAccount,
    tradeSymbol,
    openOrders,
    tradeQuote,
  } = params;

  if (tradeAccount === undefined) {
    console.log('Trade Account not found');
    return;
  }

  try {
    const finalModel = {
      symbol: orderModel.symbol || tradeSymbol.currency,
      actionType: orderModel.actionType,
      takeProfit: orderModel.takeProfit,
      stopLoss: orderModel.stopLoss,
      volume: orderModel.volume
    };

    orderModel.openPrice = 0;
    if (orderModel.type == 'buy' || orderModel.type == 'secure') {
      finalModel.actionType = 'Buy';
      orderModel.actionType = 'Buy';
    } else if (orderModel.type == 'sell' || orderModel.type == 'secure') {
      finalModel.actionType = 'Sell';
      orderModel.actionType = 'Sell';
    }

    const curr_price = _getOpenPrice(orderModel, tradeQuote);

    if (orderModel.useTakeProfit) {
      finalModel.takeProfit =
        orderModel.type == 'buy'
          ? curr_price + orderModel.takeProfit * orderModel.gridSize
          : curr_price - orderModel.takeProfit * orderModel.gridSize;
    }

    if (orderModel.useStopLoss) {
      finalModel.stopLoss =
        orderModel.type == 'buy'
          ? curr_price - orderModel.stopLoss * orderModel.gridSize
          : curr_price + orderModel.stopLoss * orderModel.gridSize;
    }

    if (orderModel.type == 'buy' || orderModel.type == 'sell') {
      if (orderModel.useDynamicMode) {
        orderModel.positionCount = orderModel.takeProfit;
      }
      
      for (let j = 0; j < orderModel.positionCount; j++) {
        if (orderModel.useDynamicMode) {
          finalModel.takeProfit =
            orderModel.type === 'buy'
              ? curr_price + (j + 1) * orderModel.gridSize
              : curr_price - (j + 1) * orderModel.gridSize;
        }
        _sendOrderImpl(finalModel);
      }
      
    }

    if (orderModel.type == 'secure') {
      
      let symbolPositions = openOrders.filter(
        position => position.symbol === tradeSymbol.currency
      );
      if (symbolPositions.length > 0) {
        symbolPositions.forEach(async pos => {
          if (pos.symbol === tradeSymbol.currency && pos.profit > 0) {
            const finalModel = {
              takeProfit: pos.takeProfit,
              stopLoss:
                pos.orderType == 'Buy'
                  ? pos.openPrice + orderModel.profitPoints
                  : pos.openPrice - orderModel.profitPoints,
              ticket: pos.ticket
            };
            modifyOrderImpl(finalModel);
          }
        });
      }
      
    }
  } catch (error) {
    console.error(error);
  }
};

const _onPlaceOrder = async params => {
  const {
    orderModel,
    tradeAccount,
    tradeSymbol,
    tradeQuote,
    sendOrderImpl
  } = params;

  if (tradeAccount === undefined) {
    return;
  }
  try {
    const curr_price = _getOpenPrice(orderModel, tradeQuote);
    const distance =
      curr_price > orderModel.takeProfit
        ? curr_price - orderModel.takeProfit
        : orderModel.takeProfit - curr_price;
    const step = distance / orderModel.positionCount;
    const prices = [];
    const tpPrices = [];
    for (let i = 0; i < orderModel.positionCount; i++) {
      if (orderModel.actionType.toLowerCase().includes('buy')) {
        prices.push(curr_price + (i + 1) * step);
        tpPrices.push(curr_price + (i + 1) * (step * 0.98));
      } else {
        prices.push(curr_price - (i + 1) * step);
        tpPrices.push(curr_price - (i + 1) * (step * 0.98));
      }
    }

    const finalModel = {
      symbol: tradeSymbol.currency,
      actionType: orderModel.actionType,
      takeProfit: orderModel.takeProfit,
      stopLoss: orderModel.stopLoss,
      volume: orderModel.volume,
      price: 0
    };

    if (orderModel.executionStyle === 'pending') {
      finalModel['openPrice'] = _getOpenPrice(orderModel, tradeQuote);
      finalModel['price'] = finalModel.openPrice;
    }

    for (let j = 0; j < orderModel.positionCount; j++) {
      if (
        orderModel.tradingMode === 'normal' ||
        orderModel.executionStyle === 'instant'
      ) {
        if (orderModel.tpStyle === 'fixed') {
          sendOrderImpl(finalModel);
        } else if (orderModel.tpStyle === 'dynamic') {
          finalModel.takeProfit = tpPrices[j];
          sendOrderImpl(finalModel);
        }
      }

      if (orderModel.tradingMode === 'advanced') {
        finalModel.openPrice = j == 0 ? finalModel.openPrice : prices[j - 1];
        if (orderModel.tpStyle === 'fixed') {
          sendOrderImpl(finalModel);
        } else if (orderModel.tpStyle === 'dynamic') {
          finalModel.takeProfit = tpPrices[j];
          sendOrderImpl(finalModel);
        }
      }

      if (orderModel.tradingMode === 'grid') {
        finalModel.openPrice = j == 0 ? finalModel.openPrice : prices[j - 1];
        if (orderModel.tpStyle === 'fixed') {
          if (!isEven(j + 1)) {
            finalModel.takeProfit = prices[j];
            sendOrderImpl(finalModel);
          }
        } else if (orderModel.tpStyle === 'dynamic') {
          finalModel.takeProfit = prices[j];
          sendOrderImpl(finalModel);
        }
      }
      
    }
  } catch (error) {
    console.error(error);
  }
};

const getHedgedOrder = async params => {
  const { id, order } = params;

  const finalModel = {
    id,
    volume: order.lots,
    stopLoss: order.openPrice,
    slippage: 15000,
    actionType: order.orderType === 'Buy' ? 'Sell' : 'Buy',
    symbol: order.symbol,
    comment: 'HT-'+order.ticket,
    openPrice: 0,
    takeProfit: 0
  };

  const _finalModel = {
      symbol: finalModel.symbol,
      actionType: finalModel.actionType,
      volume: finalModel.volume,
      openPrice: finalModel.openPrice,
      slippage: 1000,
      stopLoss: finalModel?.stopLoss,
      takeProfit: finalModel?.takeProfit,
      comment: finalModel.comment,
      expertID: 0,
      stopLimitPrice: 0,
      placedType: 'Manually'
  };

  return _finalModel;
}



const secureTrades = async params => {
  const {
    id,
    bot_id
  } = params;

  try {
      const bot = await findBotById(bot_id);
      //const bot = data;
      //console.log('Bot found:', bot);
      const ordersData = await openedOrders(id);
      //console.log('Opened orders:', ordersData[0]);
      let trades = ordersData;
      //console.log('Securing trades:', trades.length);
      if(!trades || trades.length === 0) return;
      const symbolTrades = trades.filter(trade => trade.symbol === bot.symbol);
      //console.log('Securing trades for symbol:', bot.symbol, ':', symbolTrades.length);
      for(let trade of symbolTrades) {
          if(trade.profit > 0 && trade.stopLoss === 0) {
            if(bot.secure_grid_size === 0) bot.secure_grid_size = 1;
            let delta = bot.secure_grid_size * bot.grid_size;
            let sl = 0;
              if(trade.orderType === 'Buy') {
                  sl = trade.openPrice + delta;
              } else {
                  sl = trade.openPrice - delta;
              }
              try {
                //console.log('Open Price ', trade.openPrice, ' SL ', sl, 'TP ', trade.takeProfit, ' Ticket ', trade.ticket, ' Order Type ', trade.orderType);
                await orderModify(id, trade.ticket, sl, trade.takeProfit, 0);
              } catch (error) {
                  console.log('Error modifying order:', error.message);
              }
          }
      }
  } catch (error) {
    console.log('Error securing trades:', error.message);
  }

}


const openRangePendingOrder = async params => {
  const {symbol, orderType, openPrice, endPrice, positionCount, tradeAccountToken, volume} = params.order;
  let prices = getPriceList(openPrice, endPrice, positionCount);
  console.log('Prices ', prices);
  const requests = [];
  try {
      for(let price of prices) {
        requests.push(orderSend(tradeAccountToken, symbol, orderType, volume, price, 10000, 0, 0, '', 0, 0, 'Manually' ));
      }
      await Promise.all(requests);
  } catch (error) {
    console.error('Error sending order:', error.message);
  }
}


const updateOrderTakeProfitByTicket = async params => {
  const {ticket, tradeAccountToken, profit} = params;
  try {
    const openOrders = await openedOrders(tradeAccountToken);
    const order = openOrders.find(order => order.ticket === ticket);
    if(!order) return;

    let tickValue = 0;
    let tickSize = 0;

    let ticValueTickSizeParam = await getTickValueAndSize({symbol: order.symbol});  
    if(ticValueTickSizeParam !== null) {
      tickValue = ticValueTickSizeParam.tickValue;
      tickSize = ticValueTickSizeParam.tickSize;
    } else {
      const symbol_params = await symbolParams(tradeAccountToken, order.symbol);
      if(!symbol_params) return;
      const { symbolInfo } = symbol_params;
      if(!symbolInfo) return;
      tickValue = symbolInfo.tickValue;
      tickSize = symbolInfo.tickSize;
    }

    if(tickValue === 0 || tickSize === 0) return;
        
    const quote = await getQuote(tradeAccountToken, order.symbol);
    if(!quote) return;
    
    const spread = Math.abs(quote.ask - quote.bid);
    const takeProfit = calculateTakeProfitImpl(order.openPrice, order.lots, tickValue, tickSize, profit, order.orderType, spread);
    await orderModify(tradeAccountToken, ticket, order.stopLoss, takeProfit, 0);
  } catch (error) {
    console.log('Error updating order take profit:', error.message);
  }
}


const updateOrderStopLossByTicket = async params => {
  const {ticket, tradeAccountToken, loss} = params;
  try {
    const openOrders = await openedOrders(tradeAccountToken);
    const order = openOrders.find(order => order.ticket === ticket);
    if(!order) return;

    let tickValue = 0;
    let tickSize = 0;

    let ticValueTickSizeParam = await getTickValueAndSize({symbol: order.symbol});  
    if(ticValueTickSizeParam !== null) {
      tickValue = ticValueTickSizeParam.tickValue;
      tickSize = ticValueTickSizeParam.tickSize;
    } else {
      const symbol_params = await symbolParams(tradeAccountToken, order.symbol);
      if(!symbol_params) return;
      const { symbolInfo } = symbol_params;
      if(!symbolInfo) return;
      tickValue = symbolInfo.tickValue;
      tickSize = symbolInfo.tickSize;
    }

    if(tickValue === 0 || tickSize === 0) return;
        
    const quote = await getQuote(tradeAccountToken, order.symbol);
    if(!quote) return;
    
    const spread = Math.abs(quote.ask - quote.bid);
    const stopLoss = calculateStopLossImpl(order.openPrice, order.lots, tickValue, tickSize, loss, order.orderType, spread);
    await orderModify(tradeAccountToken, ticket, stopLoss, order.takeProfit, 0);
  } catch (error) {
    console.log('Error updating order stop loss:', error.message);
  }
}


const secureOrderTakeProfitByTicket = async params => {
  const {ticket, tradeAccountToken, profit} = params;
  try {
    const openOrders = await openedOrders(tradeAccountToken);
    const order = openOrders.find(order => order.ticket === ticket);
    
    if(!order) return;

    let quote = null;
    try {
      quote = await getQuote(tradeAccountToken, order.symbol);
    } catch (error) {
      console.log('Error getting quote:', error.message);
      return;
    }

    if (!quote) return;

    let tvParam = await getTickValueAndSize({symbol: order.symbol});
    if(tvParam === null) return;
    let tickValue = tvParam.tickValue;
    let tickSize = tvParam.tickSize;

    if(tickValue === 0 || tickSize === 0) {
      const symbol_params = await symbolParams(tradeAccountToken, order.symbol);
      if (!symbol_params) return;
      const { symbolGroup, symbolInfo } = symbol_params;
      if (!symbolGroup || !symbolInfo) return;
      tickValue = symbolInfo.tickValue;
      tickSize = symbolInfo.tickSize;
    }

    
    const spread = Math.abs(quote.ask - quote.bid);
    const stopLoss = calculateTakeProfitImpl(order.openPrice, order.lots, tickValue, tickSize, profit, order.orderType, spread);
    if(isNaN(stopLoss)) return;
    await orderModify(tradeAccountToken, ticket, stopLoss, order.takeProfit, 0);
  } catch (error) {
    console.log('Error updating order take profit:', error.message);
  }
}


const updateOrderTakeProfitBySymbol = async params => {
  const {symbol, tradeAccountToken, profit, startRange, endRange, forceUpdate, orderType} = params;
  try {
    const openOrders = await openedOrders(tradeAccountToken);
    let orders = [];
    
    if(startRange === 0 || endRange === 0) {
      orders = openOrders.filter(order => order.symbol === symbol && (forceUpdate || order.takeProfit === 0) && order.orderType === orderType);
    } else {
      orders = openOrders.filter(order => order.symbol === symbol && priceIsBetween(order.openPrice, startRange, endRange) && (forceUpdate || order.takeProfit === 0) && 
      order.orderType === orderType);
    }
    const requests = [];
    for(let order of orders) {
      requests.push(updateOrderTakeProfitByTicket({ticket: order.ticket, tradeAccountToken, profit}).catch(error => {
        console.log('Error updating order take profit by ticket:', error.message);
      }));
    }
    await Promise.all(requests);
  } catch (error) {
    console.log('Error updating order take profit by symbol:', error.message);
    throw new Error('Error updating order take profit by symbol:', error.message);
  }
}


const updateOrderStopLossBySymbol = async params => {
  const {symbol, tradeAccountToken, loss, startRange, endRange, forceUpdate, orderType} = params;
  try {
    const openOrders = await openedOrders(tradeAccountToken);
    const orders = [];
    if(startRange === 0 || endRange === 0) {
      orders = openOrders.filter(order => order.symbol === symbol && (forceUpdate || order.stopLoss === 0) && order.orderType === orderType);
    } else {
      orders = openOrders.filter(order => order.symbol === symbol && priceIsBetween(order.openPrice, startRange, endRange) && (forceUpdate || order.stopLoss === 0) && 
      order.orderType === orderType);
    }
    const requests = [];
    for(let order of orders) {
      requests.push(updateOrderStopLossByTicket({ticket: order.ticket, tradeAccountToken, loss}));
    }
  } catch (error) {
    console.log('Error updating order stop loss by symbol:', error.message);
  }
} 


const secureOrderTakeProfitBySymbol = async params => {
  const {symbol, tradeAccountToken, profit, executionProfit, orderType} = params;
  try {
    const openOrders = await openedOrders(tradeAccountToken);
    const orders = openOrders.filter(order => order.symbol === symbol && (executionProfit > profit ?  order.profit >= executionProfit : order.profit >= profit) && order.orderType === orderType);
    const requests = [];
    for(let order of orders) {
      requests.push(secureOrderTakeProfitByTicket({ticket: order.ticket, tradeAccountToken, profit}));
    }
    await Promise.all(requests);
  } catch (error) {
    console.log('Error updating order take profit by symbol:', error.message);
  }
}


const placeOrderWithProfitTarget = async params => {
    const {symbol, tradeAccountToken, profit, loss, positionCount, positionsPerGrid, orderType, volume, tpStyle, comment} = params;
    let quote = null;
    let indicatorValues = null;
    try {
      quote = await getQuote(tradeAccountToken, symbol);
      indicatorValues = await findIndicatorValuesBySymbol(symbol);
    } catch (error) {
      console.log('Error getting quote:', error.message);
      return;
    }

    if(!quote) return;
    if(!indicatorValues || indicatorValues.length === 0) return;

    let openPrice = 0;
    if(orderType === 'Buy') {
      openPrice = quote.ask;
    } else {
      openPrice = quote.bid;
    }

    const { tick_value, tick_size } = indicatorValues[0];
    const spread = Math.abs(quote.ask - quote.bid);

    const requests = [];
    for(let i = 0; i < positionCount; i++) {
      let multiplier = tpStyle === 'fixed' ? 1 : i+1;
      let takeProfit = calculateTakeProfitImpl(openPrice, volume, tick_value, tick_size, profit, orderType, spread, multiplier);
      let stopLoss = loss > 0 ? calculateStopLossImpl(openPrice, volume, tick_value, tick_size, loss, orderType, spread) : 0;
      for(let j = 0; j < positionsPerGrid; j++) {
        requests.push(orderSend(tradeAccountToken, symbol, orderType, volume, 0, 10000, stopLoss, takeProfit, comment, 0, 0, 'Manually'));
      }
    }
    await Promise.all(requests);
}



const placePendingOrderWithProfitTarget = async params => {
  const {symbol, tradeAccountToken, profit, loss, spreadMultiplier, positionCount, positionsPerGrid, orderType, volume} = params;
  let quote = null;
  let indicatorValues = null;
  try {
    quote = await getQuote(tradeAccountToken, symbol);
    indicatorValues = await findIndicatorValuesBySymbol(symbol);
  } catch (error) {
    console.log('Error getting quote:', error.message);
    throw new Error('Error getting quote:', 'Unsupported symbol');
  }

  if (!quote) throw new Error('Error getting quote:', 'Network Error, Please try again later');
  if (!indicatorValues || indicatorValues.length === 0) throw new Error('Error getting indicator values:', 'Unsupported symbol');

  const { tick_value, tick_size } = indicatorValues[0];
  const spread = Math.abs(quote.ask - quote.bid);

  let openPrice = 0;
  if ((orderType === 'Buy' || orderType === 'BuyStop')) {
    openPrice = quote.ask + (spread * spreadMultiplier);
  } else if(openPrice === 0 && (orderType === 'Sell' || orderType === 'SellStop')) {
    openPrice = quote.bid - (spread * spreadMultiplier);
  }

  if (orderType === 'BuyLimit') {
    openPrice = quote.ask - (spread * spreadMultiplier);
  } else if (orderType === 'SellLimit') {
    openPrice = quote.bid + (spread * spreadMultiplier);
  }

  let tpPriceChange = await calculatePriceChange({tradeAccountToken, lotSize: volume, symbol, amount: profit}); 
  //calculateTakeProfitImpl(openPrice, volume, tick_value, tick_size, profit, orderType, spread);
  let slPriceChange = loss > 0 ? await calculatePriceChange({tradeAccountToken, lotSize: volume, symbol, amount: loss}) : 0; 
  //calculateStopLossImpl(openPrice, volume, tick_value, tick_size, loss, orderType, spread) : 0;


  let tp = orderType === 'Buy' || orderType === 'BuyStop' || orderType === 'BuyLimit' ? openPrice + tpPriceChange : openPrice - tpPriceChange;
  let sl = orderType === 'Sell' || orderType === 'SellStop' || orderType === 'SellLimit' ? openPrice + slPriceChange : openPrice - slPriceChange;

  const requests = [];
  for(let i = 0; i < positionCount; i++) {
    for(let j = 0; j < positionsPerGrid; j++) {
      requests.push(orderSend(tradeAccountToken, symbol, orderType, volume, openPrice, 10000, sl, tp, '', 0, 0, 'Manually'));
    }
  }
  await Promise.all(requests);
}


const placeGapPendingOrderWithProfitTarget = async params => {
  const {symbol, tradeAccountToken, profit, loss, positionCount, orderType, volume, gapAmount, positionsPerGrid, isDynamic, openPrice, endPrice, comment} = params;
  if(isDynamic) return await placeDynamicGapPendingOrderWithProfitTarget(params);

  let quote = null;
  try {
    quote = await getQuote(tradeAccountToken, symbol);
  } catch (error) {
    console.log('Error getting quote:', error.message);
    throw new Error('Error getting quote:', 'Unsupported symbol');
  }

  if (!quote) throw new Error('Error getting quote:', 'Network Error, Please try again later');

  const spread = Math.abs(quote.ask - quote.bid);

  const gap = endPrice === 0 ? await calculatePriceChange({tradeAccountToken, lotSize: volume, symbol, amount: gapAmount}) : Math.abs(endPrice - openPrice)/positionCount;
  const profitChange = await calculatePriceChange({tradeAccountToken, lotSize: volume, symbol, amount: profit});
  const lossChange = loss > 0 ? await calculatePriceChange({tradeAccountToken, lotSize: volume, symbol, amount: loss}) : 0;
  let prices = [];
  let takeProfits = [];
  if (orderType === 'Buy' || orderType === 'BuyStop') {
    for(let i = 0; i < positionCount; i++) {
      let op = openPrice === 0 ? quote.ask : openPrice;
      let p = op + (gap * i) + spread;
      prices.push(p);
      takeProfits.push(p + profitChange);
    }
  } else if(orderType === 'Sell' || orderType === 'SellStop') {
    for(let i = 0; i < positionCount; i++) {
      let op = openPrice === 0 ? quote.bid : openPrice;
      let p = op - (gap * i) - spread;
      prices.push(p);
      takeProfits.push(p - profitChange);
    }
  }

  if (orderType === 'BuyLimit') {
    for(let i = 0; i < positionCount; i++) {
      let op = openPrice === 0 ? quote.ask : openPrice;
      let p = op - (gap * i) - spread;
      prices.push(p);
      takeProfits.push(p + profitChange);
    }
  } else if(orderType === 'SellLimit') {
    for(let i = 0; i < positionCount; i++) {
      let op = openPrice === 0 ? quote.bid  : openPrice;
      let p = op + (gap * i) + spread;
      prices.push(p);
      takeProfits.push(p - profitChange);
    }
  }

  const requests = [];
  for(let i = 0; i < positionCount; i++) {
    for(let j = 0; j < positionsPerGrid; j++) {
      let stopLoss = 0;
      if(lossChange > 0 && (orderType === 'Buy' || orderType === 'BuyStop' || orderType === 'BuyLimit')) {
        stopLoss = prices[i] - lossChange;
      } else if(lossChange > 0 && (orderType === 'Sell' || orderType === 'SellStop' || orderType === 'SellLimit')) {
        stopLoss = prices[i] + lossChange;
      }
      requests.push(orderSend(tradeAccountToken, symbol, orderType, volume, prices[i], 10000, stopLoss, takeProfits[i], comment, 0, 0, 'Manually'));
    }
  }
  await Promise.all(requests);
}


const placeDynamicGapPendingOrderWithProfitTarget = async params => {
  const {symbol, tradeAccountToken, profit, loss, positionCount, orderType, volume, gapAmount, positionsPerGrid, openPrice, endPrice, comment} = params;
  let quote = null;
  try {
    quote = await getQuote(tradeAccountToken, symbol);
  } catch (error) {
    console.log('Error getting quote:', error.message);
    throw new Error('Error getting quote:', 'Unsupported symbol');
  }

  if (!quote) throw new Error('Error getting quote:', 'Network Error, Please try again later');

  const spread = Math.abs(quote.ask - quote.bid);

  const gap =  endPrice === 0 ? await calculatePriceChange({tradeAccountToken, lotSize: volume, symbol, amount: gapAmount}) : Math.abs(endPrice - openPrice)/positionCount;
  const profitChange = await calculatePriceChange({tradeAccountToken, lotSize: volume, symbol, amount: profit});
  const lossChange = loss > 0 ? await calculatePriceChange({tradeAccountToken, lotSize: volume, symbol, amount: loss}) : 0;
  let _positionCount = positionCount === 0 ? Math.round(profitChange / gap) : positionCount;

  let prices = [];
  let takeProfits = [];
  let currentPrice = openPrice === 0 ? (orderType === 'Buy' || orderType === 'BuyStop' || orderType === 'BuyLimit' ? quote.ask : quote.bid) : openPrice;
  
  if (orderType === 'Buy' || orderType === 'BuyStop') {
    for(let i = 0; i < _positionCount; i++) {
      let p = currentPrice + (gap * i) + spread;
      let tp = p + (profitChange  * i);
      prices.push(p);
      takeProfits.push(tp);
    }
  } else if(orderType === 'Sell' || orderType === 'SellStop') {
    for(let i = 0; i < _positionCount; i++) {
      let p = currentPrice  - (gap * i) - spread;
      let tp = p - (profitChange  * i);
      prices.push(p);
      takeProfits.push(tp);
    }
  }

  if (orderType === 'BuyLimit') {
    for(let i = 0; i < _positionCount; i++) {
      let p = currentPrice - (gap * i) - spread;
      let tp = p + (profitChange  * i);
      prices.push(p);
      takeProfits.push(tp);
    }
  } else if(orderType === 'SellLimit') {
    for(let i = 0; i < _positionCount; i++) {
      let p = currentPrice + (gap * i) + spread;
      let tp = p - (profitChange  * i);
      prices.push(p);
      takeProfits.push(tp);
    }
  }

  const requests = [];
  for(let i = 0; i < _positionCount; i++) {
    for(let j = 0; j < positionsPerGrid; j++) {
      let stopLoss = 0;
      if(lossChange > 0 && (orderType === 'Buy' || orderType === 'BuyStop' || orderType === 'BuyLimit')) {
        stopLoss = prices[i] - lossChange;
      } else if(lossChange > 0 && (orderType === 'Sell' || orderType === 'SellStop' || orderType === 'SellLimit')) {
        stopLoss = prices[i] + lossChange;
      }
      requests.push(orderSend(tradeAccountToken, symbol, orderType, volume, prices[i], 10000, stopLoss, takeProfits[i], comment, 0, 0, 'Manually'));
    }
  }
  await Promise.all(requests);
}


const placeQuickGapPendingOrderWithProfitTarget = async params => {
  const {symbol, tradeAccountToken, profit, loss, positionCount, orderType, volume, gapAmount, positionsPerGrid, isDynamic, openPrice, endPrice, comment} = params;
  if(isDynamic) return await placeQuickDynamicGapPendingOrderWithProfitTarget(params);

  let quote = null;
  try {
    quote = await getQuote(tradeAccountToken, symbol);
  } catch (error) {
    console.log('Error getting quote:', error.message);
    throw new Error('Error getting quote:', 'Unsupported symbol');
  }

  if (!quote) throw new Error('Error getting quote:', 'Network Error, Please try again later');

  const spread = Math.abs(quote.ask - quote.bid);

  const gap = await calculatePriceChange({tradeAccountToken, lotSize: volume, symbol, amount: gapAmount});
  const profitChange = endPrice === 0 ? await calculatePriceChange({tradeAccountToken, lotSize: volume, symbol, amount: profit}) : Math.abs(endPrice - openPrice)/positionCount;
  const lossChange = loss > 0 ? await calculatePriceChange({tradeAccountToken, lotSize: volume, symbol, amount: loss}) : 0;
  const profitAmount = await calculatePriceChangeAmount({tradeAccountToken, lotSize: volume, symbol, priceChange: profitChange});
  let _comment = comment;
  if(comment === '' || comment === null || comment === undefined) {
    _comment = `QT-${profitAmount}-0`;
  }

  let prices = [];
  if (orderType === 'Buy' || orderType === 'BuyStop') {
    for(let i = 0; i < positionCount; i++) {
      let op = openPrice === 0 ? quote.ask : openPrice;
      let p = op + spread + (gap * i);
      prices.push(p);
    }
  } else if(orderType === 'Sell' || orderType === 'SellStop') {
    for(let i = 0; i < positionCount; i++) {
      let op = openPrice === 0 ? quote.bid : openPrice;
      let p = op - spread - (gap * i);
      prices.push(p);
    }
  }

  if (orderType === 'BuyLimit') {
    for(let i = 0; i < positionCount; i++) {
      let op = openPrice === 0 ? quote.ask - spread : openPrice;
      let p = op - (gap * i);
      prices.push(p);
    }
  } else if(orderType === 'SellLimit') {
    for(let i = 0; i < positionCount; i++) {
      let op = openPrice === 0 ? quote.bid + spread : openPrice;
      let p = op + (gap * i);
      prices.push(p);
    }
  }

  const requests = [];
  for(let i = 0; i < positionCount; i++) {
    for(let j = 0; j < positionsPerGrid; j++) {
      let stopLoss = 0;
      if(lossChange > 0 && (orderType === 'Buy' || orderType === 'BuyStop' || orderType === 'BuyLimit')) {
        stopLoss = prices[i] - lossChange;
      } else if(lossChange > 0 && (orderType === 'Sell' || orderType === 'SellStop' || orderType === 'SellLimit')) {
        stopLoss = prices[i] + lossChange;
      }
      requests.push(orderSend(tradeAccountToken, symbol, orderType, volume, prices[i], 10000, stopLoss, 0, _comment, 0, 0, 'Manually'));
    }
  }
  await Promise.all(requests);
}


const placeQuickDynamicGapPendingOrderWithProfitTarget = async params => {
  const {symbol, tradeAccountToken, profit, loss, positionCount, orderType, volume, gapAmount, positionsPerGrid, openPrice, endPrice, comment} = params;
  let quote = null;
  try {
    quote = await getQuote(tradeAccountToken, symbol);
  } catch (error) {
    console.log('Error getting quote:', error.message);
    throw new Error('Error getting quote:', 'Unsupported symbol');
  }

  if (!quote) throw new Error('Error getting quote:', 'Network Error, Please try again later');

  const spread = Math.abs(quote.ask - quote.bid);

  const gap = endPrice === 0 ? await calculatePriceChange({tradeAccountToken, lotSize: volume, symbol, amount: gapAmount}) : Math.abs(endPrice - openPrice)/positionCount;
  const lossChange = loss > 0 ? await calculatePriceChange({tradeAccountToken, lotSize: volume, symbol, amount: loss}) : 0;
  const profitChange = await calculatePriceChange({tradeAccountToken, lotSize: volume, symbol, amount: profit});
  let _positionCount = positionCount === 0 ? Math.round(profitChange / gap) : positionCount;

  let prices = [];
  let comments = [];
  let currentPrice = openPrice === 0 ? (orderType === 'BuyStop' || orderType === 'BuyLimit' ? quote.ask : quote.bid) : openPrice;
  
  if (orderType === 'Buy' || orderType === 'BuyStop') {
    for(let i = 1; i < _positionCount; i++) {
      let p = currentPrice + spread + (gap * i);
      let priceChange = spread + (gap * i);
      let profitAmount = await calculatePriceChangeAmount({tradeAccountToken, lotSize: volume, symbol, priceChange});
      let _comment = `QT-${Math.round(profitAmount)}-0`;
      if(comment === '' || comment === null || comment === undefined) {
        comments.push(_comment);
      } else {
        comments.push(comment);
      }
      prices.push(p);
    }
  } else if(orderType === 'Sell' || orderType === 'SellStop') {
    for(let i = 0; i < _positionCount; i++) {
      let p = currentPrice - spread - (gap * i);
      let priceChange = spread + (gap * i);
      let profitAmount = await calculatePriceChangeAmount({tradeAccountToken, lotSize: volume, symbol, priceChange});
      let _comment = `QT-${Math.round(profitAmount)}-0`;
      if(comment === '' || comment === null || comment === undefined) {
        comments.push(_comment);
      } else {
        comments.push(comment);
      }
      prices.push(p);
    }
  }

  if (orderType === 'BuyLimit') {
    for(let i = 0; i < _positionCount; i++) {
      let p = currentPrice - spread - (gap * i);
      let priceChange = spread + (gap * i);
      let profitAmount = await calculatePriceChangeAmount({tradeAccountToken, lotSize: volume, symbol, priceChange});
      let _comment = `QT-${Math.round(profitAmount)}-0`;
      if(comment === '' || comment === null || comment === undefined) {
        comments.push(_comment);
      } else {
        comments.push(comment);
      }
      prices.push(p);
    }
  } else if(orderType === 'SellLimit') {
    for(let i = 0; i < _positionCount; i++) {
      let p = currentPrice + spread + (gap * i);
      let priceChange = spread + (gap * i);
      let profitAmount = await calculatePriceChangeAmount({tradeAccountToken, lotSize: volume, symbol, priceChange});
      let _comment = `QT-${Math.round(profitAmount)}-0`;
      if(comment === '' || comment === null || comment === undefined) {
        comments.push(_comment);
      } else {
        comments.push(comment);
      }
      prices.push(p);
    }
  }

  const requests = [];
  for(let i = 0; i < _positionCount; i++) {
    for(let j = 0; j < positionsPerGrid; j++) {
      let stopLoss = 0;
      if(lossChange > 0 && (orderType === 'Buy' || orderType === 'BuyStop' || orderType === 'BuyLimit')) {
        stopLoss = prices[i] - lossChange;
      } else if(lossChange > 0 && (orderType === 'Sell' || orderType === 'SellStop' || orderType === 'SellLimit')) {
        stopLoss = prices[i] + lossChange;
      }
      requests.push(orderSend(tradeAccountToken, symbol, orderType, volume, prices[i], 10000, stopLoss, 0, comments[i], 0, 0, 'Manually'));
    }
  }
  await Promise.all(requests);
}


const placeAdvancePendingOrderWithProfitTarget = async params => {
  const {symbol, tradeAccountToken, profitStart, profitEnd, loss, positionCount, orderType, volume, gapAmount, positionsPerGrid, comment} = params;
  let quote = null;
  try {
    quote = await getQuote(tradeAccountToken, symbol);
  } catch (error) {
    console.log('Error getting quote:', error.message);
    throw new Error('Error getting quote:', 'Unsupported symbol');
  }

  if (!quote) throw new Error('Error getting quote:', 'Network Error, Please try again later');

  const spread = Math.abs(quote.ask - quote.bid);

  const gap = await calculatePriceChange({tradeAccountToken, lotSize: volume, symbol, amount: gapAmount});
  const profitChange = await calculatePriceChange({tradeAccountToken, lotSize: volume, symbol, amount: profitStart});
  let stopLossChange = loss > 0 ? await calculatePriceChange({tradeAccountToken, lotSize: volume, symbol, amount: loss}) : 0;
  let _positionCount = positionCount === 0 ? Math.round(profitChange / gap) : positionCount;
  let profitGap = Math.abs(profitEnd - profitStart)/positionsPerGrid;
  const profitGapChange = await calculatePriceChange({tradeAccountToken, lotSize: volume, symbol, amount: profitGap});

  let prices = [];
  let currentPrice = (orderType === 'BuyStop' || orderType === 'BuyLimit' ? quote.ask : quote.bid);
  
  if (orderType === 'Buy' || orderType === 'BuyStop') {
    for(let i = 1; i < _positionCount; i++) {
      let p = currentPrice + spread + (gap * i);
      prices.push(p);
    }
  } else if(orderType === 'Sell' || orderType === 'SellStop') {
    for(let i = 1; i < _positionCount; i++) {
      let p = currentPrice - spread - (gap * i);
      prices.push(p);
    }
  }

  if (orderType === 'BuyLimit') {
    for(let i = 1; i < _positionCount; i++) {
      let p = currentPrice - spread - (gap * i);
      prices.push(p);
    }
  } else if(orderType === 'SellLimit') {
    for(let i = 1; i < _positionCount; i++) {
      let p = currentPrice + spread + (gap * i);
      prices.push(p);
    }
  }

  const requests = [];
  for(let i = 0; i < _positionCount; i++) {
    for(let j = 0; j < positionsPerGrid; j++) {
      let takeProfit = 0;
      let stopLoss = 0;
      if(stopLossChange > 0 && (orderType === 'Buy' || orderType === 'BuyStop' || orderType === 'BuyLimit')) {
        takeProfit = prices[i] + profitChange + (profitGapChange * (i));
        stopLoss = prices[i] - stopLossChange;
      } else if(stopLossChange > 0 && (orderType === 'Sell' || orderType === 'SellStop' || orderType === 'SellLimit')) {
        takeProfit = prices[i] - profitChange - (profitGapChange * (i));
        stopLoss = prices[i] + stopLossChange;
      }
      requests.push(orderSend(tradeAccountToken, symbol, orderType, volume, prices[i], 10000, stopLoss, takeProfit, comment, 0, 0, 'Manually'));
    }
  }
  await Promise.all(requests);
}


const calculateTakeProfit = async params => {
  const { tradeAccountToken, symbol, lotSize, profitTarget, orderType} = params;
  const quote = await getQuote(tradeAccountToken, symbol);
  if(!quote) return;
  const spread = Math.abs(quote.ask - quote.bid);
  const openPrice = orderType === 'Buy' ? quote.ask : quote.bid;
  const symbol_params = await symbolParams(tradeAccountToken, symbol);
  if(!symbol_params) return;
  const { symbolInfo } = symbol_params;
  if(!symbolInfo) return;
  const { tickSize, tickValue } = symbolInfo;
  if(tickValue === 0 || tickSize === 0) {
    const tick_params = await getTickValueAndSize({symbol});
    if(tick_params === null) return null;
    const {tickValue, tickSize} = tick_params;
    return calculateTakeProfitImpl(openPrice, lotSize, tickValue, tickSize, profitTarget, orderType, spread);
  }
  return calculateTakeProfitImpl(openPrice, lotSize, tickValue, tickSize, profitTarget, orderType, spread);
}


const calculateStopLoss = async params => {
  const { tradeAccountToken, symbol, lotSize, lossTarget, orderType} = params;
  const quote = await getQuote(tradeAccountToken, symbol);
  if(!quote) return;
  const spread = Math.abs(quote.ask - quote.bid);
  const openPrice = orderType === 'Buy' ? quote.ask : quote.bid;
  const symbol_params = await symbolParams(tradeAccountToken, symbol);
  if(!symbol_params) return;
  const { symbolInfo } = symbol_params;
  if(!symbolInfo) return;
  const { tickSize, tickValue } = symbolInfo;
  if(tickValue === 0 || tickSize === 0) {
    const tick_params = await getTickValueAndSize({symbol});
    if(tick_params === null) return null;
    const {tickValue, tickSize} = tick_params;
    return calculateStopLossImpl(openPrice, lotSize, tickValue, tickSize, lossTarget, orderType, spread);
  }
  return calculateStopLossImpl(openPrice, lotSize, tickValue, tickSize, lossTarget, orderType, spread);
}


const calculatePriceChange = async params => {
  const { tradeAccountToken, lotSize, symbol, amount } = params;
  // const symbol_params = await symbolParams(tradeAccountToken, symbol);
  // if (!symbol_params) return null;
  // const { symbolInfo } = symbol_params;
  // if (symbolInfo === undefined) {
  const tick_params = await getTickValueAndSize({ symbol });
  if (tick_params === null) return null;
  const { tickValue, tickSize } = tick_params;
  return calculatePriceChangeImpl(lotSize, tickValue, tickSize, amount);
  // }
  // return 0;
};


const calculateStopLevel = async params => {
  const { symbol } = params;
  const stopLevelParams = await getStopLevels({symbol});
  if (stopLevelParams === null || stopLevelParams === 0) return null;
  const { stopLevel } = stopLevelParams;
  return stopLevel;
}

const calculatePriceChangeAmount = async params => {
  const { tradeAccountToken, lotSize, symbol, priceChange } = params;

  const tick_params = await getTickValueAndSize({symbol});
  if(tick_params === null) return null;
  let {tickValue, tickSize} = tick_params;
  let priceChangeAmount = calculatePriceChangeAmountImpl(priceChange, tickSize, tickValue, lotSize);
  if (priceChangeAmount !== null) return priceChangeAmount;

  const symbol_params = await symbolParams(tradeAccountToken, symbol);
  console.log('symbol_params ', symbol_params);
  if(symbol_params === null) return null;
  const { symbolInfo } = symbol_params;
  return calculatePriceChangeAmountImpl(priceChange, symbolInfo.tickSize, symbolInfo.tickValue, lotSize);
}


const calculateSpreadAmount = async params => {
  const { tradeAccountToken, symbol, lotSize } = params;
  let quote = null;
  try {
    quote = await getQuote(tradeAccountToken, symbol);
  } catch (error) {
    console.log('Error getting quote:', error.message);
    return 0;
  }
  if(quote === null) return 0;
  const spread = Math.abs(quote.ask - quote.bid);
  return calculatePriceChangeAmount({tradeAccountToken, symbol, lotSize, priceChange: spread});
}


const calculateStopLevelAmount = async params => {
  const { tradeAccountToken, symbol, lotSize } = params;
  let stopLevel = null;
  try {
    stopLevel  = await calculateStopLevel({tradeAccountToken, symbol});
  } catch (error) {
    console.log('Error getting quote:', error.message);
    return 0;
  }
  if(stopLevel === null || stopLevel === 0) return 0;
  // stopLevel = stopLevel/=100;
  return calculatePriceChangeAmount({tradeAccountToken, symbol, lotSize, priceChange: stopLevel});
}


const getTickValueAndSize = async params => {
  const {symbol} = params;
  const indicatorValues = await findIndicatorValuesBySymbol(symbol);
  if(!indicatorValues || indicatorValues.length === 0) return null;
  const {tick_value, tick_size} = indicatorValues[0];
  return {tickValue: tick_value, tickSize: tick_size};
}


const getStopLevels = async params => {
  const {symbol} = params;
  const indicatorValues = await findIndicatorValuesBySymbol(symbol);
  if(!indicatorValues || indicatorValues.length === 0) return null;
  const {minimum_tpsl_distance} = indicatorValues[0];
  return { stopLevel: minimum_tpsl_distance };
}


const closeTrades = async params => {
  const {tradeAccountToken, symbol, orderType, startRange, endRange, maxProfit, maxLoss} = params;
  let openOrders = await openedOrders(tradeAccountToken);
  let _maxLoss = maxLoss * -1;

  openOrders = openOrders.filter(order => order.symbol === symbol);
  
  if(maxProfit > 0) {
    openOrders = openOrders.filter(order => order.profit < maxProfit);
  }
  if(maxLoss > 0) {
    openOrders = openOrders.filter(order => order.profit > _maxLoss);
  }

  if(startRange > 0 && endRange > 0) {
    openOrders = openOrders.filter(order => priceIsBetween(order.openPrice, startRange, endRange));
  }


  let typeOrders = [];
  if(orderType === 'All') {
    typeOrders = openOrders.filter(order => order.symbol === symbol);
  } else if(orderType === 'Buy') {
    typeOrders = openOrders.filter(order => order.orderType === 'Buy');
  } else if(orderType === 'Sell') {
    typeOrders = openOrders.filter(order => order.orderType === 'Sell');
  } else if(orderType === 'BuyLimit') {
    typeOrders = openOrders.filter(order => order.orderType === 'BuyLimit');
  } else if(orderType === 'SellLimit') {
    typeOrders = openOrders.filter(order => order.orderType === 'SellLimit');
  } else if(orderType === 'BuyStop') {
    typeOrders = openOrders.filter(order => order.orderType === 'BuyStop');
  } else if(orderType === 'SellStop') {
    typeOrders = openOrders.filter(order => order.orderType === 'SellStop');
  } else if(orderType === 'Buy_Profit') {
    typeOrders = openOrders.filter(order => order.orderType === 'Buy' && order.profit > 0);
  } else if(orderType === 'Sell_Profit') {
    typeOrders = openOrders.filter(order => order.orderType === 'Sell' && order.profit > 0);
  } else if(orderType === 'Buy_Loss') {
    typeOrders = openOrders.filter(order => order.orderType === 'Buy' && order.profit < 0);
  } else if(orderType === 'Sell_Loss') {
    typeOrders = openOrders.filter(order => order.orderType === 'Sell' && order.profit < 0);
  }
  const requests = [];
  for(let order of typeOrders) {
    requests.push(orderClose(tradeAccountToken, order.ticket, order.lots, 0, 10000));
  }
  await Promise.all(requests);
}


const calculateUnrealizedProfit = async params => {
  const { tradeAccountToken } = params;
  const openOrders = await openedOrders(tradeAccountToken);
  const orderWithTP = openOrders.filter(order => order.takeProfit !== 0 && (order.orderType === 'Buy' || order.orderType === 'Sell'));
  let totalProfit = 0;
  for(let order of orderWithTP) {
    const priceChange = Math.abs(order.takeProfit - order.openPrice);
    const profit = await calculatePriceChangeAmount({ tradeAccountToken, symbol: order.symbol, lotSize: order.lots, priceChange });
    if(profit !== null) {
      totalProfit += profit;
    }
  }
  return totalProfit;
};


const calculateUnrealizedProfitBySymbol = async params => {
  const { tradeAccountToken, symbol } = params;
  const openOrders = await openedOrders(tradeAccountToken);
  const orderWithTP = openOrders.filter(order => order.takeProfit !== 0 && (order.orderType === 'Buy' || order.orderType === 'Sell') && order.symbol === symbol);
  let totalProfit = 0;
  for(let order of orderWithTP) {
    const priceChange = Math.abs(order.takeProfit - order.openPrice);
    const profit = await calculatePriceChangeAmount({ tradeAccountToken, symbol, lotSize: order.lots, priceChange });
    if(profit !== null) {
      totalProfit += profit;
    }
  }
  return totalProfit;
};

const getFullStatistics = async params => {
  const { tradeAccountToken } = params;
  const openOrders = await openedOrders(tradeAccountToken);
  const symbols = await getUniqueSymbols(openOrders);
  let statistics = {};
  for(let symbol of symbols) {
    statistics[symbol] = await getStatisticsBySymbol({tradeAccountToken, symbol});
  }
  statistics.totalProfit = await calculateTotalProfit(openOrders);
  return statistics;
}

const getStatisticsBySymbol = async params => {
  const { tradeAccountToken, symbol } = params;
  const openOrders = await openedOrders(tradeAccountToken);
  const ordersBySymbol = openOrders.filter(order => order.symbol === symbol);
  return getTradeStatistics({openOrders: ordersBySymbol});
}


const getTradeStatistics = async params => {
  const { openOrders } = params;
  let statistics = {};
  statistics.totalTrades = openOrders.length;
  statistics.buyTrades = getCountByType(openOrders, 'Buy');
  statistics.sellTrades = getCountByType(openOrders, 'Sell');
  statistics.buyLimitTrades = getCountByType(openOrders, 'BuyLimit');
  statistics.sellLimitTrades = getCountByType(openOrders, 'SellLimit');
  statistics.buyStopTrades = getCountByType(openOrders, 'BuyStop');
  statistics.sellStopTrades = getCountByType(openOrders, 'SellStop');
  statistics.buyProfitTrades = getCountByCondition(openOrders, order => order.orderType === 'Buy' && order.profit > 0);
  statistics.sellProfitTrades = getCountByCondition(openOrders, order => order.orderType === 'Sell' && order.profit > 0);
  statistics.buyLossTrades = getCountByCondition(openOrders, order => order.orderType === 'Buy' && order.profit < 0);
  statistics.sellLossTrades = getCountByCondition(openOrders, order => order.orderType === 'Sell' && order.profit < 0);
  return statistics;
}


const getCountByType = async (openOrders, orderType) => {
  const count = openOrders.filter(order => order.orderType === orderType).length;
  return count;
}

const calculateTotalProfit = async openOrders => {
  let totalProfit = 0;
  for(let order of openOrders) {
    totalProfit += order.profit - (order.swap + order.commission);
  }
  return totalProfit;
}

const getCountBySymbol = async (openOrders, symbol) => {
  const count = openOrders.filter(order => order.symbol === symbol).length;
  return count;
}

const getCountByCondition = async (openOrders, condition) => {
  const count = openOrders.filter(order => condition(order)).length;
  return count;
}

const getUniqueSymbols = async openOrders => {
  const symbols = openOrders.map(order => order.symbol);
  return [...new Set(symbols)];
}


const getHighestProfitOrderBySymbolAndCount = async (openOrders, symbol, count) => {
  const ordersBySymbol = openOrders.filter(order => order.symbol === symbol && order.profit > 0);
  const sortedOrders = ordersBySymbol.sort((a, b) => b.profit - a.profit);
  return sortedOrders.slice(0, count);
}

const getLowestProfitOrderBySymbolAndCount = async (openOrders, symbol, count) => {
  const ordersBySymbol = openOrders.filter(order => order.symbol === symbol && order.profit > 0);
  const sortedOrders = ordersBySymbol.sort((a, b) => a.profit - b.profit);
  return sortedOrders.slice(0, count);
}

const calculateGridAiPrices = async params => {
  const { symbol, tradeAccountToken, start_price, end_price, gap_amount, take_profit, volume } = params;
  const gap = await calculatePriceChange({tradeAccountToken, lotSize: volume, symbol: mapSymbol(symbol), amount: gap_amount});
  // const profitChange = await calculatePriceChange({tradeAccountToken, lotSize: volume, symbol, amount: take_profit});

  // let quote = null;
  // try {
  //   quote = await getQuote(tradeAccountToken, symbol);
  // } catch (error) {
  //   console.log('Error getting quote:', error.message);
  //   throw new Error('Error getting quote:', 'Unsupported symbol');
  // }
  // const spread = Math.abs(quote.ask - quote.bid);
  const total_distance = Math.abs(end_price - start_price);
  const grid_count = Math.floor(total_distance / gap);
  let start = Math.max(start_price, end_price);
  const grid_prices = [];
  for(let i = 0; i < grid_count; i++) {
    const price = (start - (gap * i));
    grid_prices.push(price);
  }
  console.log('grid_prices ', grid_prices);
  return grid_prices;
}


const createGridAi = async params => {
  try {
    const prices = await calculateGridAiPrices(params);
    const {id, user_id, account_id, symbol, take_profit, trade_mode, is_active, volume, start_price, end_price, gap_amount, positions_per_grid, stop_loss, secure_trades, secure_amount, execution_amount, order_types} = params;

    const final_params = {id, user_id, account_id, symbol, gap_amount, is_active, take_profit, stop_loss, trade_mode, volume, start_price, end_price, positions_per_grid, secure_trades, secure_amount, execution_amount, prices, order_types, position_count: prices.length};

    const grid_ai = await saveGridAi(final_params);
    return grid_ai;
  } catch (error) {
    console.log('Error creating grid ai:', error.message);
    throw new Error('Error creating grid ai:', error.message);
  }
}


const placeSnrPendingOrder = async params => {
  const { symbol, tradeAccountToken, profitPrice, lossPrice, positionCount, positionsPerGrid, startPrice, endPrice, orderType, volume, comment } = params;
  let gap = Math.abs(endPrice - startPrice) / positionCount;
  let prices = [];
  for(let i = 0; i < positionCount; i++) {
    prices.push(startPrice + (gap * i));
  }

  let requests = [];
  for(let price of prices) {
    for(let i = 0; i < positionsPerGrid; i++) {
      requests.push(orderSend(tradeAccountToken, symbol, orderType, volume, price, 10000, lossPrice, profitPrice, comment, 0, 0, 'Manually')
      .catch(error => {console.log('Error placing order:', error.message);}));
    }
  }
  Promise.all(requests);
}


const updateTpSl = async params => {
  const { tradeAccountToken, toAmount, modifyType, orderList } = params;

  try {
    // Prepare all modification requests
    const modificationRequests = orderList.filter(order => order.orderType === 'Buy' || order.orderType === 'Sell').map( async order => {
      const toPriceChange = await calculatePriceChange({tradeAccountToken, lotSize: order.lots, symbol: order.symbol, amount: toAmount});
      const newTP = order.orderType === 'Buy' ? 
        order.openPrice + toPriceChange : 
        order.openPrice - toPriceChange;
      
      const newSL = order.orderType === 'Sell' ? 
        order.openPrice + toPriceChange : 
        order.openPrice - toPriceChange;

      const secureSL = order.orderType === 'Buy' ? 
        order.openPrice + toPriceChange : 
        order.openPrice - toPriceChange;

      return orderModify(
        tradeAccountToken,
        order.ticket,
        modifyType === 'SL' ? newSL : modifyType === 'Secure' ? secureSL : order.stopLoss,
        modifyType === 'TP' ? newTP : order.takeProfit,
        0
      ).catch(error => {
        console.error(`Error modifying order ${order.ticket}:`, error.message);
        return null; // Return null for failed modifications
      });
    });

    // Execute all modifications in parallel
    const results = await Promise.all(modificationRequests);
    
    // Return number of successful modifications
    return results.filter(result => result !== null).length;

  } catch (error) {
    console.error('Error in updateTpSl:', error.message);
    throw error;
  }
};



const calculateProfitLossAmount = async params => {
  const { tradeAccountToken } = params;
  let openOrders = await openedOrders(tradeAccountToken);
  openOrders = openOrders.filter(order => order.orderType === 'Buy' || order.orderType === 'Sell');
  const result = {};
  for(let order of openOrders) {
    const profitChange = order.takeProfit !== 0 ? Math.abs(order.takeProfit - order.openPrice) : 0;
    const lossChange = order.stopLoss !== 0 ? Math.abs(order.stopLoss - order.openPrice) : 0;
    const profit = profitChange !== 0 ? await calculatePriceChangeAmount({ tradeAccountToken, symbol: order.symbol, lotSize: order.lots, priceChange: profitChange }) : 0;
    const loss = lossChange !== 0 ? await calculatePriceChangeAmount({ tradeAccountToken, symbol: order.symbol, lotSize: order.lots, priceChange: lossChange }) : 0;
    result[`${tradeAccountToken}-${order.symbol}-${order.ticket}-profit`] = profit;
    result[`${tradeAccountToken}-${order.symbol}-${order.ticket}-loss`] = loss;
  }
  return result;
}




module.exports = {
  _sendOrderImpl,
  _modifyOrderImpl,
  _closeOrderImpl,
  _getOpenPrice,
  _onUpdateOrder,
  _onPlaceQuickPendingTrade,
  _onPlaceQuickTrade,
  _onPlaceOrder,
  getHedgedOrder,
  secureTrades,
  openRangePendingOrder,
  updateOrderTakeProfitBySymbol,
  updateOrderStopLossBySymbol,
  placeOrderWithProfitTarget,
  calculateTakeProfit,
  calculateStopLoss,
  calculatePriceChange,
  calculateStopLevel,
  calculatePriceChangeAmount,
  calculateSpreadAmount,
  calculateStopLevelAmount,
  placePendingOrderWithProfitTarget,
  placeGapPendingOrderWithProfitTarget,
  secureOrderTakeProfitBySymbol,
  secureOrderTakeProfitByTicket,
  updateOrderTakeProfitByTicket,
  placeQuickGapPendingOrderWithProfitTarget,
  closeTrades,
  calculateUnrealizedProfit,
  calculateUnrealizedProfitBySymbol,
  placeAdvancePendingOrderWithProfitTarget,
  getHighestProfitOrderBySymbolAndCount,
  getLowestProfitOrderBySymbolAndCount,
  createGridAi,
  placeSnrPendingOrder,
  updateTpSl,
  calculateProfitLossAmount
};
