const { priceHistory } = require('../mt5-service');



class OHLCV {
  constructor(data, symbol) {
    this.time = data.time;
    this.open = data.openPrice;
    this.high = data.highPrice;
    this.low = data.lowPrice;
    this.close = data.closePrice;
    this.volume = data.volume || 1;
    this.symbol = symbol;
  }

  get bodySize() {
    return Math.abs(this.close - this.open);
  }

  get range() {
    return this.high - this.low;
  }

  get upperWick() {
    return this.high - Math.max(this.open, this.close);
  }

  get lowerWick() {
    return Math.min(this.open, this.close) - this.low;
  }

  get isBullish() {
    return this.close > this.open;
  }
}

// ---------------- Data Acquisition ----------------
async function fetchHistoricalDataImpl(
  symbol = 'EURUSD',
  from = '2020-01-01T00:00:00',
  to = '2024-01-01T00:00:00',
  timeframe = 60
) {
  try {
    const data = await priceHistory(
      'fwwvr1q9-2h2r-ko1e-b410-9q6jwxbrb1f2',
      symbol,
      from,
      to,
      timeframe
    );
    if (!data || !Array.isArray(data) || data.length === 0) {
      throw new Error('Invalid or empty historical data received');
    }
    console.log('Received historical data:', {
      length: data.length,
      firstItem: data[0],
      lastItem: data[data.length - 1]
    });
    const ohlcvData = data.map(d => {
      if (!d || typeof d.openPrice === 'undefined') {
        console.error('Invalid data item:', d);
        throw new Error('Invalid data format received');
      }
      return new OHLCV(d, symbol);
    });
    return ohlcvData;
  } catch (error) {
    console.error('Error fetching historical data:', error);
    throw error;
  }
}

async function fetchTestingData() {
  const symbol = 'XAUUSDm';
  return await fetchHistoricalDataImpl(
    symbol,
    '2024-01-01T00:00:00',
    '2025-02-14T00:00:00',
    60
  );
}

/**
 * Configuration object for trendline detection
 */
const TRENDLINE_CONFIG = {
  TOUCH_TOLERANCE: 0.0005, // 0.05% tolerance for price touches
  MIN_SWING_STRENGTH: 0.001, // 0.1% minimum swing for valid pivot
  MIN_TOUCHES: 2, // Minimum number of touches to confirm trendline
  MAX_DEVIATION: 0.002 // Maximum allowed deviation from trendline (0.2%)
};

/**
 * Calculates the strength of a potential pivot point
 * @param {Candle} current - Current candle
 * @param {Candle[]} surrounding - Array of surrounding candles
 * @param {string} trendType - "up" or "down"
 * @returns {number} Strength score of the pivot
 */
function calculatePivotStrength(current, surrounding, trendType) {
  if (!current || !surrounding || surrounding.length < 2) return 0;

  const isLowPivot = trendType === 'up';
  const pivotPrice = isLowPivot ? current.low : current.high;
  let strength = 0;

  for (const candle of surrounding) {
    if (isLowPivot) {
      if (candle.low > pivotPrice) strength++;
    } else {
      if (candle.high < pivotPrice) strength++;
    }
  }

  return strength / surrounding.length;
}

/**
 * Finds significant pivot points that could form trendline anchors
 * @param {Candle[]} candles - Array of candlestick data
 * @param {string} trendType - "up" or "down"
 * @returns {Array<Object>} Array of potential pivot points with their strengths
 */
function findPivotPoints(candles, trendType) {
  const pivots = [];
  const windowSize = 5; // Look at 5 candles before and after

  for (let i = windowSize; i < candles.length - windowSize; i++) {
    const current = candles[i];
    const surrounding = [
      ...candles.slice(i - windowSize, i),
      ...candles.slice(i + 1, i + windowSize + 1)
    ];

    const strength = calculatePivotStrength(current, surrounding, trendType);
    const price = trendType === 'up' ? current.low : current.high;
    const priceChange = Math.abs(price - candles[i - 1].close) / candles[i - 1].close;

    if (strength > 0.6 && priceChange > TRENDLINE_CONFIG.MIN_SWING_STRENGTH) {
      pivots.push({
        index: i,
        price,
        strength,
        time: current.time
      });
    }
  }

  return pivots.sort((a, b) => b.strength - a.strength);
}

/**
 * Enhanced function to find trendline anchors using pivot points
 */
function findTrendLineAnchors(candles, trendType = 'up') {
  if (candles.length < 10) return null;

  const pivots = findPivotPoints(candles, trendType);
  if (pivots.length < 2) return null;

  // Find the best pair of pivot points
  let bestAnchors = null;
  let bestScore = 0;

  for (let i = 0; i < pivots.length - 1; i++) {
    for (let j = i + 1; j < pivots.length; j++) {
      const p1 = pivots[i];
      const p2 = pivots[j];
      
      // Skip if points are too close
      if (p2.index - p1.index < 5) continue;

      const score = p1.strength * p2.strength;
      if (score > bestScore) {
        bestScore = score;
        bestAnchors = {
          index1: p1.index,
          price1: p1.price,
          index2: p2.index,
          price2: p2.price,
          strength: score
        };
      }
    }
  }

  return bestAnchors;
}

/**
 * Checks if a price point touches the trendline within tolerance
 */
function isTrendlineTouch(price, trendLinePrice) {
  const deviation = Math.abs(price - trendLinePrice) / trendLinePrice;
  return deviation <= TRENDLINE_CONFIG.TOUCH_TOLERANCE;
}

/**
 * Enhanced function to detect all trendline breaks with touch tolerance
 */
function detectAllTrendBreaks(candles, trendType = 'up') {
  const touches = [];
  const breaks = [];
  let lastTouchIndex = -1;
  let trendlineStrength = 0;
  const minSwingDistance = 3; // Reduced from 5 to be more lenient
  
  // First pass: identify potential swing points with more lenient criteria
  const swingPoints = [];
  for (let i = 1; i < candles.length - 1; i++) {
    const curr = candles[i];
    const prev = candles[i - 1];
    const next = candles[i + 1];
    
    if (trendType === 'up') {
      // Look for swing lows with more lenient criteria
      if (curr.low <= prev.low && curr.low <= next.low) {
        // Check if it's a significant enough swing
        const recentLows = candles
          .slice(Math.max(0, i - 5), Math.min(candles.length, i + 5))
          .map(c => c.low);
        if (curr.low === Math.min(...recentLows)) {
          swingPoints.push({ 
            index: i, 
            price: curr.low, 
            time: new Date(curr.time).getTime()
          });
        }
      }
    } else {
      // Look for swing highs with more lenient criteria
      if (curr.high >= prev.high && curr.high >= next.high) {
        // Check if it's a significant enough swing
        const recentHighs = candles
          .slice(Math.max(0, i - 5), Math.min(candles.length, i + 5))
          .map(c => c.high);
        if (curr.high === Math.max(...recentHighs)) {
          swingPoints.push({ 
            index: i, 
            price: curr.high, 
            time: new Date(curr.time).getTime()
          });
        }
      }
    }
  }
  
  // Filter swing points for minimum distance
  const filteredSwings = swingPoints.filter((point, index, arr) => {
    if (index === 0) return true;
    return point.index - arr[index - 1].index >= minSwingDistance;
  });
  
  // Second pass: detect touches and breaks using filtered swing points
  for (let i = 2; i < candles.length; i++) {
    const currentCandle = candles[i];
    const previousSwings = filteredSwings.filter(p => p.index < i);
    
    if (previousSwings.length < 2) continue;
    
    // Use last two swing points for trendline
    const point1 = previousSwings[previousSwings.length - 2];
    const point2 = previousSwings[previousSwings.length - 1];
    
    // Calculate trendline price using millisecond timestamps
    const currentTime = new Date(currentCandle.time).getTime();
    const slope = (point2.price - point1.price) / (point2.time - point1.time);
    const trendLinePrice = point2.price + slope * (currentTime - point2.time);
    
    // Dynamic touch tolerance based on volatility and price level
    const atr = calculateATR(candles.slice(Math.max(0, i - 14), i));
    const priceLevel = currentCandle.close;
    const touchTolerance = Math.min(
      atr * 0.15,
      priceLevel * 0.0005 // 0.05% of price level
    );
    
    // Check for touch with improved criteria
    const touchScore = calculateTouchScore(currentCandle, trendLinePrice, trendType, touchTolerance);
    const isTouching = touchScore > 0.7;
    
    if (isTouching) {
      touches.push({
        index: i,
        candle: currentCandle,
        trendLinePrice,
        touchScore,
        trendline: { point1, point2 }
      });
      lastTouchIndex = i;
      trendlineStrength++;
      continue;
    }
    
    // Break detection with confirmation
    const breakScore = calculateBreakScore(
      candles.slice(Math.max(0, i - 3), i + 1),
      trendLinePrice,
      trendType,
      atr
    );
    
    if (breakScore > 0.8 && lastTouchIndex !== -1 && trendlineStrength >= 1) {
      breaks.push({
        breakIndex: i,
        candle: currentCandle,
        priceAtBreak: trendType === 'up' ? currentCandle.low : currentCandle.high,
        trendLinePrice,
        strength: trendlineStrength + 1,
        lastTouchDistance: i - lastTouchIndex,
        trendType,
        breakScore,
        trendline: { point1, point2 }
      });
    }
  }
  
  // Filter breaks based on market conditions
  const validatedBreaks = breaks.map(breakPoint => {
    const validation = validateMarketConditions(breakPoint, candles);
    return {
      ...breakPoint,
      isValid: validation.isValid,
      validationMetrics: validation.metrics,
      invalidationReasons: !validation.isValid ? 
        Object.entries(validation.reasons)
          .filter(([_, isValid]) => !isValid)
          .map(([reason]) => reason) : []
    };
  }).filter(breakPoint => breakPoint.isValid);
  
  return {
    touches,
    breaks: validatedBreaks,
    trendlineStrength: trendlineStrength + 1,
    originalBreakCount: breaks.length,
    filteredBreakCount: validatedBreaks.length,
    swingPoints: filteredSwings
  };
}

/**
 * Detects when a trendline is broken. For backward compatibility, returns the first break found.
 * For an uptrend, a break occurs when a candle's low falls below the trendline;
 * for a downtrend, when a candle's high rises above the trendline.
 *
 * @param {Candle[]} candles - Array of candlestick data
 * @param {string} trendType - "up" or "down"
 * @returns {Object|null} - If a break is detected, returns the first break object:
 *                         { breakIndex, candle, trendLinePrice, message }.
 *                         Otherwise, returns null.
 */
function detectTrendBreak(candles, trendType = 'up') {
  const breaks = detectAllTrendBreaks(candles, trendType);
  return breaks.length > 0 ? breaks[0] : null;
}

/**
 * Calculates the Average True Range (ATR) for volatility-based stop loss
 * @param {Array} candles - Array of candles
 * @param {number} period - Period for ATR calculation
 * @returns {number} ATR value
 */
function calculateATR(candles, period = 14) {
  if (candles.length < period) return 0;

  const trueRanges = candles.map((candle, i) => {
    if (i === 0) return candle.high - candle.low;
    const previousClose = candles[i - 1].close;
    return Math.max(
      candle.high - candle.low,
      Math.abs(candle.high - previousClose),
      Math.abs(candle.low - previousClose)
    );
  });

  const atr = trueRanges.slice(0, period).reduce((sum, tr) => sum + tr, 0) / period;
  return atr;
}

/**
 * Calculates suggested stop loss based on break type and volatility
 * @param {Object} breakPoint - Break point information
 * @param {Array} candles - Array of candles
 * @param {string} trendType - "up" or "down"
 * @returns {number} Suggested stop loss price
 */
function calculateStopLoss(breakPoint, candles, trendType) {
  const recentCandles = candles.slice(Math.max(0, breakPoint.breakIndex - 14), breakPoint.breakIndex + 1);
  const atr = calculateATR(recentCandles);
  
  // Dynamic ATR multiplier based on volatility and trend strength
  const volatility = calculateVolatilityState(recentCandles);
  const baseMultiplier = 1.0;
  let multiplier = baseMultiplier;
  
  // Adjust multiplier based on volatility
  if (volatility.isHighVolatility) {
    multiplier *= 1.5;
  } else if (volatility.isLowVolatility) {
    multiplier *= 0.8;
  }
  
  // Adjust multiplier based on trend strength
  if (breakPoint.strength >= 3) {
    multiplier *= 0.8; // Tighter stop for strong trends
  }
  
  // Calculate stop loss with dynamic multiplier
  if (trendType === 'up') {
    const stopDistance = Math.max(atr * multiplier, breakPoint.priceAtBreak - breakPoint.trendLinePrice);
    return breakPoint.priceAtBreak + stopDistance;
  } else {
    const stopDistance = Math.max(atr * multiplier, breakPoint.trendLinePrice - breakPoint.priceAtBreak);
    return breakPoint.priceAtBreak - stopDistance;
  }
}

/**
 * Calculates multiple take-profit targets based on break type, volatility, and risk-reward ratios
 * @param {Object} breakPoint - Break point information
 * @param {Array} candles - Array of candles
 * @param {string} trendType - "up" or "down"
 * @param {number} stopLoss - Calculated stop loss price
 * @returns {Array<Object>} Array of take-profit targets with prices and ratios
 */
function calculateTakeProfitTargets(breakPoint, candles, trendType, stopLoss) {
  const atr = calculateATR(candles.slice(Math.max(0, breakPoint.breakIndex - 14), breakPoint.breakIndex + 1));
  const riskAmount = Math.abs(stopLoss - breakPoint.priceAtBreak);
  
  // Define risk-reward ratios for multiple targets
  const targets = [
    { ratio: 1.5, percentage: 0.4 },  // First target: 1.5R with 40% of position
    { ratio: 2.5, percentage: 0.3 },  // Second target: 2.5R with 30% of position
    { ratio: 3.5, percentage: 0.3 }   // Third target: 3.5R with 30% of position
  ];

  return targets.map(target => {
    const rewardAmount = riskAmount * target.ratio;
    const price = trendType === 'up'
      ? breakPoint.priceAtBreak - rewardAmount  // For uptrend break (going short)
      : breakPoint.priceAtBreak + rewardAmount; // For downtrend break (going long)

    return {
      price,
      ratio: target.ratio,
      percentage: target.percentage,
      pips: Math.abs(price - breakPoint.priceAtBreak) / atr
    };
  });
}

/**
 * Formats a price with appropriate decimal places and adds color indicators
 * @param {number} price - Price to format
 * @param {number} decimals - Number of decimal places
 * @returns {string} Formatted price string
 */
function formatPrice(price, decimals = 5) {
  return price.toFixed(decimals);
}

/**
 * Creates a styled header for the analysis output
 * @param {string} text - Header text
 * @returns {string} Styled header
 */
function createHeader(text) {
  const line = '═'.repeat(50);
  return `\n╔${line}╗\n║ ${text.padEnd(48)} ║\n╚${line}╝\n`;
}

/**
 * Creates a styled section separator
 * @param {string} text - Section text
 * @returns {string} Styled separator
 */
function createSection(text) {
  return `\n┌─── ${text} ${'─'.repeat(40 - text.length)}┐\n`;
}

/**
 * Analyzes the performance of a trade based on subsequent price action
 * @param {Object} breakPoint - Break point information
 * @param {Array} candles - Array of candles after the break
 * @param {number} stopLoss - Stop loss price
 * @param {Array} takeProfitTargets - Array of take profit targets
 * @param {string} trendType - "up" or "down"
 * @returns {Object} Trade performance metrics
 */
function analyzeTrade(breakPoint, candles, stopLoss, takeProfitTargets, trendType) {
  const subsequentCandles = candles.slice(breakPoint.breakIndex + 1);
  let maxAdverseExcursion = 0;  // Maximum drawdown
  let maxFavorableExcursion = 0; // Maximum profit
  let hitStopLoss = false;
  let targetsHit = new Array(takeProfitTargets.length).fill(false);
  let exitPrice = null;
  let exitIndex = -1;
  
  for (let i = 0; i < subsequentCandles.length; i++) {
    const candle = subsequentCandles[i];
    
    if (trendType === 'up') { // Selling on support break
      // Check for stop loss hit
      if (candle.high >= stopLoss) {
        hitStopLoss = true;
        exitPrice = stopLoss;
        exitIndex = i;
        break;
      }
      
      // Update maximum adverse excursion (drawdown)
      const adverseExcursion = candle.high - breakPoint.priceAtBreak;
      maxAdverseExcursion = Math.max(maxAdverseExcursion, adverseExcursion);
      
      // Update maximum favorable excursion (profit)
      const favorableExcursion = breakPoint.priceAtBreak - candle.low;
      maxFavorableExcursion = Math.max(maxFavorableExcursion, favorableExcursion);
      
      // Check take profit targets
      takeProfitTargets.forEach((target, index) => {
        if (!targetsHit[index] && candle.low <= target.price) {
          targetsHit[index] = true;
          if (!exitPrice) {
            exitPrice = target.price;
            exitIndex = i;
          }
        }
      });
    } else { // Buying on resistance break
      // Check for stop loss hit
      if (candle.low <= stopLoss) {
        hitStopLoss = true;
        exitPrice = stopLoss;
        exitIndex = i;
        break;
      }
      
      // Update maximum adverse excursion (drawdown)
      const adverseExcursion = breakPoint.priceAtBreak - candle.low;
      maxAdverseExcursion = Math.max(maxAdverseExcursion, adverseExcursion);
      
      // Update maximum favorable excursion (profit)
      const favorableExcursion = candle.high - breakPoint.priceAtBreak;
      maxFavorableExcursion = Math.max(maxFavorableExcursion, favorableExcursion);
      
      // Check take profit targets
      takeProfitTargets.forEach((target, index) => {
        if (!targetsHit[index] && candle.high >= target.price) {
          targetsHit[index] = true;
          if (!exitPrice) {
            exitPrice = target.price;
            exitIndex = i;
          }
        }
      });
    }
  }
  
  // Calculate profit/loss and R-multiple
  const riskAmount = Math.abs(stopLoss - breakPoint.priceAtBreak);
  const profitAmount = exitPrice ? Math.abs(exitPrice - breakPoint.priceAtBreak) : maxFavorableExcursion;
  const rMultiple = hitStopLoss ? -1 : (profitAmount / riskAmount);
  
  return {
    success: !hitStopLoss && targetsHit.some(hit => hit),
    rMultiple,
    maxAdverseExcursion,
    maxFavorableExcursion,
    targetsHit,
    exitPrice,
    exitIndex,
    candlesToExit: exitIndex + 1,
    maxDrawdownPercent: (maxAdverseExcursion / breakPoint.priceAtBreak) * 100,
    maxProfitPercent: (maxFavorableExcursion / breakPoint.priceAtBreak) * 100
  };
}

/**
 * Calculates overall performance metrics for all signals
 * @param {Array} trades - Array of analyzed trades
 * @returns {Object} Overall performance metrics
 */
function calculatePerformanceMetrics(trades) {
  const successfulTrades = trades.filter(t => t.success);
  const winRate = (successfulTrades.length / trades.length) * 100;
  const averageRMultiple = trades.reduce((sum, t) => sum + t.rMultiple, 0) / trades.length;
  const profitFactor = Math.abs(
    successfulTrades.reduce((sum, t) => sum + t.rMultiple, 0) /
    trades.filter(t => !t.success).reduce((sum, t) => sum + t.rMultiple, 0)
  );
  
  return {
    totalTrades: trades.length,
    winRate,
    averageRMultiple,
    profitFactor,
    averageCandlesToExit: trades.reduce((sum, t) => sum + t.candlesToExit, 0) / trades.length,
    maxDrawdown: Math.max(...trades.map(t => t.maxDrawdownPercent)),
    bestTrade: Math.max(...trades.map(t => t.rMultiple)),
    worstTrade: Math.min(...trades.map(t => t.rMultiple))
  };
}

/**
 * Calculates market volatility state using ATR
 * @param {Array} candles - Array of candlestick data
 * @param {number} period - Period for ATR calculation
 * @returns {Object} Volatility metrics
 */
function calculateVolatilityState(candles, period = 14) {
  const atr = calculateATR(candles.slice(-period));
  const averagePrice = candles[candles.length - 1].close;
  const volatilityRatio = (atr / averagePrice) * 100;
  
  return {
    atr,
    volatilityRatio,
    isHighVolatility: volatilityRatio > 0.5, // More than 0.5% movement
    isLowVolatility: volatilityRatio < 0.2   // Less than 0.2% movement
  };
}

/**
 * Validates market conditions for a potential trade
 * @param {Object} breakPoint - Break point information
 * @param {Array} candles - Candlestick data
 * @returns {Object} Validation results
 */
function validateMarketConditions(breakPoint, candles) {
  const priorCandles = candles.slice(Math.max(0, breakPoint.breakIndex - 20), breakPoint.breakIndex + 1);
  const volatility = calculateVolatilityState(priorCandles);
  
  // Calculate momentum using RSI
  const closes = priorCandles.map(c => c.close);
  const rsi = calculateRSI(closes);
  
  // Check recent price action
  const recentCandles = priorCandles.slice(-5);
  const breakCandle = breakPoint.candle;
  const prevCandle = candles[breakPoint.breakIndex - 1];
  
  // Calculate trend direction of recent candles
  const recentTrend = recentCandles.reduce((acc, curr, i, arr) => {
    if (i === 0) return 0;
    return acc + (curr.close - arr[i-1].close);
  }, 0);
  
  // Validate momentum alignment with break direction
  const isAlignedMomentum = (breakPoint.trendType === 'up' && recentTrend < 0) || 
                           (breakPoint.trendType === 'down' && recentTrend > 0);
  
  // More sophisticated momentum conditions
  const isStrongMomentum = (breakPoint.trendType === 'up' && rsi < 40 && isAlignedMomentum) || 
                          (breakPoint.trendType === 'down' && rsi > 60 && isAlignedMomentum);
  
  // Improved break candle validation
  const isStrongBreakCandle = Math.abs(breakCandle.close - breakCandle.open) > volatility.atr * 0.3 &&
                             (breakPoint.trendType === 'up' ? 
                               breakCandle.close < breakCandle.open : // Red candle for sell
                               breakCandle.close > breakCandle.open); // Green candle for buy
  
  // Check if previous candle confirms the break
  const isPrevCandleConfirming = breakPoint.trendType === 'up' ? 
    prevCandle.close < prevCandle.open : // Previous candle should be bearish for sell
    prevCandle.close > prevCandle.open;  // Previous candle should be bullish for buy
  
  // Price deviation check
  const priceDeviation = Math.abs(breakCandle.close - breakPoint.trendLinePrice) / volatility.atr;
  const isOverextended = priceDeviation > 2.5;
  
  // Volume validation (if available)
  const hasIncreasingVolume = breakCandle.volume > prevCandle.volume;
  
  // Minimum requirements
  const hasMinimumStrength = breakPoint.strength >= 2;
  const hasRecentTouch = breakPoint.lastTouchDistance <= 10; // Max 10 candles since last touch
  
  return {
    isValid: (isStrongMomentum || (isStrongBreakCandle && isPrevCandleConfirming)) &&
             !isOverextended &&
             hasMinimumStrength &&
             hasRecentTouch &&
             !volatility.isLowVolatility,
    reasons: {
      momentum: isStrongMomentum,
      breakCandle: isStrongBreakCandle,
      prevCandle: isPrevCandleConfirming,
      priceDeviation: !isOverextended,
      strength: hasMinimumStrength,
      recentTouch: hasRecentTouch,
      volatility: !volatility.isLowVolatility
    },
    metrics: {
      rsi,
      recentTrend,
      volatilityRatio: volatility.volatilityRatio,
      priceDeviation,
      candleStrength: Math.abs(breakCandle.close - breakCandle.open) / volatility.atr,
      volumeIncrease: hasIncreasingVolume,
      strength: breakPoint.strength,
      touchDistance: breakPoint.lastTouchDistance
    }
  };
}

/**
 * Finds points for trendline calculation with improved swing point detection
 * @param {Array} candles - Candlestick data
 * @param {string} trendType - "up" or "down"
 * @returns {Array} Array of points for trendline
 */
function findTrendlinePoints(candles, trendType) {
  const points = [];
  let currentExtreme = trendType === 'up' ? -Infinity : Infinity;
  let lastPointIndex = -1;
  const lookback = 3; // Look back period for swing point confirmation
  
  for (let i = lookback; i < candles.length - lookback; i++) {
    const candle = candles[i];
    const price = trendType === 'up' ? candle.low : candle.high;
    
    // Check if this is a swing point
    let isSwingPoint = true;
    for (let j = 1; j <= lookback; j++) {
      const prevPrice = trendType === 'up' ? 
        candles[i - j].low : 
        candles[i - j].high;
      const nextPrice = trendType === 'up' ? 
        candles[i + j].low : 
        candles[i + j].high;
        
      if (trendType === 'up') {
        if (price > prevPrice || price > nextPrice) {
          isSwingPoint = false;
          break;
        }
      } else {
        if (price < prevPrice || price < nextPrice) {
          isSwingPoint = false;
          break;
        }
      }
    }
    
    if (!isSwingPoint) continue;
    
    // Ensure minimum distance between points
    if (lastPointIndex !== -1 && i - lastPointIndex < 5) continue;
    
    points.push({
      time: candle.time,
      price: price,
      index: i
    });
    lastPointIndex = i;
    currentExtreme = price;
  }
  
  return points;
}

/**
 * Calculates the trendline price at a given time
 * @param {Array} candles - Previous candlestick data
 * @param {number} targetTime - Time to calculate trendline price for
 * @param {string} trendType - "up" or "down"
 * @returns {number|null} Trendline price or null if not enough points
 */
function calculateTrendlinePrice(candles, targetTime, trendType) {
  const trendlinePoints = findTrendlinePoints(candles, trendType);
  if (trendlinePoints.length < 2) return null;
  
  // Use the last two points to calculate the trendline
  const point1 = trendlinePoints[trendlinePoints.length - 2];
  const point2 = trendlinePoints[trendlinePoints.length - 1];
  
  // Calculate slope and intercept
  const slope = (point2.price - point1.price) / (point2.time - point1.time);
  const intercept = point1.price - slope * point1.time;
  
  // Calculate price at target time
  return slope * targetTime + intercept;
}

/**
 * Checks if a price point touches the trendline
 * @param {Object} candle - Candlestick data
 * @param {number} trendLinePrice - Price of trendline at candle's time
 * @param {string} trendType - "up" or "down"
 * @param {number} tolerance - Tolerance for touch detection
 * @returns {boolean} True if candle touches trendline
 */
function checkTrendlineTouch(candle, trendLinePrice, trendType, tolerance) {
  if (trendType === 'up') {
    return Math.abs(candle.low - trendLinePrice) <= tolerance;
  } else {
    return Math.abs(candle.high - trendLinePrice) <= tolerance;
  }
}

/**
 * Checks if a candle breaks the trendline
 * @param {Object} candle - Candlestick data
 * @param {number} trendLinePrice - Price of trendline at candle's time
 * @param {string} trendType - "up" or "down"
 * @returns {boolean} True if candle breaks trendline
 */
function checkTrendlineBreak(candle, trendLinePrice, trendType) {
  if (trendType === 'up') {
    return candle.low < trendLinePrice;
  } else {
    return candle.high > trendLinePrice;
  }
}

/**
 * Calculates RSI (Relative Strength Index)
 * @param {Array} prices - Array of closing prices
 * @param {number} period - RSI period
 * @returns {number} RSI value
 */
function calculateRSI(prices, period = 14) {
  if (prices.length < period + 1) {
    return 50; // Return neutral RSI if not enough data
  }

  let gains = 0;
  let losses = 0;
  
  // Calculate initial average gain/loss
  for (let i = 1; i < period + 1; i++) {
    const difference = prices[i] - prices[i - 1];
    if (difference >= 0) {
      gains += difference;
    } else {
      losses -= difference;
    }
  }
  
  gains /= period;
  losses /= period;
  
  // Avoid division by zero
  if (losses === 0) {
    return 100;
  }
  
  // Calculate subsequent values using smoothing
  for (let i = period + 1; i < prices.length; i++) {
    const difference = prices[i] - prices[i - 1];
    if (difference >= 0) {
      gains = (gains * (period - 1) + difference) / period;
      losses = (losses * (period - 1)) / period;
    } else {
      gains = (gains * (period - 1)) / period;
      losses = (losses * (period - 1) - difference) / period;
    }
  }
  
  // Avoid division by zero
  if (losses === 0) {
    return 100;
  }
  
  const rs = gains / losses;
  return 100 - (100 / (1 + rs));
}

/**
 * Calculate touch score based on multiple factors
 * @param {Object} candle - Current candle
 * @param {number} trendLinePrice - Price of trendline at current point
 * @param {string} trendType - 'up' or 'down'
 * @param {number} tolerance - Allowed deviation from trendline
 * @returns {number} Score between 0 and 1
 */
function calculateTouchScore(candle, trendLinePrice, trendType, tolerance) {
  const price = trendType === 'up' ? candle.low : candle.high;
  const deviation = Math.abs(price - trendLinePrice);
  
  // If deviation is too large, no touch
  if (deviation > tolerance * 2) return 0;
  
  // Base score from how close price is to trendline
  let score = 1 - (deviation / tolerance);
  
  // Bonus for strong rejection from trendline
  const wickSize = trendType === 'up' ? 
    (candle.close - candle.low) : 
    (candle.high - candle.close);
  
  if (wickSize > tolerance) {
    score += 0.2;
  }
  
  // Bonus for momentum in right direction
  const candleDirection = candle.close - candle.open;
  if ((trendType === 'up' && candleDirection > 0) || 
      (trendType === 'down' && candleDirection < 0)) {
    score += 0.1;
  }
  
  return Math.min(score, 1);
}

/**
 * Calculate break score based on multiple factors
 * @param {Array} candles - Recent candles including current
 * @param {number} trendLinePrice - Price of trendline at current point
 * @param {string} trendType - 'up' or 'down'
 * @param {number} atr - Average True Range
 * @returns {number} Score between 0 and 1
 */
function calculateBreakScore(candles, trendLinePrice, trendType, atr) {
  const currentCandle = candles[candles.length - 1];
  const prevCandle = candles[candles.length - 2];
  
  let score = 0;
  
  // Check break size relative to ATR
  const breakSize = trendType === 'up' ?
    trendLinePrice - currentCandle.low :
    currentCandle.high - trendLinePrice;
  
  score += Math.min(breakSize / atr, 1) * 0.4;
  
  // Check candle size and direction
  const candleSize = Math.abs(currentCandle.close - currentCandle.open);
  const isBreakingCandle = trendType === 'up' ?
    currentCandle.close < currentCandle.open :
    currentCandle.close > currentCandle.open;
  
  if (isBreakingCandle) {
    score += 0.3;
  }
  score += Math.min(candleSize / atr, 1) * 0.3;
  
  // Check previous candle
  const isPrevBreaking = trendType === 'up' ?
    prevCandle.low < trendLinePrice :
    prevCandle.high > trendLinePrice;
  
  if (isPrevBreaking) {
    score += 0.2;
  }
  
  // Bonus for strong momentum
  const bodySize = Math.abs(currentCandle.close - currentCandle.open);
  if (bodySize > candleSize * 0.7) { // Strong body
    score += 0.1;
  }
  
  return Math.min(score, 1);
}

async function main() {
  try {
    const data = await fetchTestingData();
    
    // Detect breaks and touches for both trend types
    const upTrendAnalysis = detectAllTrendBreaks(data, 'up');
    const downTrendAnalysis = detectAllTrendBreaks(data, 'down');
    
    // Store all trade analyses
    const upTrendTrades = [];
    const downTrendTrades = [];
    
    // Print analysis summary
    console.log(createHeader('TRENDLINE BREAK ANALYSIS'));
    
    console.log('SUMMARY');
    console.log('───────');
    console.log(`Uptrend Breaks: ${upTrendAnalysis.breaks.length} | Touches: ${upTrendAnalysis.touches.length} | Strength: ${upTrendAnalysis.trendlineStrength.toFixed(4)}`);
    console.log(`Downtrend Breaks: ${downTrendAnalysis.breaks.length} | Touches: ${downTrendAnalysis.touches.length} | Strength: ${downTrendAnalysis.trendlineStrength.toFixed(4)}\n`);
    
    if (upTrendAnalysis.breaks.length > 0) {
      console.log(createHeader('SUPPORT BREAKS (SELL OPPORTUNITIES)'));
      upTrendAnalysis.breaks
        .sort((a, b) => b.strength - a.strength)
        .slice(0, 3)
        .forEach((breakPoint, index) => {
          const stopLoss = calculateStopLoss(breakPoint, data, 'up');
          const takeProfitTargets = calculateTakeProfitTargets(breakPoint, data, 'up', stopLoss);
          
          // Analyze trade performance
          const tradeAnalysis = analyzeTrade(breakPoint, data, stopLoss, takeProfitTargets, 'up');
          upTrendTrades.push(tradeAnalysis);
          
          console.log(createSection(`BREAK #${index + 1} - HIGH PRIORITY`));
          console.log(`📊 TRADE SETUP - SELL ${data[0].symbol}`);
          console.log(`⏰ Time: ${new Date(breakPoint.candle.time).toISOString()}`);
          console.log(`\n📈 ENTRY ANALYSIS`);
          console.log(`   Entry Price: ${formatPrice(breakPoint.priceAtBreak)} (Break Price)`);
          console.log(`   Trendline:   ${formatPrice(breakPoint.trendLinePrice)}`);
          console.log(`   Strength:    ${breakPoint.strength} touches`);
          console.log(`   Validity:    ${breakPoint.lastTouchDistance} candles since last touch`);
          
          console.log(`\n🎯 TRADE TARGETS`);
          console.log(`   Stop Loss:   ${formatPrice(stopLoss)} (${((stopLoss - breakPoint.priceAtBreak) / breakPoint.priceAtBreak * 100).toFixed(2)}%)`);
          takeProfitTargets.forEach((target, i) => {
            const hitStatus = tradeAnalysis.targetsHit[i] ? '✅' : '⏳';
            console.log(`   Target ${i + 1}:    ${formatPrice(target.price)} (${target.ratio}R | ${(target.percentage * 100)}% size | ${target.pips.toFixed(1)} ATR) ${hitStatus}`);
          });
          
          console.log(`\n� PERFORMANCE ANALYSIS`);
          console.log(`   Outcome:     ${tradeAnalysis.success ? '✅ Profitable' : '❌ Loss'}`);
          console.log(`   R-Multiple:  ${tradeAnalysis.rMultiple.toFixed(2)}R`);
          console.log(`   Max Drawdown: ${tradeAnalysis.maxDrawdownPercent.toFixed(2)}%`);
          console.log(`   Exit After:  ${tradeAnalysis.candlesToExit} candles`);
          
          console.log(`\n�📝 TRADE INSTRUCTIONS`);
          console.log(`   1. Place SELL order at market or ${formatPrice(breakPoint.priceAtBreak)}`);
          console.log(`   2. Set stop loss at ${formatPrice(stopLoss)}`);
          console.log(`   3. Set multiple take profits as specified above`);
          console.log(`   4. Monitor price action at each target for potential exit\n`);
          console.log('─'.repeat(50));
        });
    }
    
    // Similar updates for downtrend analysis...
    
    // Print overall performance metrics
    if (upTrendTrades.length > 0 || downTrendTrades.length > 0) {
      console.log(createHeader('OVERALL PERFORMANCE METRICS'));
      
      if (upTrendTrades.length > 0) {
        const upTrendMetrics = calculatePerformanceMetrics(upTrendTrades);
        console.log('\nSUPPORT BREAK (SELL) PERFORMANCE:');
        console.log(`Win Rate:      ${upTrendMetrics.winRate.toFixed(2)}%`);
        console.log(`Avg R-Multiple: ${upTrendMetrics.averageRMultiple.toFixed(2)}`);
        console.log(`Profit Factor:  ${upTrendMetrics.profitFactor.toFixed(2)}`);
        console.log(`Max Drawdown:   ${upTrendMetrics.maxDrawdown.toFixed(2)}%`);
        console.log(`Best Trade:     ${upTrendMetrics.bestTrade.toFixed(2)}R`);
        console.log(`Worst Trade:    ${upTrendMetrics.worstTrade.toFixed(2)}R`);
      }
      
      if (downTrendTrades.length > 0) {
        const downTrendMetrics = calculatePerformanceMetrics(downTrendTrades);
        console.log('\nRESISTANCE BREAK (BUY) PERFORMANCE:');
        console.log(`Win Rate:      ${downTrendMetrics.winRate.toFixed(2)}%`);
        console.log(`Avg R-Multiple: ${downTrendMetrics.averageRMultiple.toFixed(2)}`);
        console.log(`Profit Factor:  ${downTrendMetrics.profitFactor.toFixed(2)}`);
        console.log(`Max Drawdown:   ${downTrendMetrics.maxDrawdown.toFixed(2)}%`);
        console.log(`Best Trade:     ${downTrendMetrics.bestTrade.toFixed(2)}R`);
        console.log(`Worst Trade:    ${downTrendMetrics.worstTrade.toFixed(2)}R`);
      }
    }
    
    return {
      upTrend: upTrendAnalysis,
      downTrend: downTrendAnalysis,
      performance: {
        upTrend: upTrendTrades,
        downTrend: downTrendTrades
      }
    };
  } catch (error) {
    console.error('Error in trend break analysis:', error);
    throw error;
  }
}

main();