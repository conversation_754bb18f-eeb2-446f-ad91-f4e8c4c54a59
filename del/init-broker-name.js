const {
  saveUserInformation,
  lookupUserById,
  lookupUserByEmail,
  findUserByUUID,
  saveTradeBrokerAccount,
  getTradeBrokerAccountById,
  getTradeBrokerAccountByLogin,
  getTradeBrokerAccountsByUserId,
  removeBrokerTradingAccount,
  saveTradeAccountSymbol,
  getTradeAccountSymbolByUserIdAndBrokerAccountId,
  saveTradeUserAccountSymbolDefault,
  getTradeUserAccountSymbolDefaultByUserIdAndBrokerAccountIdAndSymbol,
  saveTradeBot,
  getTradeBotByUserIdAndBrokerAccountIdAndSymbol,
  getTradeBotByUserIdAndBrokerAccountId,
  getTradeBotByUserId,
  findBotById
} = require('../supabaseService');




app.post('/openHedgedOrder', async (req, res) => {
  const { orderModel, id } = req.body;
  console.log('order:', orderModel);
  console.log('id:', id);
  try {
    let finalOrder = getHedgedOrder(orderModel);
    console.log('Sending hedged order:', finalOrder);
    try {
      await _sendOrderImpl(finalOrder, id);
    } catch (error) {
      console.log('Error sending order:', error?.message);
    }
    res.json({ message: 'Orders placed successfully' });
  } catch (error) {
    res.status(500).send('Error opening trades');
  }
});