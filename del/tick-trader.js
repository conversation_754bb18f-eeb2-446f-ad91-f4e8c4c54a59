const { connectToMT5, fetchTicks } = require('../mt5-server');

class TickScalper {
  constructor(config = {}) {
    this.config = {
      symbol: config.symbol || 'XAUUSDm',
      profitPips: config.profitPips || 1.0,    // Very small profit target
      stopLossPips: config.stopLossPips || 2.0, // Wider stop loss for safety
      maxSpreadPips: config.maxSpreadPips || 0.5,
      minTickVolume: config.minTickVolume || 1,
      trendPeriod: config.trendPeriod || 100,   // Number of ticks to determine trend
      volumeThreshold: config.volumeThreshold || 5,
      cooldownPeriod: config.cooldownPeriod || 50, // Ticks to wait after trade
      ...config
    };
    
    this.state = {
      ticks: [],
      lastTradeTime: 0,
      position: null,
      cooldownTicks: 0,
      consecutiveWins: 0,
      consecutiveLosses: 0
    };
  }
  
  /**
   * Initialize the trading system
   */
  async init() {
    try {
      await connectToMT5();
      console.log('Connected to MT5');
      this.startTrading();
    } catch (error) {
      console.error('Failed to initialize:', error);
    }
  }


  
  /**
   * Start the trading loop
   */
  async startTrading() {
    console.log('Starting tick trading system...');
    
    // Subscribe to tick data
    const tickStream = await fetchTicks(this.config.symbol);
    
    tickStream.on('data', (tick) => {
      this.processTick(tick);
    });
    
    tickStream.on('error', (error) => {
      console.error('Tick stream error:', error);
    });
  }
  
  /**
   * Process each incoming tick
   */
  processTick(tick) {
    // Add tick to history
    this.state.ticks.push(tick);
    if (this.state.ticks.length > this.config.trendPeriod) {
      this.state.ticks.shift();
    }
    
    // Update cooldown
    if (this.state.cooldownTicks > 0) {
      this.state.cooldownTicks--;
      return;
    }
    
    // Check if we have enough ticks
    if (this.state.ticks.length < this.config.trendPeriod) {
      return;
    }
    
    // Check for trade opportunities
    this.checkForTrades(tick);
    
    // Manage open positions
    if (this.state.position) {
      this.managePosition(tick);
    }
  }


  /**
   * Check for new trade opportunities
   */
  checkForTrades(currentTick) {
    if (this.state.position || this.state.cooldownTicks > 0) {
      return;
    }
    
    // Calculate current spread
    const spread = currentTick.ask - currentTick.bid;
    const spreadPips = spread * 10000;
    
    if (spreadPips > this.config.maxSpreadPips) {
      return;
    }
    
    // Calculate micro trend
    const recentTicks = this.state.ticks.slice(-20);
    const priceChanges = recentTicks.map(t => t.ask - t.bid);
    const averageChange = priceChanges.reduce((a, b) => a + b, 0) / priceChanges.length;
    
    // Calculate volume pressure
    const recentVolume = recentTicks.map(t => t.volume);
    const averageVolume = recentVolume.reduce((a, b) => a + b, 0) / recentVolume.length;
    
    // Entry conditions
    const highVolume = currentTick.volume > averageVolume * 2;
    const lowSpread = spreadPips < this.config.maxSpreadPips / 2;
    
    if (highVolume && lowSpread) {
      if (averageChange > 0) {
        this.openPosition('buy', currentTick);
      } else if (averageChange < 0) {
        this.openPosition('sell', currentTick);
      }
    }
  }
  
  /**
   * Open a new position
   */
  openPosition(type, tick) {
    const entryPrice = type === 'buy' ? tick.ask : tick.bid;
    const stopLoss = type === 'buy' ? 
      entryPrice - (this.config.stopLossPips * 0.0001) :
      entryPrice + (this.config.stopLossPips * 0.0001);
    const takeProfit = type === 'buy' ?
      entryPrice + (this.config.profitPips * 0.0001) :
      entryPrice - (this.config.profitPips * 0.0001);
    
    this.state.position = {
      type,
      entryPrice,
      stopLoss,
      takeProfit,
      entryTime: tick.time,
      volume: 0.01 // Micro lot
    };
    
    console.log(`Opening ${type} position:`, this.state.position);
    // Here you would add actual order placement logic
  }
  
  /**
   * Manage open position
   */
  managePosition(tick) {
    const { position } = this.state;
    const currentPrice = position.type === 'buy' ? tick.bid : tick.ask;
    
    // Check for take profit
    if ((position.type === 'buy' && currentPrice >= position.takeProfit) ||
        (position.type === 'sell' && currentPrice <= position.takeProfit)) {
      this.closePosition('profit', tick);
      return;
    }
    
    // Check for stop loss
    if ((position.type === 'buy' && currentPrice <= position.stopLoss) ||
        (position.type === 'sell' && currentPrice >= position.stopLoss)) {
      this.closePosition('loss', tick);
      return;
    }
  }
  
  /**
   * Close current position
   */
  closePosition(reason, tick) {
    const { position } = this.state;
    const closePrice = position.type === 'buy' ? tick.bid : tick.ask;
    const profit = position.type === 'buy' ?
      (closePrice - position.entryPrice) * 10000 :
      (position.entryPrice - closePrice) * 10000;
    
    console.log(`Closing position (${reason}):`, {
      type: position.type,
      profit: profit.toFixed(1) + ' pips',
      duration: tick.time - position.entryTime + 'ms'
    });
    
    // Update stats
    if (profit > 0) {
      this.state.consecutiveWins++;
      this.state.consecutiveLosses = 0;
    } else {
      this.state.consecutiveLosses++;
      this.state.consecutiveWins = 0;
    }
    
    // Reset state
    this.state.position = null;
    this.state.cooldownTicks = this.config.cooldownPeriod;
    
    // Adjust trading parameters based on performance
    this.adaptParameters();
  }
  
  /**
   * Adapt trading parameters based on performance
   */
  adaptParameters() {
    // Increase caution after consecutive losses
    if (this.state.consecutiveLosses >= 3) {
      this.config.profitPips *= 0.8; // Reduce profit target
      this.config.stopLossPips *= 1.2; // Increase stop loss
      this.config.cooldownPeriod *= 1.5; // Increase waiting time
    }
    
    // Become more aggressive after consecutive wins
    if (this.state.consecutiveWins >= 5) {
      this.config.profitPips *= 1.2; // Increase profit target
      this.config.stopLossPips *= 0.8; // Decrease stop loss
      this.config.cooldownPeriod = Math.max(
        20,
        Math.floor(this.config.cooldownPeriod * 0.8)
      ); // Decrease waiting time
    }
  }
}

// Create and start the trading system
const trader = new TickScalper({
  symbol: 'XAUUSDm',
  profitPips: 0.5,      // Very small profit target
  stopLossPips: 1.0,    // Tight stop loss
  maxSpreadPips: 0.3,   // Only trade on tight spreads
  minTickVolume: 2,     // Minimum tick volume
  trendPeriod: 50,      // Short trend period
  cooldownPeriod: 30    // Short cooldown between trades
});

trader.init().catch(console.error);