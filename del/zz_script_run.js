const supabase = require('../lib/supabaseClient');
const { findIndicatorValuesBySymbol } = require('../lib/supabaseService');

const symbol_indicator_values = {};


const main = async () => {  

  let { data: indicator_values, error } = await supabase
    .from('indicator_values')
    .select('*');
        

  for(const indicator_value of indicator_values) {
    if(!symbol_indicator_values[indicator_value.symbol]) {
      symbol_indicator_values[indicator_value.symbol] = indicator_value;
    }
  }

  const keys = Object.keys(symbol_indicator_values);
  const results = [];

  for(const key of keys) {
    results.push({
      symbol: key,
      tick_value: symbol_indicator_values[key].tick_value,
      tick_size: symbol_indicator_values[key].tick_size
    });
  }

  console.log(results);

  for(const result of results) {
      let { data: trade_symbol_amount, error: trade_symbol_amount_error } =
        await supabase.from('trade_symbol_amount').select('*').eq('symbol', result.symbol);

      if(true || trade_symbol_amount.length === 0) {
        console.log('trade result is empty ', trade_symbol_amount, 'inserting result ', result);
        await supabase.from('trade_symbol_amount').upsert(result);
      }
  }


}

main();